name: "v1_feeds_rendering_engine"
description: "A description of your project"
version: 0.0.135
homepage: https://github.com/discovery-ltd/v1-feeds-rendering-engine-flutter
publish_to: none
environment:
  sdk: ">=2.17.0 <3.0.0"
  flutter: ">=2.17.0"
isar_version: &isar_version 3.1.0
dependencies:
  flutter:
    sdk: flutter
  json_annotation: ^4.4.0
  flutter_modular: ^5.0.3
  mockito: ^5.4.0
  intl: ^0.18.1
  http: ^1.0.0
  jinja: ^0.5.0
  isar: *isar_version
  isar_flutter_libs: *isar_version
  get_it: ^7.2.0
  path_provider: ^2.0.2
  collection: ^1.15.0
  path_provider_platform_interface: ^2.1.2
  feeds_card_wrapper:
    git:
      url: **************:discovery-ltd/v1-feeds-card-wrapper-flutter.git
      path: feeds_card_wrapper
      ref: 0.0.2
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      ref: 0.0.14
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.316
  v1_feeds_rendering_api_module_flutter:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-api-module-flutter.git
      path: v1_feeds_rendering_api_module_flutter
      ref: 0.0.13
  v1_feeds_card_builder_promotions:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builder-promotions-flutter.git
      path: v1_feeds_card_builder_promotions
      ref: 0.0.22
  v1_feeds_card_builder_ctas:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builder-ctas-flutter.git
      path: v1_feeds_card_builder_ctas
      ref: 0.0.32
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.134
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.148
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.39
dependency_overrides:
  http: ^0.13.5
  intl: ^0.18.1
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.19
      path: vg_framework_contracts
  flutter_secure_storage: ^8.0.0
  v1_font_awesome_icons_flutter:
    git:
      url: **************:discovery-ltd/v1-font-awesome-icons-flutter.git
      path: v1_font_awesome_icons_flutter
      version: 0.0.8
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      ref: 0.0.14
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.58
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.148
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.552
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.316
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.21
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.467
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.11
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.10
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.39
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.19
dev_dependencies:
  isar_generator: *isar_version
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
