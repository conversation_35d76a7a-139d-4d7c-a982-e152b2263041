import 'package:framework_contracts_flutter/feeds/get_feeds_cards_response_model.dart';
import 'package:framework_contracts_flutter/storage/storage_contract.dart';
import 'package:storage_management/storage_management.dart';

class FeedsDataRepository {

  StorageManagementProvider storageManagementProvider = StorageManagementProvider.instance;

  Future<void> processFeedsFromResponse(GetFeedsCardsResponseModel feedsResponse, List<String> namesOrContextNames, String locale) async {

    List<FeedOutModel> invalidFeeds = [];
    List<String> responseNamesOrContextNames = feedsResponse.feedOuts.map((feed) => feed.name).toList();

    // Retrieve all feeds from cache
    List<FeedOutModel> cachedFeeds = await getFeeds(namesOrContextNames, locale);

    // Identify feeds in cache but not in response
    List<FeedOutModel> feedsToDelete = cachedFeeds.where((cachedFeed) {
      return !responseNamesOrContextNames.contains(cachedFeed.name);
    }).toList();

    // Delete feeds that are in cache but not in response
    if (feedsToDelete.isNotEmpty) {
      await deleteInvalidFeeds(feedsToDelete);
    }

    /// Re-iterate to each Feed Out to render the feed section
    for (FeedOutModel feed in feedsResponse.feedOuts) {

      List<FeedCardOutModel> feedCardOutModelList = [];

      // validate if feed is still valid otherwise delete in cache
      await validateCacheFeedFromResponse(
          feed.feedAttributeses?.isEmpty == true,
          feed.feedCardOuts.isEmpty,
          feed.name,
          feed.id,
          invalidFeeds
      );

      if(feed.feedCardOuts.isNotEmpty) {
        feedCardOutModelList = await processFeedCards(feed, locale);
      }

      await processAndCacheFeed(feed, feedCardOutModelList, locale);
    }

    // delete invalid feeds in cache
    if(invalidFeeds.isNotEmpty) {
      await deleteInvalidFeeds(invalidFeeds);
    }

  }

  Future<List<FeedCardOutModel>> processFeedCards(FeedOutModel feed, String locale) async {

    List<FeedCardOutModel> backendFeedCards = feed.feedCardOuts; // backend feedCards
    List<FeedCardOutModel> processedFeedCards = [];

    for (var feedCard in backendFeedCards) {
      FeedCardOutModel? cacheFeedCard = await getFeedCardsById(feed.name, feedCard.id, locale);
      if(cacheFeedCard == null) {
        processedFeedCards.add(feedCard);
      } else {
        if(await isOnlyIdPresent(feedCard)) {
          processedFeedCards.add(cacheFeedCard);
        } else {
          processedFeedCards.add(feedCard);
        }
      }
    }

    return processedFeedCards;
  }

  Future<void> processAndCacheFeed(FeedOutModel feed, List<FeedCardOutModel> feedCardOutModelList, String locale) async {

    FeedOutModel? feedFromCache = await getFeedOutsModelByIdAndLocale(feed.id, locale);

    FeedOutModel feedsModel = FeedOutModel();
    feedsModel.feedCardOuts = feedCardOutModelList;
    feedsModel.feedAttributeses = feed.feedAttributeses;
    feedsModel.contextName = feed.contextName;
    feedsModel.effectiveFrom = feed.effectiveFrom;
    feedsModel.effectiveTo = feed.effectiveTo;
    feedsModel.hashCode = feed.hashCode;
    feedsModel.id = feed.id;
    feedsModel.name = feed.name;
    feedsModel.typeKey = feed.typeKey;
    feedsModel.locale = locale;

    if(feedFromCache != null) {
      feedsModel.isarId = feedFromCache.isarId;
      await updateFeedOutsModel(feedsModel);
    } else {
      await saveFeedOutsModel(feedsModel);
    }
  }

  Future<bool> isOnlyIdPresent(FeedCardOutModel feedCard) async {
    return feedCard.feedItemHashCode.isEmpty == true &&
        feedCard.effectiveTo.isEmpty == true &&
        feedCard.effectiveFrom.isEmpty == true &&
        feedCard.name.isEmpty == true &&
        feedCard.applicabilityRule.isEmpty == true &&
        feedCard.rankingRule?.isEmpty == true &&
        (feedCard.feedCardTexts.isEmpty) &&
        (feedCard.feedCardImages.isEmpty) &&
        (feedCard.feedCardAttributeOuts.isEmpty) &&
        (feedCard.feedItemAttributes.isEmpty) &&
        (feedCard.feedCardIcons.isEmpty);
  }

  Future<FeedCardOutModel?> getFeedCardsById(String name, int id, String locale) async {
    FeedOutModel? feedModel = await getFeedOutsModelByName(name, locale);
    if(feedModel != null) {
      if (feedModel.feedCardOuts.isNotEmpty) {
        for (var feedCard in feedModel.feedCardOuts) {
          if (feedCard.id == id) {
            return feedCard;
          }
        }
      }
    }
    return null;
  }

  Future<void> saveFeedOutsModel(FeedOutModel model) async {
    await storageManagementProvider.getStorage().add<FeedOutModel>(model);
  }

  Future<void> updateFeedOutsModel(FeedOutModel model) async {
    await storageManagementProvider.getStorage().update<FeedOutModel>(model);
  }

  Future<FeedOutModel?> getFeedOutsModelByName(String name, String locale) async {
    return await storageManagementProvider.getStorage().getData<FeedOutModel>(
        groupType: VGFilterGroupType.and,
        filterQueries: [
          VGFilterQuery(
              property: "name",
              value: name,
              conditionType: VGFilterConditionType.equalTo
          ),
          VGFilterQuery(
              property: "locale",
              value: locale,
              conditionType: VGFilterConditionType.equalTo
          )
        ]
    );
  }

  Future<FeedOutModel?> getFeedOutsModelByNameAndId(String name, int id) async {
    return await storageManagementProvider.getStorage().getData<FeedOutModel>(
        groupType: VGFilterGroupType.and,
        filterQueries: [
          VGFilterQuery(
              property: "name",
              value: name,
              conditionType: VGFilterConditionType.equalTo
          ),
          VGFilterQuery(
              property: "id",
              value: id,
              conditionType: VGFilterConditionType.equalTo
          )
        ]
    );
  }

  Future<void> validateCacheFeedFromResponse(bool isFeedAttributesEmpty, bool isFeedCardsEmpty, String feedName, int feedId, List<FeedOutModel> invalidFeeds) async {
    FeedOutModel? feed = await getFeedOutsModelByNameAndId(feedName, feedId);
    if(feed != null) {
      if(isFeedAttributesEmpty || isFeedCardsEmpty) {
        invalidFeeds.add(feed);
      }
    }
  }

  Future<void> deleteInvalidFeeds(List<FeedOutModel> invalidFeeds) async {
    await storageManagementProvider.getStorage().deleteMultiple<FeedOutModel>(invalidFeeds);
  }

  Future<List<FeedCardOutModel>> getFeedCardsByNameOrContextName(List<String> namesOrContextNames, String locale) async {
    List<FeedOutModel> feeds = [];
    for (String nameOrContextName in namesOrContextNames) {
      feeds.addAll(await getFeedsByNameOrContext(nameOrContextName, locale));
    }

    // Collecting all FeedCardOutModels from the matching FeedOutModels
    List<FeedCardOutModel> feedCards = [];
    for (FeedOutModel feed in feeds) {
      feedCards.addAll(feed.feedCardOuts);
    }

    return feedCards;
  }

  Future<List<FeedOutModel>> getFeedsByNameOrContext(String name, String locale) async {

    final feedsByName = await storageManagementProvider.getStorage().getDataList<FeedOutModel>(
        groupType: VGFilterGroupType.or,
        filterQueries: [
          VGFilterQuery(
              property: "name",
              value: name,
              conditionType: VGFilterConditionType.equalTo
          ),
          VGFilterQuery(
              property: "contextName",
              value: name,
              conditionType: VGFilterConditionType.equalTo
          )
        ]
    );

    return feedsByName.where((feed) => feed.locale == locale).toList();
  }

  Future<List<FeedOutModel>> getFeeds(List<String> namesOrContextNames, String locale) async {
    List<FeedOutModel> feeds = [];
    for (String nameOrContextName in namesOrContextNames) {
      feeds.addAll(await getFeedsByNameOrContext(nameOrContextName, locale));
    }
    return feeds;
  }

  Future<FeedOutModel?> getFeedOutsModelByIdAndLocale(int id, String locale) async {
    return await storageManagementProvider.getStorage().getData<FeedOutModel>(
      groupType: VGFilterGroupType.and,
      filterQueries: [
        VGFilterQuery(
          property: "id",
          value: id,
          conditionType: VGFilterConditionType.equalTo,
        ),
        VGFilterQuery(
          property: "locale",
          value: locale,
          conditionType: VGFilterConditionType.equalTo,
        ),
      ],
    );
  }

}