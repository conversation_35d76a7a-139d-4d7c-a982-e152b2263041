name: test_app
description: "Test app for V1 Feeds Rendering Engine implementation"
publish_to: 'none'
version: 0.0.135
environment:
  sdk: ">=2.17.0 <3.0.0"
dependencies:
  flutter:
    sdk: flutter
  injectable: ^2.3.1
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.0
  cupertino_icons: ^1.0.2
  http: 1.1.0
  flutter_modular: ^5.0.3
  intl: ^0.18.1
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.316
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.10
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      ref: 0.0.14
  v1_feeds_rendering_engine:
    path: ../v1_feeds_rendering_engine
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.148
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
  manage_vitality_points_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-points-micro-service-sdk-flutter.git
      path: manage_vitality_points_micro_service_sdk
      ref: main
  manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-rewards-micro-service-sdk-flutter.git
      path: manage_rewards_micro_service_sdk
      ref: main
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.39
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.323
dependency_overrides:
  v1_feeds_rendering_engine:
    path: ../v1_feeds_rendering_engine
  v1_font_awesome_icons_flutter:
    git:
      url: **************:discovery-ltd/v1-font-awesome-icons-flutter.git
      path: v1_font_awesome_icons_flutter
      version: 0.0.8
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  dynatrace_flutter_plugin: 3.307.1
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      ref: 0.0.14
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.58
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.134
  http: 1.1.0
  intl: ^0.18.1
  flutter_secure_storage: ^8.0.0
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.316
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.552
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.148
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.21
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.467
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.11
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.10
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.39
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.19
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.74
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.19
  manage_content_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      path: manage_content_micro_service_sdk
      ref: main
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
  injectable_generator: ^2.4.1
  analyzer: ^5.13.0
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/module/
    - assets/market/
    - assets/central/
