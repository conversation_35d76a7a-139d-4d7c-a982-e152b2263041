# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.135] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: Ibe06734c338842388f386642e37e1d8b9da79e24

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I8e1ba0211e401a9959c06172054f445e53e70fbe

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I0ff1eb5af74377cce7ea6e838dc1d90d1ff35679 

## [v0.0.134] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi locale
Change-Id: I6eab933130576c1d5880b8aa1265260075acd058

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi locale
Change-Id: Ie10b55bd1db119be9481ecdbd110e98c46795ea4 

## [v0.0.133] - 2025-07-10

* [SFSTRY0100894] - Update versions
Change-Id: I22b9a6c3c7c3b7cf13e7983e901af68073c12466

* [SFSTRY0100894] - Update versions
Change-Id: Ie9eca02c7289aca2d9eb39d132ef032296d038ba

* [SFSTRY0100894] - Update support for see all screen
Change-Id: Ic86b2054d04f3d67f2482d30138a05d301f42d04

* [SFSTRY0100894] Fixed issue with test_app

* [SFSTRY0100894] Fixed issue with test_app

* [SFSTRY0100894] Fixed .gitignore

* [SFSTRY0100894] Fixed issue with test_app

* [SFSTRY0112552] Setup Login Journey in test_app

* [SFSTRY0100894] Fixed issue with test_app

* [SFSTRY0100894] Fixed issue with resource bundle UTF8 response

* [SFSTRY0100894] Removed unnecessary code

* [SFSTRY0100894] Moved deletion of FeedModel from renderer to wrapper

* [SFSTRY0100894][Feeds][Wrapper] - Added new params for wrapper
Change-Id: Ib5d17abe8e920768d60ae5bcca12ca03ffe6e76f

* [SFSTRY0100894] - Moved feeds card module in src

* [SFSTRY0100894][Feeds][Wrapper] - Moved event in feeds renderer

*  [SFSTRY0100894][Feeds][Wrapper] - Update pubspec

*  [SFSTRY0100894][Feeds][Wrapper] - Implementation for deleting feed card and return the latest cache

* [SFSTRY0100894][Feeds][Wrapper] - Added initial implementation for the feeds wrapper feature
Change-Id: I7913dd07081ebd26e3eca75c135a2cf58b7a52be 

## [v0.0.132] - 2025-05-15

* [SFSTRY0106473] - added vpoint card and its flow in status reward module. 

## [v0.0.131] - 2025-05-07

* [SFSTRY0101296] - toggle for remote app card 

## [v0.0.130] - 2025-04-29

* [SFSTRY0101296] - toggle for remote app card

* [SFSTRY0101296] - toggle for remote app card

* [SFSTRY0101296] - Introduced new feed card type "VitalityApplet" 

## [v0.0.129] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.128] - 2025-02-12

* [SFSTRY0040993] - label and points fix 

## [v0.0.127] - 2025-01-31

* [SFSTRY0100901]-Resolved issues when running flutter analyze 

## [v0.0.126] - 2025-01-21

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.125] - 2025-01-09

* [DFCT0040379] - modify expedia indentifier, use new feed card type instead. - changes made in reference to https://github.com/discovery-ltd/v1-feeds-rendering-engine-flutter/pull/135 review points 

## [v0.0.124] - 2024-12-12

* [SFSTRY0086433][FEEDS] - Externalize container
Change-Id: Ie2cc66b1a9dd2c5e68adc72367b5767d0fcdf086 

## [v0.0.123] - 2024-12-06

* [SFSTRY0097963][Gutenberg][Monthly Rewards - Rebased to master]

* [SFSTRY0097963][Gutenberg][Monthly Rewards - Added separator height] 

## [v0.0.122] - 2024-12-05

* [SFSTRY0094090][DFCT0040064][[GTB][T31-ECU] BYOD - The cashback amoun… (#143)
* [SFSTRY0094090][DFCT0040064][[GTB][T31-ECU] BYOD - The cashback amount displayed on BYOD homecard is hardcoded on all Tierst]

* [SFSTRY0094090][DFCT0040064][[GTB][T31-ECU] BYOD - The cashback amount displayed on BYOD homecard is hardcoded on all Tierst] 

## [v0.0.121] - 2024-11-29

* [SFSTRY0097722][FEEDS] - Update UT
Change-Id: I014f1b8fa48932d108513dd3ecc2a7c21b4e5e3a

* [SFSTRY0097722][FEEDS] - Added deletion of empty feeds
Change-Id: I14b044ee317e62246900eb26ce3e6bfe00884894 

## [v0.0.120] - 2024-11-22

* [DFCT0040560] - added byPassCacheDataFlag and updated platforcore, frameworkcontracts, applicationfeature , storagemanagement versions. 

## [v0.0.119] - 2024-11-06

* [SFSTRY0087226][Feeds] - Correct vlog data and correct nitpicks
Change-Id: I76f871a2b0e88651bb8cff2298cf1d628a98ffe3 

## [v0.0.118] - 2024-11-06

* [SFSTRY0087226][Feeds] - Use correct versions
Change-Id: Ie9d0338b68f24d96d7b294ae3b89bb002c4aef01

* [SFSTRY0087226][Feeds] - Added exception handling and Added dynatrace logging
Change-Id: Id2f197d14cfcd49894852c2e1a697637ef4589e1 

## [v0.0.117] - 2024-10-30

* [SFSTRY0070702] - update resource-bundle to 0.0.334

* [SFSTRY0070702] - update feed-card-builders to 0.0.101

* [SFSTRY0070702] - update feed-card-builders to 0.0.101

* [SFSTRY0070702] - ui lib version updated

* [SFSTRY0070702] - use expedia card layout-builder with bookings count tracker - update feed-card-builders version - update resource-bundle-manager with expedia strings 

## [v0.0.116] - 2024-10-28

* [SFSTSK0021462]Update certs.

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines.

* [SFSTRY0080527][Engineering - GTB] Minor alignments. Testing the PR process. 
