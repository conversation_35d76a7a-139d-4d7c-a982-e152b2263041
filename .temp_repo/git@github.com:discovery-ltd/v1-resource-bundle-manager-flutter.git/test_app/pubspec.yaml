name: test_app
description: "Test app for Resource Bundle Manager implementation."
publish_to: 'none'
version: 0.0.496
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_modular: ^5.0.3
  resource_bundle_manager:
    path: ../resource_bundle_manager
  intl: ^0.18.0
  rxdart: ^0.27.7
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.97
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.118
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.6
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^1.0.0
  build_runner: ^2.1.2
  analyzer: ^5.13.0
dependency_overrides:
  watcher: ^1.1.0
  http: 1.1.0
  intl: ^0.19.0
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.118
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.6
flutter:
  uses-material-design: true
  #  generate: true
  assets:
    - assets/
