# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][<PERSON><PERSON><PERSON>][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file. 

## [v0.0.394] - 2025-02-14

* [SFSTRY0101368] - Added keys for apps New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file.  devices subtitles 

## [v0.0.393] - 2025-02-13

* [SFSTRY0101044] - Added strings for upgrade OS prompt 

## [v0.0.392] - 2025-02-12

* [SFSTRY0102749]-Fitbit reminder model strings added 

## [v0.0.391] - 2025-02-11

* [DFCT0041712] - Teams challenge keys added 

## [v0.0.390] - 2025-02-10

* [DFCT0040726] - new key added for Teams challenge 

## [v0.0.389] - 2025-02-05

* [DFCT0041712] - added Teams challenge keys (#480) 

## [v0.0.388] - 2025-02-04

* [SFSTRY0098583] - new key added for device cashback 

## [v0.0.387] - 2025-02-03

* [SFSTRY0099654] - added missing keys in landing pages 

## [v0.0.386] - 2025-01-31

* [DFCT0042969] - contact us form phrase 

## [v0.0.385] - 2025-01-28

* [DFCT0041834]-Biometric-Phrases in Login screen 

## [v0.0.384] - 2025-01-20

* [SFSTRY0098948] - Added strings for Apple Watch device cashback 

## [v0.0.383] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.382] - 2025-01-17

* [DFCT0042403] - oldWheel feature card keys added 

## [v0.0.381] - 2025-01-15

* [DFCT0041637] - add "Code Copied" for expedia 

## [v0.0.380] - 2025-01-09

* [SFSTRY0099747] - add string 

## [v0.0.379] - 2024-12-19

* [DFCT0042291] - Translation issue on a VDP response message for multiple partners 

## [v0.0.378] - 2024-12-13

* [SFSTRY0096090] - add partner strings for Gift Cards 

## [v0.0.377] - 2024-12-12

* [DFCT0041128] - Added two date format keys for device cashback 

## [v0.0.376] - 2024-12-12

* [SFSTRY0096340] - Add strings for defect DFCT0040138 

## [v0.0.375] - 2024-12-11

* [DFCT0036221] - added monthly device challenge key 

## [v0.0.374] - 2024-12-10

* [SFSTRY0098168]: add inactive_membership_date_format 

## [v0.0.373] - 2024-12-06

* [SFSTRY0068017] - oldhweel feature card keys added 

## [v0.0.372] - 2024-12-05

* [SFSTRY0068017] - old wheel landing page keys add (#462)
* [SFSTRY0068017] - old wheel landing page keys add

* [SFSTRY0068017] - old wheel landing page keys added

* [SFSTRY0068017] - old wheel landing page keys added 

## [v0.0.371] - 2024-12-05

* [DFCT0041401] Add new keys for WLG history dateformats 

## [v0.0.370] - 2024-12-04

* [SFSTRY0094680] - status journey how it works cta for india market 

## [v0.0.369] - 2024-12-02

* [DFCT0041450][ECU] HF Visit Website key 

## [v0.0.368] - 2024-11-29

* [SFSTRY0095226]-Participating-Gym-Key-Update 

## [v0.0.367] - 2024-11-27

* [SFSTRY0090289][DFCT0039329][Mental Health assessment completion - Partner suggestion redirect ] 

## [v0.0.366] - 2024-11-26

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.365] - 2024-11-26

* [SFSTRY0096014] - add "contract details" string 

## [v0.0.364] - 2024-11-25

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.363] - 2024-11-25

* [DFCT0039748]-Add Keys Digital Stamp Privacy Policy 

## [v0.0.362] - 2024-11-22

* [SFSTRY0090289][Gutenberg][Profile New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file. 

## [v0.0.394] - 2025-02-14

* [SFSTRY0101368] - Added keys for apps New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file.  devices subtitles 

## [v0.0.393] - 2025-02-13

* [SFSTRY0101044] - Added strings for upgrade OS prompt 

## [v0.0.392] - 2025-02-12

* [SFSTRY0102749]-Fitbit reminder model strings added 

## [v0.0.391] - 2025-02-11

* [DFCT0041712] - Teams challenge keys added 

## [v0.0.390] - 2025-02-10

* [DFCT0040726] - new key added for Teams challenge 

## [v0.0.389] - 2025-02-05

* [DFCT0041712] - added Teams challenge keys (#480) 

## [v0.0.388] - 2025-02-04

* [SFSTRY0098583] - new key added for device cashback 

## [v0.0.387] - 2025-02-03

* [SFSTRY0099654] - added missing keys in landing pages 

## [v0.0.386] - 2025-01-31

* [DFCT0042969] - contact us form phrase 

## [v0.0.385] - 2025-01-28

* [DFCT0041834]-Biometric-Phrases in Login screen 

## [v0.0.384] - 2025-01-20

* [SFSTRY0098948] - Added strings for Apple Watch device cashback 

## [v0.0.383] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.382] - 2025-01-17

* [DFCT0042403] - oldWheel feature card keys added 

## [v0.0.381] - 2025-01-15

* [DFCT0041637] - add "Code Copied" for expedia 

## [v0.0.380] - 2025-01-09

* [SFSTRY0099747] - add string 

## [v0.0.379] - 2024-12-19

* [DFCT0042291] - Translation issue on a VDP response message for multiple partners 

## [v0.0.378] - 2024-12-13

* [SFSTRY0096090] - add partner strings for Gift Cards 

## [v0.0.377] - 2024-12-12

* [DFCT0041128] - Added two date format keys for device cashback 

## [v0.0.376] - 2024-12-12

* [SFSTRY0096340] - Add strings for defect DFCT0040138 

## [v0.0.375] - 2024-12-11

* [DFCT0036221] - added monthly device challenge key 

## [v0.0.374] - 2024-12-10

* [SFSTRY0098168]: add inactive_membership_date_format 

## [v0.0.373] - 2024-12-06

* [SFSTRY0068017] - oldhweel feature card keys added 

## [v0.0.372] - 2024-12-05

* [SFSTRY0068017] - old wheel landing page keys add (#462)
* [SFSTRY0068017] - old wheel landing page keys add

* [SFSTRY0068017] - old wheel landing page keys added

* [SFSTRY0068017] - old wheel landing page keys added 

## [v0.0.371] - 2024-12-05

* [DFCT0041401] Add new keys for WLG history dateformats 

## [v0.0.370] - 2024-12-04

* [SFSTRY0094680] - status journey how it works cta for india market 

## [v0.0.369] - 2024-12-02

* [DFCT0041450][ECU] HF Visit Website key 

## [v0.0.368] - 2024-11-29

* [SFSTRY0095226]-Participating-Gym-Key-Update 

## [v0.0.367] - 2024-11-27

* [SFSTRY0090289][DFCT0039329][Mental Health assessment completion - Partner suggestion redirect ] 

## [v0.0.366] - 2024-11-26

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.365] - 2024-11-26

* [SFSTRY0096014] - add "contract details" string 

## [v0.0.364] - 2024-11-25

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.363] - 2024-11-25

* [DFCT0039748]-Add Keys Digital Stamp Privacy Policy  Settings - Added language selection phase keys]

* [SFSTRY0090289][Gutenberg][Profile New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file. 

## [v0.0.394] - 2025-02-14

* [SFSTRY0101368] - Added keys for apps New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file.  devices subtitles 

## [v0.0.393] - 2025-02-13

* [SFSTRY0101044] - Added strings for upgrade OS prompt 

## [v0.0.392] - 2025-02-12

* [SFSTRY0102749]-Fitbit reminder model strings added 

## [v0.0.391] - 2025-02-11

* [DFCT0041712] - Teams challenge keys added 

## [v0.0.390] - 2025-02-10

* [DFCT0040726] - new key added for Teams challenge 

## [v0.0.389] - 2025-02-05

* [DFCT0041712] - added Teams challenge keys (#480) 

## [v0.0.388] - 2025-02-04

* [SFSTRY0098583] - new key added for device cashback 

## [v0.0.387] - 2025-02-03

* [SFSTRY0099654] - added missing keys in landing pages 

## [v0.0.386] - 2025-01-31

* [DFCT0042969] - contact us form phrase 

## [v0.0.385] - 2025-01-28

* [DFCT0041834]-Biometric-Phrases in Login screen 

## [v0.0.384] - 2025-01-20

* [SFSTRY0098948] - Added strings for Apple Watch device cashback 

## [v0.0.383] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.382] - 2025-01-17

* [DFCT0042403] - oldWheel feature card keys added 

## [v0.0.381] - 2025-01-15

* [DFCT0041637] - add "Code Copied" for expedia 

## [v0.0.380] - 2025-01-09

* [SFSTRY0099747] - add string 

## [v0.0.379] - 2024-12-19

* [DFCT0042291] - Translation issue on a VDP response message for multiple partners 

## [v0.0.378] - 2024-12-13

* [SFSTRY0096090] - add partner strings for Gift Cards 

## [v0.0.377] - 2024-12-12

* [DFCT0041128] - Added two date format keys for device cashback 

## [v0.0.376] - 2024-12-12

* [SFSTRY0096340] - Add strings for defect DFCT0040138 

## [v0.0.375] - 2024-12-11

* [DFCT0036221] - added monthly device challenge key 

## [v0.0.374] - 2024-12-10

* [SFSTRY0098168]: add inactive_membership_date_format 

## [v0.0.373] - 2024-12-06

* [SFSTRY0068017] - oldhweel feature card keys added 

## [v0.0.372] - 2024-12-05

* [SFSTRY0068017] - old wheel landing page keys add (#462)
* [SFSTRY0068017] - old wheel landing page keys add

* [SFSTRY0068017] - old wheel landing page keys added

* [SFSTRY0068017] - old wheel landing page keys added 

## [v0.0.371] - 2024-12-05

* [DFCT0041401] Add new keys for WLG history dateformats 

## [v0.0.370] - 2024-12-04

* [SFSTRY0094680] - status journey how it works cta for india market 

## [v0.0.369] - 2024-12-02

* [DFCT0041450][ECU] HF Visit Website key 

## [v0.0.368] - 2024-11-29

* [SFSTRY0095226]-Participating-Gym-Key-Update 

## [v0.0.367] - 2024-11-27

* [SFSTRY0090289][DFCT0039329][Mental Health assessment completion - Partner suggestion redirect ] 

## [v0.0.366] - 2024-11-26

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.365] - 2024-11-26

* [SFSTRY0096014] - add "contract details" string 

## [v0.0.364] - 2024-11-25

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.363] - 2024-11-25

* [DFCT0039748]-Add Keys Digital Stamp Privacy Policy  Settings - Added language selection phase keys]

* [SFSTRY0090289][Gutenberg][Profile New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file. 

## [v0.0.394] - 2025-02-14

* [SFSTRY0101368] - Added keys for apps New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file.  devices subtitles 

## [v0.0.393] - 2025-02-13

* [SFSTRY0101044] - Added strings for upgrade OS prompt 

## [v0.0.392] - 2025-02-12

* [SFSTRY0102749]-Fitbit reminder model strings added 

## [v0.0.391] - 2025-02-11

* [DFCT0041712] - Teams challenge keys added 

## [v0.0.390] - 2025-02-10

* [DFCT0040726] - new key added for Teams challenge 

## [v0.0.389] - 2025-02-05

* [DFCT0041712] - added Teams challenge keys (#480) 

## [v0.0.388] - 2025-02-04

* [SFSTRY0098583] - new key added for device cashback 

## [v0.0.387] - 2025-02-03

* [SFSTRY0099654] - added missing keys in landing pages 

## [v0.0.386] - 2025-01-31

* [DFCT0042969] - contact us form phrase 

## [v0.0.385] - 2025-01-28

* [DFCT0041834]-Biometric-Phrases in Login screen 

## [v0.0.384] - 2025-01-20

* [SFSTRY0098948] - Added strings for Apple Watch device cashback 

## [v0.0.383] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.382] - 2025-01-17

* [DFCT0042403] - oldWheel feature card keys added 

## [v0.0.381] - 2025-01-15

* [DFCT0041637] - add "Code Copied" for expedia 

## [v0.0.380] - 2025-01-09

* [SFSTRY0099747] - add string 

## [v0.0.379] - 2024-12-19

* [DFCT0042291] - Translation issue on a VDP response message for multiple partners 

## [v0.0.378] - 2024-12-13

* [SFSTRY0096090] - add partner strings for Gift Cards 

## [v0.0.377] - 2024-12-12

* [DFCT0041128] - Added two date format keys for device cashback 

## [v0.0.376] - 2024-12-12

* [SFSTRY0096340] - Add strings for defect DFCT0040138 

## [v0.0.375] - 2024-12-11

* [DFCT0036221] - added monthly device challenge key 

## [v0.0.374] - 2024-12-10

* [SFSTRY0098168]: add inactive_membership_date_format 

## [v0.0.373] - 2024-12-06

* [SFSTRY0068017] - oldhweel feature card keys added 

## [v0.0.372] - 2024-12-05

* [SFSTRY0068017] - old wheel landing page keys add (#462)
* [SFSTRY0068017] - old wheel landing page keys add

* [SFSTRY0068017] - old wheel landing page keys added

* [SFSTRY0068017] - old wheel landing page keys added 

## [v0.0.371] - 2024-12-05

* [DFCT0041401] Add new keys for WLG history dateformats 

## [v0.0.370] - 2024-12-04

* [SFSTRY0094680] - status journey how it works cta for india market 

## [v0.0.369] - 2024-12-02

* [DFCT0041450][ECU] HF Visit Website key 

## [v0.0.368] - 2024-11-29

* [SFSTRY0095226]-Participating-Gym-Key-Update 

## [v0.0.367] - 2024-11-27

* [SFSTRY0090289][DFCT0039329][Mental Health assessment completion - Partner suggestion redirect ] 

## [v0.0.366] - 2024-11-26

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.365] - 2024-11-26

* [SFSTRY0096014] - add "contract details" string 

## [v0.0.364] - 2024-11-25

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.363] - 2024-11-25

* [DFCT0039748]-Add Keys Digital Stamp Privacy Policy  Settings - Added language selection phase keys]

* [SFSTRY0090289][Gutenberg][Profile New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file. 

## [v0.0.394] - 2025-02-14

* [SFSTRY0101368] - Added keys for apps New Changes:

## [v0.0.496] - 2025-08-13

* [SFSTRY0111065] new string added for JH cotinine 

## [v0.0.495] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Resource Bundle Manager - New keys for HealthyFood] 

## [v0.0.494] - 2025-08-08

* [SFSTRY0115150] - add headspace breakout page url 

## [v0.0.493] - 2025-08-08

* [SFSTRY0117693][SLI][VHC CR-325 strings added] 

## [v0.0.492] - 2025-08-07

* [SFSTRY0113988] - conflicts fixes

* [SFSTRY0113988] - new strings added for manually submit a workout 

## [v0.0.491] - 2025-08-07

* [SFSTRY0106129][VNA] - Added new phrase key 

## [v0.0.490] - 2025-08-07

* [SFSTRY0115148] - fixed master merge conflicts.

* [SFSTRY0115148] - add strings for headspace partner 

## [v0.0.489] - 2025-08-06

* [SFSTRY0113984] - conflicts fixes

* [SFSTRY0113984] - new strings added for Workout screen

* [SFSTRY0113984] - strings added for Workout screen 

## [v0.0.488] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-Glucose Intro key added for PRT 

## [v0.0.487] - 2025-07-31

* [SFSTRY0116222] - Multicare freemium phrase key added for prt

* [SFSTRY0116222] - freemium phrase key added for prt 

## [v0.0.486] - 2025-07-30

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.485] - 2025-07-29

* [SFSTRY0105906][VNA] - Added new phrase key 

## [v0.0.484] - 2025-07-24

* [SFSTRY0110877] - add resource_bundle String 

## [v0.0.483] - 2025-07-23

* [SFSTRY0116558] - add active calories strings 

## [v0.0.482] - 2025-07-22

* [SFSTRY0116657] - strings added for book screening 

## [v0.0.481] - 2025-07-18

* [SFSTRY0107241] - [My Health] Add new screen for Generic Health Priority 

## [v0.0.480] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for Healthy Food] 

## [v0.0.479] - 2025-07-15

* [SFSTRY0112088][Gutenberg][Resource Bundle - grail partner keys] 

## [v0.0.478] - 2025-07-14

* [SFSTRY0114128] - add prenatal and diabetes support strings

* [SFSTRY0114128] - add prenatal and diabetes support strings 

## [v0.0.477] - 2025-07-10

* [SFSTRY0109072] - add prenatal and safe driving strings 

## [v0.0.476] - 2025-07-08

* [DFCT0040323] - biometric value update

* [DFCT0040323] - biometric value update 

## [v0.0.475] - 2025-06-30

* [SFSTRY0113499] - Add burned calories details text.

* [SFSTRY0113499] - Add burned calories details text. 

## [v0.0.474] - 2025-06-27

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.473] - 2025-06-27

* [SFSTRY0111563][VNA] - Added new phrase keys 

## [v0.0.472] - 2025-06-26

* [SFSTRY0113807] - add resource_bundle String 

## [v0.0.471] - 2025-06-25

* [SFSTRY0112895] - Add Safe driving and prenantal care strings. 

## [v0.0.470] - 2025-06-24

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.469] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.468] - 2025-06-24

* [SFSTRY0113310] - add resource_bundle String 

## [v0.0.467] - 2025-06-23

* [SFSTRY0112895][Gutenberg][Resource Bundle - safe diving keys] 

## [v0.0.466] - 2025-06-20

* [DFCT0047315] - added password phrase strings. 

## [v0.0.465] - 2025-06-20

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.464] - 2025-06-18

* [SFSTRY0111559][VNA] - Added new phrase keys 

## [v0.0.463] - 2025-06-18

* [SFSTRY0112390] - Resource Bundle - adidas codes history strings 

## [v0.0.462] - 2025-06-18

* [SFSTRY0107831] - IGI -new key/ value pair addition for fuel bar benefits 

## [v0.0.461] - 2025-06-17

* [SFSTRY0107245] - add resource_bundle String 

## [v0.0.460] - 2025-06-13

* [SFSTRY0111557][VNA] - Added/updated phrase keys

* [SFSTRY0111557][VNA] - Added/updated phrase keys 

## [v0.0.459] - 2025-06-13

* [SFSTRY0111568][Gutenberg][Resource Bundle - small correction]

* [SFSTRY0111568][Gutenberg][Resource Bundle - added new key for Nutrisense partner 

## [v0.0.458] - 2025-06-11

* [SFSTRY0112832][Gutenberg][Resource bundle- Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#556) 

## [v0.0.457] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings 

## [v0.0.456] - 2025-06-10

* [SFSTRY0112390] - Resource Bundle - adidas landing screen reward status strings update 

## [v0.0.455] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-YouAndVitality Keys added 

## [v0.0.454] - 2025-06-09

* [SFSTRY0112390] - add resource_bundle String 

## [v0.0.453] - 2025-06-06

* [SFSTRY0111557][VNA] - Added new phrase keys 

## [v0.0.452] - 2025-06-05

* [SFSTRY0110754] - added new key for diabetes landing 

## [v0.0.451] - 2025-06-05

* [SFSTRY0110105] - Add VHR Overall health strings. 

## [v0.0.450] - 2025-06-04

* [SFSTRY0112380] - Resource Bundle - added app supported strings under rewards partner lear more about rules screen 

## [v0.0.449] - 2025-06-03

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.448] - 2025-05-30

* [SFSTRY0110981] - added new keys for landing earn points

* [SFSTRY0110981] - Added diabeties support strings 

## [v0.0.447] - 2025-05-29

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.446] - 2025-05-27

* [SFSTRY0109226][VNA] - Added new phrase keys 

## [v0.0.445] - 2025-05-27

* [SFSTRY0107018] - Resource Bundle - added app supported strings under  codes history screen in rewards 

## [v0.0.444] - 2025-05-22

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated] (#540)
* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated]

* [SFSTRY0094772][Gutenberg][Resource bundle- reflection text string updated][Gen fies]

---------

Co-authored-by: P01NiteshT <<EMAIL>> 

## [v0.0.443] - 2025-05-22

* [SFSTRY0105904][VNA] - Added new phrase keys 

## [v0.0.442] - 2025-05-22

* [SFSTRY0107018] - Resource Bundle - added app supported strings under rewards codes history screen 

## [v0.0.441] - 2025-05-21

* [SFSTRY0110199] Fixed generation of g.dart

* [SFSTRY0110199]Added MSMR new keys 

## [v0.0.440] - 2025-05-19

* [SFSTRY0094772][Gutenberg][Resource bundle - bottom sheet string added] (… (#537) 

## [v0.0.439] - 2025-05-15

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.438] - 2025-05-15

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.437] - 2025-05-15

* [SFSTRY0109554] - add new key for Vitality ID 

## [v0.0.436] - 2025-05-14

* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added] (#533)
* [SFSTRY0094772][Gutenberg][Resource bundle - txt week string added]

* [SFSTRY0094772][Gutenberg][Resource bundle - removed mock files] 

## [v0.0.435] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.434] - 2025-05-14

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward

* [SFSTRY0107017] - update resource_bundle String 

## [v0.0.433] - 2025-05-13

* [SFSTRY0109556] - fix bitrise issue.

* [SFSTRY0109556] - Added tags for contact us screen. 

## [v0.0.432] - 2025-05-12

* [SFSTRY0108945] - Added bmi condition strings 

## [v0.0.431] - 2025-05-12

* [SFSTRY0108945] - Added bmi note string 

## [v0.0.430] - 2025-05-09

* [SFSTRY0109416] - solved conflicts.

* [SFSTRY0109416] - Added Settings login preferences update button strings.

* [SFSTRY0109416] - Added Settings login preferences strings. 

## [v0.0.429] - 2025-05-07

* [SFSTRY0108945] - added resource_bundle.g.dart

* [SFSTRY0108945] - Added Smart scale string 

## [v0.0.428] - 2025-05-02

* [SFSTRY0106993] - Resource Bundle - added app supported strings under adidas reward 

## [v0.0.427] - 2025-04-22

* [SFSTRY0089938] - updated app supported goal string 

## [v0.0.426] - 2025-04-22

* [SFSTRY0103702][Gutenberg][Resource Bundle Manager - Configure property files for all possible milestones] (#521) 

## [v0.0.425] - 2025-04-21

* [SFSTRY0089938] - added phrase keys for automatic tracked goals 

## [v0.0.424] - 2025-04-17

* [SFSTRY0108848] - Added phase key for new heart rate variation

* [SFSTRY0108848] - Added phase key for new heart rate variation 

## [v0.0.423] - 2025-04-15

* [SFSTRY0105112][VNA] - Added new phrase keys 

## [v0.0.422] - 2025-04-10

* [SFSTRY0101307] - app supported goals string 

## [v0.0.421] - 2025-04-03

* [SFSTRY0105111][JH] - New phrase keys added 

## [v0.0.420] - 2025-03-26

* [SFSTRY0098489][SLI][Teams challenge new keys added] 

## [v0.0.419] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey added new keys 

## [v0.0.418] - 2025-03-20

* [SFSTRY0095934][SLI][Vitality journey new keys added] 

## [v0.0.417] - 2025-03-19

* [DFCT0044952][SLI]-DateFormat Configured for Status Details Screen 

## [v0.0.416] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.415] - 2025-03-18

* [DFCT0041712] - new key added for team challenges 

## [v0.0.414] - 2025-03-13

* [DFCT0041712] - Wording change for team challenge

* [DFCT0041712] - Wording change for team challenge 

## [v0.0.413] - 2025-03-12

* [DFCT0041917] - new key added for expedia journey

* [DFCT0041917] - Updated kotlin Version

* [DFCT0041917] - new key added for expedia journey 

## [v0.0.412] - 2025-03-10

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.411] - 2025-03-06

* [DFCT0044485] - After activating PAG, on PAG landing screen displaying "insurer" instead of insurer name 

## [v0.0.410] - 2025-03-04

* [SFSTRY0101434] - redirect user to app store or FitBit app- Andriod 

## [v0.0.409] - 2025-03-04

* [DFCT0044379][SLI] - Add new keys for WDA/PAG title

* [DFCT0044379][SLI] - Add keys for WDA/PAG title 

## [v0.0.408] - 2025-03-03

* [SFSTRY0047981] - cache and reuse "key: resource_bundle prop-id jsonmap" 

## [v0.0.407] - 2025-03-03

* [DFCT0044114] - Upload File error message displaying in English 

## [v0.0.406] - 2025-02-28

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.405] - 2025-02-28

* [DFCT0043727] - revert the flutter version in bitrise.yml

* [DFCT0043727] - Updated version in test_app/pubspec.yaml

* [DFCT0043727] - Updated version in bitrise.yml and pubspec.yaml

* [DFCT0043727] - Removed ^ in http

* [DFCT0043727] - Added partner_description_2345 in base.json 

## [v0.0.404] - 2025-02-21

* [DFCT0043840] - new key added for vitality journey 

## [v0.0.403] - 2025-02-19

* [SFSTRY0102749]-[CR-324 strings added] 

## [v0.0.402] - 2025-02-19

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.401] - 2025-02-19

* [DFCT0043842] - Wording change for Help centre title 

## [v0.0.400] - 2025-02-18

* [SFSTRY0101341] - New keys have been added, and the content of the old keys has been updated to handle the latest onboarding connect screen 

## [v0.0.399] - 2025-02-18

* [SFSTRY0101373] - Added Health Connect page keys 

## [v0.0.398] - 2025-02-18

* [DFCT0042606] Added new key for reinstatement period URL

* [DFCT0042606] Added new key for reinstatement period URL 

## [v0.0.397] - 2025-02-18

* [SFSTRY0102653] - new key added for mental wellbeing my health result value 

## [v0.0.396] - 2025-02-14

* [DFCT0043777] - new key added for vitality journey 

## [v0.0.395] - 2025-02-14

* [DFCT0041712] - added 2 string in json file.  devices subtitles 

## [v0.0.393] - 2025-02-13

* [SFSTRY0101044] - Added strings for upgrade OS prompt 

## [v0.0.392] - 2025-02-12

* [SFSTRY0102749]-Fitbit reminder model strings added 

## [v0.0.391] - 2025-02-11

* [DFCT0041712] - Teams challenge keys added 

## [v0.0.390] - 2025-02-10

* [DFCT0040726] - new key added for Teams challenge 

## [v0.0.389] - 2025-02-05

* [DFCT0041712] - added Teams challenge keys (#480) 

## [v0.0.388] - 2025-02-04

* [SFSTRY0098583] - new key added for device cashback 

## [v0.0.387] - 2025-02-03

* [SFSTRY0099654] - added missing keys in landing pages 

## [v0.0.386] - 2025-01-31

* [DFCT0042969] - contact us form phrase 

## [v0.0.385] - 2025-01-28

* [DFCT0041834]-Biometric-Phrases in Login screen 

## [v0.0.384] - 2025-01-20

* [SFSTRY0098948] - Added strings for Apple Watch device cashback 

## [v0.0.383] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.382] - 2025-01-17

* [DFCT0042403] - oldWheel feature card keys added 

## [v0.0.381] - 2025-01-15

* [DFCT0041637] - add "Code Copied" for expedia 

## [v0.0.380] - 2025-01-09

* [SFSTRY0099747] - add string 

## [v0.0.379] - 2024-12-19

* [DFCT0042291] - Translation issue on a VDP response message for multiple partners 

## [v0.0.378] - 2024-12-13

* [SFSTRY0096090] - add partner strings for Gift Cards 

## [v0.0.377] - 2024-12-12

* [DFCT0041128] - Added two date format keys for device cashback 

## [v0.0.376] - 2024-12-12

* [SFSTRY0096340] - Add strings for defect DFCT0040138 

## [v0.0.375] - 2024-12-11

* [DFCT0036221] - added monthly device challenge key 

## [v0.0.374] - 2024-12-10

* [SFSTRY0098168]: add inactive_membership_date_format 

## [v0.0.373] - 2024-12-06

* [SFSTRY0068017] - oldhweel feature card keys added 

## [v0.0.372] - 2024-12-05

* [SFSTRY0068017] - old wheel landing page keys add (#462)
* [SFSTRY0068017] - old wheel landing page keys add

* [SFSTRY0068017] - old wheel landing page keys added

* [SFSTRY0068017] - old wheel landing page keys added 

## [v0.0.371] - 2024-12-05

* [DFCT0041401] Add new keys for WLG history dateformats 

## [v0.0.370] - 2024-12-04

* [SFSTRY0094680] - status journey how it works cta for india market 

## [v0.0.369] - 2024-12-02

* [DFCT0041450][ECU] HF Visit Website key 

## [v0.0.368] - 2024-11-29

* [SFSTRY0095226]-Participating-Gym-Key-Update 

## [v0.0.367] - 2024-11-27

* [SFSTRY0090289][DFCT0039329][Mental Health assessment completion - Partner suggestion redirect ] 

## [v0.0.366] - 2024-11-26

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.365] - 2024-11-26

* [SFSTRY0096014] - add "contract details" string 

## [v0.0.364] - 2024-11-25

* [SFSTRY0051170] - market credential breakout string added 

## [v0.0.363] - 2024-11-25

* [DFCT0039748]-Add Keys Digital Stamp Privacy Policy  Settings - Added language selection phase keys]

* [SFSTRY0090289] - Added language selection phase keys 

## [v0.0.361] - 2024-11-21

* [SFSTRY0096710] - string changes

* [SFSTRY0096710] - string changes 

## [v0.0.360] - 2024-11-20

* [SFSTRY0091087] Added strings for Device Cashback 

## [v0.0.359] - 2024-11-20

* [SFSTRY0067134] - added strings for Old Wheel Journey 

## [v0.0.358] - 2024-11-19

* [SFSTRY0090289] - Added language phase key (#448)
* [SFSTRY0090289] - Added language phase keys

* [SFSTRY0090289] - new strings.

---------

Co-authored-by: MadhanObilisetti <<EMAIL>> 

## [v0.0.357] - 2024-11-19

* [SFSTRY0094771] - google fit permission key added 

## [v0.0.356] - 2024-11-18

* [SFSTRY0092856] - SLI CR065 key 

## [v0.0.355] - 2024-11-18

* [SFSTRY0096164] Key values added for permission dialog 

## [v0.0.354] - 2024-11-15

* [SFSTRY0094771] - steps range key added 

## [v0.0.353] - 2024-11-15

* [SFSTRY0095822] - added two new phrase app keys as per market requirement 

## [v0.0.352] - 2024-11-13

* [SFSTRY0093305] - Code refactor

* [SFSTRY0093305] - Version update and caching mechanism enhancement.

* [SFSTRY0093305] - Deprecating the caching method because a new caching mechanism has been implemented in common_lib. 

## [v0.0.351] - 2024-11-07

* [DFCT0040066] - shortcut link section header key added 

## [v0.0.350] - 2024-11-07

* [SFSTRY0096653] - generated keys

* [SFSTRY0096653] - Heart rate phase string added 

## [v0.0.349] - 2024-11-05

* [SFSTRY0094746] - phrase string for india market with discounts

* [SFSTRY0094746] - homescreen status update with discounts 

## [v0.0.348] - 2024-10-30

* [SFSTRY0092856] - SLI SnV CR061 keys 

## [v0.0.347] - 2024-10-30

* [DFCT0039220] - keys added 

## [v0.0.346] - 2024-10-30

* [DFCT0039220] - keys added 

## [v0.0.345] - 2024-10-30

* [DFCT0039984] - keys added 

## [v0.0.344] - 2024-10-28

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines. 

## [v0.0.343] - 2024-10-28

* [SFSTRY0095836] - Data sharing or storage key 

## [v0.0.342] - 2024-10-28

* [SFSTRY0067639]-Add Keys Archive Popup in GiftCards 

## [v0.0.341] - 2024-10-25

* [SFSTRY0094121] - new strings for home screen 

## [v0.0.137] - 2024-03-13

* [SFSTRY0073337] [Engineering - GTB]Add a readme/changelog file and update it with latest commits during every new release tag. 2nd test.

* [SFSTRY0073337] [Engineering - GTB]Add a readme/changelog file and update it with latest commits during every new release tag. 1st test. 
