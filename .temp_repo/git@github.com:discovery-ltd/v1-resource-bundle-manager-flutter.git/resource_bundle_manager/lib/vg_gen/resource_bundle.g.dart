// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'resource_bundle.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VGResourceBundle _$VGResourceBundleFromJson(Map<String, dynamic> json) =>
    VGResourceBundle(
      already_have_an_account_2:
          json['already_have_an_account_2'] as String? ?? "",
      appdevices_applehealth_txtconnect_001:
          json['appdevices_applehealth_txtconnect_001'] as String? ?? "",
      appdevices_applehealth_txtmanagepermissions_001:
          json['appdevices_applehealth_txtmanagepermissions_001'] as String? ??
              "",
      appdevices_applehealth_txtsynctime_001:
          json['appdevices_applehealth_txtsynctime_001'] as String? ?? "",
      appdevices_fitbit_txtconnect_001:
          json['appdevices_fitbit_txtconnect_001'] as String? ?? "",
      appdevices_fitbit_txtmanagepermissions_001:
          json['appdevices_fitbit_txtmanagepermissions_001'] as String? ?? "",
      appdevices_fitbit_txtsynctime_001:
          json['appdevices_fitbit_txtsynctime_001'] as String? ?? "",
      appdevices_garmin_txtconnect_001:
          json['appdevices_garmin_txtconnect_001'] as String? ?? "",
      appdevices_garmin_txtmanagepermissions_001:
          json['appdevices_garmin_txtmanagepermissions_001'] as String? ?? "",
      appdevices_garmin_txtsynctime_001:
          json['appdevices_garmin_txtsynctime_001'] as String? ?? "",
      appdevices_googlefit_txtconnect_001:
          json['appdevices_googlefit_txtconnect_001'] as String? ?? "",
      appdevices_googlefit_txtmanagepermissions_001:
          json['appdevices_googlefit_txtmanagepermissions_001'] as String? ??
              "",
      appdevices_googlefit_txtsynctime_001:
          json['appdevices_googlefit_txtsynctime_001'] as String? ?? "",
      appdevices_polar_txtconnect_001:
          json['appdevices_polar_txtconnect_001'] as String? ?? "",
      appdevices_polar_txtmanagepermissions_001:
          json['appdevices_polar_txtmanagepermissions_001'] as String? ?? "",
      appdevices_polar_txtsynctime_001:
          json['appdevices_polar_txtsynctime_001'] as String? ?? "",
      appdevices_samsunghealth_txtconnect_001:
          json['appdevices_samsunghealth_txtconnect_001'] as String? ?? "",
      appdevices_samsunghealth_txtmanagepermissions_001:
          json['appdevices_samsunghealth_txtmanagepermissions_001']
                  as String? ??
              "",
      appdevices_samsunghealth_txtsynctime_001:
          json['appdevices_samsunghealth_txtsynctime_001'] as String? ?? "",
      appdevices_strava_txtconnect_001:
          json['appdevices_strava_txtconnect_001'] as String? ?? "",
      appdevices_strava_txtmanagepermissions_001:
          json['appdevices_strava_txtmanagepermissions_001'] as String? ?? "",
      appdevices_strava_txtsynctime_001:
          json['appdevices_strava_txtsynctime_001'] as String? ?? "",
      appdevices_suunto_txtconnect_001:
          json['appdevices_suunto_txtconnect_001'] as String? ?? "",
      appdevices_suunto_txtmanagepermissions_001:
          json['appdevices_suunto_txtmanagepermissions_001'] as String? ?? "",
      appdevices_suunto_txtsynctime_001:
          json['appdevices_suunto_txtsynctime_001'] as String? ?? "",
      appdevices_withings_txtconnect_001:
          json['appdevices_withings_txtconnect_001'] as String? ?? "",
      appdevices_withings_txtmanagepermissions_001:
          json['appdevices_withings_txtmanagepermissions_001'] as String? ?? "",
      appdevices_withings_txtsynctime_001:
          json['appdevices_withings_txtsynctime_001'] as String? ?? "",
      apppsdevices_landing_txtlastsyncdate_001:
          json['apppsdevices_landing_txtlastsyncdate_001'] as String? ?? "",
      appsdevices_fitbit_txtsyncaccount_001:
          json['appsdevices_fitbit_txtsyncaccount_001'] as String? ?? "",
      appsdevices_samsunghealth_txtallpermissions_001:
          json['appsdevices_samsunghealth_txtallpermissions_001'] as String? ??
              "",
      appsdevices_samsunghealth_txtbodyallpermissions_001:
          json['appsdevices_samsunghealth_txtbodyallpermissions_001']
                  as String? ??
              "",
      appsdevices_samsunghealth_txtbodyconnectionerror_001:
          json['appsdevices_samsunghealth_txtbodyconnectionerror_001']
                  as String? ??
              "",
      appsdevices_samsunghealth_txtbodydisconnect_001:
          json['appsdevices_samsunghealth_txtbodydisconnect_001'] as String? ??
              "",
      appsdevices_samsunghealth_txtconnected_001:
          json['appsdevices_samsunghealth_txtconnected_001'] as String? ?? "",
      appsdevices_samsunghealth_txtdisconnectalert_001:
          json['appsdevices_samsunghealth_txtdisconnectalert_001'] as String? ??
              "",
      appsdevices_samsunghealth_txthddisconnect_001:
          json['appsdevices_samsunghealth_txthddisconnect_001'] as String? ??
              "",
      appsdevices_samsunghealth_txtsyncaccount_001:
          json['appsdevices_samsunghealth_txtsyncaccount_001'] as String? ?? "",
      appsdevices_samsungsupport_txtbodyhowitworks_001:
          json['appsdevices_samsungsupport_txtbodyhowitworks_001'] as String? ??
              "",
      appsdevices_samsungsupport_txthdhowitworks_001:
          json['appsdevices_samsungsupport_txthdhowitworks_001'] as String? ??
              "",
      body_AppCrashSettins: json['body_AppCrashSettins'] as String? ?? "",
      body_cholestrolAbout: json['body_cholestrolAbout'] as String? ?? "",
      body_EmailSettings: json['body_EmailSettings'] as String? ?? "",
      body_emptyState_: json['body_emptyState_'] as String? ?? "",
      body_emptyState_noPromotion:
          json['body_emptyState_noPromotion'] as String? ?? "",
      body_MembershipCancelled:
          json['body_MembershipCancelled'] as String? ?? "",
      body_NeedHelp: json['body_NeedHelp'] as String? ?? "",
      body_Needhelp: json['body_Needhelp'] as String? ?? "",
      body_PrivacyPolicy: json['body_PrivacyPolicy'] as String? ?? "",
      body_SaveEmailLogin: json['body_SaveEmailLogin'] as String? ?? "",
      body_ScreenAnalyticsSettings:
          json['body_ScreenAnalyticsSettings'] as String? ?? "",
      body_submitproofparagraph:
          json['body_submitproofparagraph'] as String? ?? "",
      body_TsCs: json['body_TsCs'] as String? ?? "",
      btn_Agree: json['btn_Agree'] as String? ?? "",
      btn_Disagree: json['btn_Disagree'] as String? ?? "",
      btn_LogOut: json['btn_LogOut'] as String? ?? "",
      btn_next: json['btn_next'] as String? ?? "",
      btn_ResetPw: json['btn_ResetPw'] as String? ?? "",
      btn_SavePreferences: json['btn_SavePreferences'] as String? ?? "",
      btn_showAll: json['btn_showAll'] as String? ?? "",
      chip_activeEnergy: json['chip_activeEnergy'] as String? ?? "",
      chip_heartRate: json['chip_heartRate'] as String? ?? "",
      chip_steps: json['chip_steps'] as String? ?? "",
      chip_weight: json['chip_weight'] as String? ?? "",
      crd_title_supportQ1: json['crd_title_supportQ1'] as String? ?? "",
      crd_title_supportQ2: json['crd_title_supportQ2'] as String? ?? "",
      crd_title_supportQ3: json['crd_title_supportQ3'] as String? ?? "",
      crd_title_supportQ4: json['crd_title_supportQ4'] as String? ?? "",
      ctrl_appdevices_applehealth_btnConnect_001:
          json['ctrl_appdevices_applehealth_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_applehealth_txtsupport_001:
          json['ctrl_appdevices_applehealth_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_fitbit_btnConnect_001:
          json['ctrl_appdevices_fitbit_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_fitbit_txtsupport_001:
          json['ctrl_appdevices_fitbit_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_garmin_btnConnect_001:
          json['ctrl_appdevices_garmin_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_garmin_txtsupport_001:
          json['ctrl_appdevices_garmin_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_googlefit_btnConnect_001:
          json['ctrl_appdevices_googlefit_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_googlefit_txtsupport_001:
          json['ctrl_appdevices_googlefit_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_polar_btnConnect_001:
          json['ctrl_appdevices_polar_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_polar_txtsupport_001:
          json['ctrl_appdevices_polar_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_samsunghealth_btnConnect_001:
          json['ctrl_appdevices_samsunghealth_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_samsunghealth_txtsupport_001:
          json['ctrl_appdevices_samsunghealth_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_strava_btnConnect_001:
          json['ctrl_appdevices_strava_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_strava_txtsupport_001:
          json['ctrl_appdevices_strava_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_suunto_btnConnect_001:
          json['ctrl_appdevices_suunto_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_suunto_txtsupport_001:
          json['ctrl_appdevices_suunto_txtsupport_001'] as String? ?? "",
      ctrl_appdevices_withings_btnConnect_001:
          json['ctrl_appdevices_withings_btnConnect_001'] as String? ?? "",
      ctrl_appdevices_withings_txtsupport_001:
          json['ctrl_appdevices_withings_txtsupport_001'] as String? ?? "",
      ctrl_appsdevices_applehealth_btndisconnect_001:
          json['ctrl_appsdevices_applehealth_btndisconnect_001'] as String? ??
              "",
      ctrl_appsdevices_applehealth_btnmanpermissions_001:
          json['ctrl_appsdevices_applehealth_btnmanpermissions_001']
                  as String? ??
              "",
      ctrl_appsdevices_fitbit_btndisconnect_001:
          json['ctrl_appsdevices_fitbit_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_garmin_btndisconnect_001:
          json['ctrl_appsdevices_garmin_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_googlefit_btndisconnect_001:
          json['ctrl_appsdevices_googlefit_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_polar_btndisconnect_001:
          json['ctrl_appsdevices_polar_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_samsunghealth_btndisconnect_001:
          json['ctrl_appsdevices_samsunghealth_btndisconnect_001'] as String? ??
              "",
      ctrl_appsdevices_samsunghealth_btnmanpermissions_001:
          json['ctrl_appsdevices_samsunghealth_btnmanpermissions_001']
                  as String? ??
              "",
      ctrl_appsdevices_strava_btndisconnect_001:
          json['ctrl_appsdevices_strava_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_suunto_btndisconnect_001:
          json['ctrl_appsdevices_suunto_btndisconnect_001'] as String? ?? "",
      ctrl_appsdevices_withings_btndisconnect_001:
          json['ctrl_appsdevices_withings_btndisconnect_001'] as String? ?? "",
      ctrl_btnactivateacc_001: json['ctrl_btnactivateacc_001'] as String? ?? "",
      ctrl_btnactivategoal_001:
          json['ctrl_btnactivategoal_001'] as String? ?? "",
      txtcholesterol_guide_001:
          json['txtcholesterol_guide_001'] as String? ?? "",
      txthealthycholesterol_intro_001:
          json['txthealthycholesterol_intro_001'] as String? ?? "",
      txttips_cholesterol_001: json['txttips_cholesterol_001'] as String? ?? "",
      txttips_cholesterol_content_001:
          json['txttips_cholesterol_content_001'] as String? ?? "",
      ctrl_btnagree_002: json['ctrl_btnagree_002'] as String? ?? "",
      ctrl_btnallow_001: json['ctrl_btnallow_001'] as String? ?? "",
      ctrl_btnancel_001: json['ctrl_btnancel_001'] as String? ?? "",
      ctrl_btnangleright_001: json['ctrl_btnangleright_001'] as String? ?? "",
      ctrl_btnappsanddevices_001:
          json['ctrl_btnappsanddevices_001'] as String? ?? "",
      ctrl_btncancel_001: json['ctrl_btncancel_001'] as String? ?? "",
      ctrl_btncancel_002: json['ctrl_btncancel_002'] as String? ?? "",
      ctrl_btnconfirm_001: json['ctrl_btnconfirm_001'] as String? ?? "",
      ctrl_btnconfirm_002: json['ctrl_btnconfirm_002'] as String? ?? "",
      ctrl_btnconnect_001: json['ctrl_btnconnect_001'] as String? ?? "",
      ctrl_btncontinue_001: json['ctrl_btncontinue_001'] as String? ?? "",
      ctrl_btnctaconnect_001: json['ctrl_btnctaconnect_001'] as String? ?? "",
      ctrl_btnchoose_product_001:
          json['ctrl_btnchoose_product_001'] as String? ?? "",
      ctrl_btndisagree_001: json['ctrl_btndisagree_001'] as String? ?? "",
      ctrl_btndisconnect_001: json['ctrl_btndisconnect_001'] as String? ?? "",
      ctrl_btndontallow_001: json['ctrl_btndontallow_001'] as String? ?? "",
      ctrl_btngotit_001: json['ctrl_btngotit_001'] as String? ?? "",
      ctrl_btngotovitalitymall_001:
          json['ctrl_btngotovitalitymall_001'] as String? ?? "",
      ctrl_btnhealth_001: json['ctrl_btnhealth_001'] as String? ?? "",
      ctrl_btnhide_001: json['ctrl_btnhide_001'] as String? ?? "",
      ctrl_btnhome_001: json['ctrl_btnhome_001'] as String? ?? "",
      ctrl_btniunderstand_001: json['ctrl_btniunderstand_001'] as String? ?? "",
      ctrl_btnlearnmore_001: json['ctrl_btnlearnmore_001'] as String? ?? "",
      ctrl_btnletsgo_001: json['ctrl_btnletsgo_001'] as String? ?? "",
      ctrl_btnlinknow_001: json['ctrl_btnlinknow_001'] as String? ?? "",
      ctrl_btnLoggingIn_001: json['ctrl_btnLoggingIn_001'] as String? ?? "",
      ctrl_btnlogin_001: json['ctrl_btnlogin_001'] as String? ?? "",
      ctrl_btnloginfinger_001: json['ctrl_btnloginfinger_001'] as String? ?? "",
      ctrl_btnLoginNewPw_001: json['ctrl_btnLoginNewPw_001'] as String? ?? "",
      ctrl_btnloginwith_001: json['ctrl_btnloginwith_001'] as String? ?? "",
      ctrl_btnLoginWithEmail_001:
          json['ctrl_btnLoginWithEmail_001'] as String? ?? "",
      ctrl_btnok_001: json['ctrl_btnok_001'] as String? ?? "",
      ctrl_btnokay_001: json['ctrl_btnokay_001'] as String? ?? "",
      ctrl_btnonnect_001: json['ctrl_btnonnect_001'] as String? ?? "",
      ctrl_btnprofile_001: json['ctrl_btnprofile_001'] as String? ?? "",
      ctrl_btnradiooff_001: json['ctrl_btnradiooff_001'] as String? ?? "",
      ctrl_btnrequestcode_001: json['ctrl_btnrequestcode_001'] as String? ?? "",
      ctrl_btnrequestingcode_001:
          json['ctrl_btnrequestingcode_001'] as String? ?? "",
      ctrl_btnrequestnow_001: json['ctrl_btnrequestnow_001'] as String? ?? "",
      ctrl_btnresetpassword_001:
          json['ctrl_btnresetpassword_001'] as String? ?? "",
      ctrl_btnreturnlogin_001: json['ctrl_btnreturnlogin_001'] as String? ?? "",
      ctrl_btnseeyousoon_001: json['ctrl_btnseeyousoon_001'] as String? ?? "",
      ctrl_btnrewards_001: json['ctrl_btnrewards_001'] as String? ?? "",
      ctrl_btnsavedetails_001: json['ctrl_btnsavedetails_001'] as String? ?? "",
      ctrl_btnsavepref_001: json['ctrl_btnsavepref_001'] as String? ?? "",
      ctrl_btnsavingdetails_001:
          json['ctrl_btnsavingdetails_001'] as String? ?? "",
      ctrl_btnsavingpref_001: json['ctrl_btnsavingpref_001'] as String? ?? "",
      ctrl_btnshow_001: json['ctrl_btnshow_001'] as String? ?? "",
      ctrl_btnskip_001: json['ctrl_btnskip_001'] as String? ?? "",
      ctrl_btntick_001: json['ctrl_btntick_001'] as String? ?? "",
      ctrl_btntryagain_001: json['ctrl_btntryagain_001'] as String? ?? "",
      ctrl_btnusepass_001: json['ctrl_btnusepass_001'] as String? ?? "",
      ctrl_btnusepasswrd_001: json['ctrl_btnusepasswrd_001'] as String? ?? "",
      ctrl_btnvector_001: json['ctrl_btnvector_001'] as String? ?? "",
      ctrl_btnverify_001: json['ctrl_btnverify_001'] as String? ?? "",
      ctrl_btnverifying_001: json['ctrl_btnverifying_001'] as String? ?? "",
      ctrl_linktxtmanuallyrefresh_001:
          json['ctrl_linktxtmanuallyrefresh_001'] as String? ?? "",
      ctrl_lnkforgotpass_001: json['ctrl_lnkforgotpass_001'] as String? ?? "",
      ctrl_lnkloginemail_001: json['ctrl_lnkloginemail_001'] as String? ?? "",
      ctrl_loginwithbiometric_001:
          json['ctrl_loginwithbiometric_001'] as String? ?? "",
      ctrl_lnkprivpolcy_001: json['ctrl_lnkprivpolcy_001'] as String? ?? "",
      ctrl_lnkresendactivcode_001:
          json['ctrl_lnkresendactivcode_001'] as String? ?? "",
      ctrl_login_forgotpassfeedback_btnloginnewpass_001:
          json['ctrl_login_forgotpassfeedback_btnloginnewpass_001']
                  as String? ??
              "",
      ctrl_physicalactivity_landing_btnactivategoal_001:
          json['ctrl_physicalactivity_landing_btnactivategoal_001']
                  as String? ??
              "",
      ctrl_txt_keep_email_001: json['ctrl_txt_keep_email_001'] as String? ?? "",
      ctrl_txt_use_facial_001: json['ctrl_txt_use_facial_001'] as String? ?? "",
      ctrl_txtaboutphysicalactivitygoal_001:
          json['ctrl_txtaboutphysicalactivitygoal_001'] as String? ?? "",
      ctrl_txtappsanddevices_001:
          json['ctrl_txtappsanddevices_001'] as String? ?? "",
      ctrl_txtgoalhistory_001: json['ctrl_txtgoalhistory_001'] as String? ?? "",
      ctrl_txthowitworks_001: json['ctrl_txthowitworks_001'] as String? ?? "",
      ctrl_txthowtocomplete_001:
          json['ctrl_txthowtocomplete_001'] as String? ?? "",
      ctrl_txthowtoearnpoints_001:
          json['ctrl_txthowtoearnpoints_001'] as String? ?? "",
      ctrl_txtlearnmore_001: json['ctrl_txtlearnmore_001'] as String? ?? "",
      ctrl_txtmanageappsdevices_001:
          json['ctrl_txtmanageappsdevices_001'] as String? ?? "",
      ctrl_txtmanagedevicesandapps_001:
          json['ctrl_txtmanagedevicesandapps_001'] as String? ?? "",
      ctrl_txtsamsungconnected_001:
          json['ctrl_txtsamsungconnected_001'] as String? ?? "",
      ctrl_txtsupport_001: json['ctrl_txtsupport_001'] as String? ?? "",
      ctrl_btnyeslogout_001: json['ctrl_btnyeslogout_001'] as String? ?? "",
      ctrl_btnnogoback_001: json['ctrl_btnnogoback_001'] as String? ?? "",
      dialog_body_BiometricsDisabled:
          json['dialog_body_BiometricsDisabled'] as String? ?? "",
      dialog_body_NotRecognised:
          json['dialog_body_NotRecognised'] as String? ?? "",
      dialog_body_TsCsRequired:
          json['dialog_body_TsCsRequired'] as String? ?? "",
      dialog_body_UnableToRegister:
          json['dialog_body_UnableToRegister'] as String? ?? "",
      dialog_btn1_: json['dialog_btn1_'] as String? ?? "",
      dialog_btn1_Ok: json['dialog_btn1_Ok'] as String? ?? "",
      dialog_btn1_Okay: json['dialog_btn1_Okay'] as String? ?? "",
      dialog_btn2_Cancel: json['dialog_btn2_Cancel'] as String? ?? "",
      dialog_errfinprntdis_hgconfirm_001:
          json['dialog_errfinprntdis_hgconfirm_001'] as String? ?? "",
      dialog_hd_BiometricsDisabled:
          json['dialog_hd_BiometricsDisabled'] as String? ?? "",
      dialog_hd_fingerAuthentication:
          json['dialog_hd_fingerAuthentication'] as String? ?? "",
      dialog_hd_fingerFacial: json['dialog_hd_fingerFacial'] as String? ?? "",
      dialog_hd_touch: json['dialog_hd_touch'] as String? ?? "",
      dialog_hd_TsCsRequired: json['dialog_hd_TsCsRequired'] as String? ?? "",
      dialog_hd_UnableToRegister:
          json['dialog_hd_UnableToRegister'] as String? ?? "",
      dialog_hg_Biometrics: json['dialog_hg_Biometrics'] as String? ?? "",
      footnote_ManagePermissions:
          json['footnote_ManagePermissions'] as String? ?? "",
      guide_Privacy: json['guide_Privacy'] as String? ?? "",
      guide_supportQ2: json['guide_supportQ2'] as String? ?? "",
      hd_connectApp: json['hd_connectApp'] as String? ?? "",
      hd_doB: json['hd_doB'] as String? ?? "",
      login_dobafter_txthdenterdob_001:
          json['login_dobafter_txthdenterdob_001'] as String? ?? "",
      hd_emptyState_noPromotion:
          json['hd_emptyState_noPromotion'] as String? ?? "",
      hd_justOneMoreStep: json['hd_justOneMoreStep'] as String? ?? "",
      hd_TsCs: json['hd_TsCs'] as String? ?? "",
      hg_PersonalisePreferences:
          json['hg_PersonalisePreferences'] as String? ?? "",
      hg_PrivacyPolicy: json['hg_PrivacyPolicy'] as String? ?? "",
      input_: json['input_'] as String? ?? "",
      label_App: json['label_App'] as String? ?? "",
      label_insurerVitalty: json['label_insurerVitalty'] as String? ?? "",
      label_mobilePhone: json['label_mobilePhone'] as String? ?? "",
      label_or: json['label_or'] as String? ?? "",
      label_PWReq_01: json['label_PWReq_01'] as String? ?? "",
      label_PWReq_02: json['label_PWReq_02'] as String? ?? "",
      label_PWReq_03: json['label_PWReq_03'] as String? ?? "",
      label_PWReq_04: json['label_PWReq_04'] as String? ?? "",
      label_PWReq_05: json['label_PWReq_05'] as String? ?? "",
      label_Step: json['label_Step'] as String? ?? "",
      label_wearableDevice: json['label_wearableDevice'] as String? ?? "",
      labelRight: json['labelRight'] as String? ?? "",
      login_dialog_bdface_001: json['login_dialog_bdface_001'] as String? ?? "",
      login_dialog_bdfingerprnt_001:
          json['login_dialog_bdfingerprnt_001'] as String? ?? "",
      login_dialog_bdincorrectpwd_001:
          json['login_dialog_bdincorrectpwd_001'] as String? ?? "",
      login_dialog_bdtouchfingerprnt_001:
          json['login_dialog_bdtouchfingerprnt_001'] as String? ?? "",
      login_dialog_hdface_001: json['login_dialog_hdface_001'] as String? ?? "",
      login_dialog_hdfingerprnt_001:
          json['login_dialog_hdfingerprnt_001'] as String? ?? "",
      login_dialog_hdincorrectpwd_001:
          json['login_dialog_hdincorrectpwd_001'] as String? ?? "",
      login_dialog_hdtouchfingerprnt_001:
          json['login_dialog_hdtouchfingerprnt_001'] as String? ?? "",
      login_forgotpassfeedback_txtbodygotmail_001:
          json['login_forgotpassfeedback_txtbodygotmail_001'] as String? ?? "",
      login_memnotactive_txthdmemnotactive_001:
          json['login_memnotactive_txthdmemnotactive_001'] as String? ?? "",
      login_txtand_001: json['login_txtand_001'] as String? ?? "",
      login_txtbdyaccativ_002: json['login_txtbdyaccativ_002'] as String? ?? "",
      login_txtbdyacccancelled_001:
          json['login_txtbdyacccancelled_001'] as String? ?? "",
      login_txtbylogginin_001: json['login_txtbylogginin_001'] as String? ?? "",
      login_txtdob_error_001: json['login_txtdob_error_001'] as String? ?? "",
      login_txthdwelcomebk_001:
          json['login_txthdwelcomebk_001'] as String? ?? "",
      login_txtpassword_error_001:
          json['login_txtpassword_error_001'] as String? ?? "",
      login_txtrememberme_001: json['login_txtrememberme_001'] as String? ?? "",
      login_txtturnoffrememberme_001:
          json['login_txtturnoffrememberme_001'] as String? ?? "",
      login_txtyouvegotmail_001:
          json['login_txtyouvegotmail_001'] as String? ?? "",
      loginScreenTitle: json['loginScreenTitle'] as String? ?? "",
      phd_MembershipCancelled: json['phd_MembershipCancelled'] as String? ?? "",
      phd_supportSection: json['phd_supportSection'] as String? ?? "",
      physicalactivity_alert_txtgoalmet_001:
          json['physicalactivity_alert_txtgoalmet_001'] as String? ?? "",
      physicalactivity_dialogalert_txtbodydeviceapplink_001:
          json['physicalactivity_dialogalert_txtbodydeviceapplink_001']
                  as String? ??
              "",
      physicalactivity_dialogalert_txthddeviceapplink_001:
          json['physicalactivity_dialogalert_txthddeviceapplink_001']
                  as String? ??
              "",
      physicalactivity_goalhistory_txtgoalhistory_001:
          json['physicalactivity_goalhistory_txtgoalhistory_001'] as String? ??
              "",
      physicalactivity_landing_lvautotracked_001:
          json['physicalactivity_landing_lvautotracked_001'] as String? ?? "",
      physicalactivity_landing_lvnewgoal_001:
          json['physicalactivity_landing_lvnewgoal_001'] as String? ?? "",
      physicalactivity_landing_lvreward_001:
          json['physicalactivity_landing_lvreward_001'] as String? ?? "",
      physicalactivity_landing_txtbodyhowtocomplete_001:
          json['physicalactivity_landing_txtbodyhowtocomplete_001']
                  as String? ??
              "",
      physicalactivity_landing_txtbodytip_001:
          json['physicalactivity_landing_txtbodytip_001'] as String? ?? "",
      physicalactivity_landing_txtbodytip_002:
          json['physicalactivity_landing_txtbodytip_002'] as String? ?? "",
      physicalactivity_landing_txtbodytip_003:
          json['physicalactivity_landing_txtbodytip_003'] as String? ?? "",
      physicalactivity_landing_txtbodywhyimportant_001:
          json['physicalactivity_landing_txtbodywhyimportant_001'] as String? ??
              "",
      physicalactivity_landing_txtbodywhyimportant_002:
          json['physicalactivity_landing_txtbodywhyimportant_002'] as String? ??
              "",
      physicalactivity_landing_txtbodywhyimportant_003:
          json['physicalactivity_landing_txtbodywhyimportant_003'] as String? ??
              "",
      physicalactivity_landing_txtgoalname_001:
          json['physicalactivity_landing_txtgoalname_001'] as String? ?? "",
      physicalactivity_modalactivated_txtactivatedgoal_001:
          json['physicalactivity_modalactivated_txtactivatedgoal_001']
                  as String? ??
              "",
      physicalactivity_modalactivated_txtactivatedgoal_002:
          json['physicalactivity_modalactivated_txtactivatedgoal_002']
                  as String? ??
              "",
      physicalactivity_modalactivated_txtyourfirststartgoal_001:
          json['physicalactivity_modalactivated_txtyourfirststartgoal_001']
                  as String? ??
              "",
      physicalactivity_modalactivated_txtyourfirststartgoal_002:
          json['physicalactivity_modalactivated_txtyourfirststartgoal_002']
                  as String? ??
              "",
      physicalactivity_weeklygoals_txtearnreward_001:
          json['physicalactivity_weeklygoals_txtearnreward_001'] as String? ??
              "",
      physicalactivity_weeklygoals_txtweeklygoals_001:
          json['physicalactivity_weeklygoals_txtweeklygoals_001'] as String? ??
              "",
      reg_accactivtd_txtbdyaccativ_001:
          json['reg_accactivtd_txtbdyaccativ_001'] as String? ?? "",
      reg_accactivtd_txtbdyaccativ_002:
          json['reg_accactivtd_txtbdyaccativ_002'] as String? ?? "",
      reg_accactivtd_txthgaccactiv_001:
          json['reg_accactivtd_txthgaccactiv_001'] as String? ?? "",
      reg_activation_confirmpass_001:
          json['reg_activation_confirmpass_001'] as String? ?? "",
      reg_activation_input_001:
          json['reg_activation_input_001'] as String? ?? "",
      reg_activation_step3_txtsetlogin_001:
          json['reg_activation_step3_txtsetlogin_001'] as String? ?? "",
      reg_activation_txtconfirmpass_001:
          json['reg_activation_txtconfirmpass_001'] as String? ?? "",
      reg_activation_txtcreatepass_001:
          json['reg_activation_txtcreatepass_001'] as String? ?? "",
      reg_codesentmsg_001: json['reg_codesentmsg_001'] as String? ?? "",
      reg_notif_bdpushnotif_001:
          json['reg_notif_bdpushnotif_001'] as String? ?? "",
      reg_notif_hdpushnotif_001:
          json['reg_notif_hdpushnotif_001'] as String? ?? "",
      reg_privacypolicy_txtbdprivpol_001:
          json['reg_privacypolicy_txtbdprivpol_001'] as String? ?? "",
      reg_privacypolicy_txthgprivpol_001:
          json['reg_privacypolicy_txthgprivpol_001'] as String? ?? "",
      reg_step1_002: json['reg_step1_002'] as String? ?? "",
      reg_step1_input_001: json['reg_step1_input_001'] as String? ?? "",
      reg_step1_txtemailbody_001:
          json['reg_step1_txtemailbody_001'] as String? ?? "",
      reg_step1_txtgetcode_001:
          json['reg_step1_txtgetcode_001'] as String? ?? "",
      reg_step1_txtwelcomvit_001:
          json['reg_step1_txtwelcomvit_001'] as String? ?? "",
      reg_step2_002: json['reg_step2_002'] as String? ?? "",
      reg_step2_enteractiv_001:
          json['reg_step2_enteractiv_001'] as String? ?? "",
      reg_step2_input_001: json['reg_step2_input_001'] as String? ?? "",
      reg_step2_txtenteractiv_001:
          json['reg_step2_txtenteractiv_001'] as String? ?? "",
      reg_step2_txtsecacc_001: json['reg_step2_txtsecacc_001'] as String? ?? "",
      reg_step2_txtverifyid_001:
          json['reg_step2_txtverifyid_001'] as String? ?? "",
      reg_step2_verifyid_001: json['reg_step2_verifyid_001'] as String? ?? "",
      reg_step2txtcodesentmsg_001:
          json['reg_step2txtcodesentmsg_001'] as String? ?? "",
      reg_step3_003: json['reg_step3_003'] as String? ?? "",
      reg_step3_confirmpass_001:
          json['reg_step3_confirmpass_001'] as String? ?? "",
      reg_step3_createpass_001:
          json['reg_step3_createpass_001'] as String? ?? "",
      reg_step3_input_001: json['reg_step3_input_001'] as String? ?? "",
      reg_step3_secacc_001: json['reg_step3_secacc_001'] as String? ?? "",
      reg_step3_setlogin_001: json['reg_step3_setlogin_001'] as String? ?? "",
      reg_step3_txtcreatepass_001:
          json['reg_step3_txtcreatepass_001'] as String? ?? "",
      reg_step3_txtinput_001: json['reg_step3_txtinput_001'] as String? ?? "",
      reg_step3_txtsecacc_001: json['reg_step3_txtsecacc_001'] as String? ?? "",
      reg_step3_txtsetlogin_001:
          json['reg_step3_txtsetlogin_001'] as String? ?? "",
      reg_step4_txtguideprivacy_001:
          json['reg_step4_txtguideprivacy_001'] as String? ?? "",
      reg_step4_txtjustonestep_001:
          json['reg_step4_txtjustonestep_001'] as String? ?? "",
      reg_step4_txtpersonalise_001:
          json['reg_step4_txtpersonalise_001'] as String? ?? "",
      reg_termsconditions_txtbdtcs_001:
          json['reg_termsconditions_txtbdtcs_001'] as String? ?? "",
      reg_termsconditions_txthdtcs_001:
          json['reg_termsconditions_txthdtcs_001'] as String? ?? "",
      reg_txtstep1_001: json['reg_txtstep1_001'] as String? ?? "",
      reg_welcome_findoutmore_001:
          json['reg_welcome_findoutmore_001'] as String? ?? "",
      reg_welcome_insupolicy_001:
          json['reg_welcome_insupolicy_001'] as String? ?? "",
      reg_welcome_needqualify_001:
          json['reg_welcome_needqualify_001'] as String? ?? "",
      reg_welcome_policyto_001:
          json['reg_welcome_policyto_001'] as String? ?? "",
      reg_welcome_txthealthliv_001:
          json['reg_welcome_txthealthliv_001'] as String? ?? "",
      reg_welcome_txtinsupolicy_001:
          json['reg_welcome_txtinsupolicy_001'] as String? ?? "",
      sample123: json['sample123'] as String? ?? "",
      sectionHd_lastmembershipyear:
          json['sectionHd_lastmembershipyear'] as String? ?? "",
      sectionHd_promotion: json['sectionHd_promotion'] as String? ?? "",
      txt_footnoteVitalityprogramme_001:
          json['txt_footnoteVitalityprogramme_001'] as String? ?? "",
      txt1spin_001: json['txt1spin_001'] as String? ?? "",
      txtaccount_001: json['txtaccount_001'] as String? ?? "",
      txtactivateaccountlogin_001:
          json['txtactivateaccountlogin_001'] as String? ?? "",
      txtactivatecode_001: json['txtactivatecode_001'] as String? ?? "",
      txtallowedtoread_001: json['txtallowedtoread_001'] as String? ?? "",
      txtalreadyacc_001: json['txtalreadyacc_001'] as String? ?? "",
      txtalreadyacc_002: json['txtalreadyacc_002'] as String? ?? "",
      txtapp_001: json['txtapp_001'] as String? ?? "",
      txtappcrashsettings_001: json['txtappcrashsettings_001'] as String? ?? "",
      txtappdevices_001: json['txtappdevices_001'] as String? ?? "",
      txtapplehealth_001: json['txtapplehealth_001'] as String? ?? "",
      txtapplehealthh_001: json['txtapplehealthh_001'] as String? ?? "",
      txtappsanddevicesfooter: json['txtappsanddevicesfooter'] as String? ?? "",
      txtappver_001: json['txtappver_001'] as String? ?? "",
      txtareyousureyouwanttodisconnect_001:
          json['txtareyousureyouwanttodisconnect_001'] as String? ?? "",
      txtauthcodesent_001: json['txtauthcodesent_001'] as String? ?? "",
      txtautosync_001: json['txtautosync_001'] as String? ?? "",
      txtautosyncautomaticallythroughouttheda_001:
          json['txtautosyncautomaticallythroughouttheda_001'] as String? ?? "",
      txtautotracked_001: json['txtautotracked_001'] as String? ?? "",
      txtavailableagaindate_001:
          json['txtavailableagaindate_001'] as String? ?? "",
      txtbdconncterr_001: json['txtbdconncterr_001'] as String? ?? "",
      txtbdincrrdetail_001: json['txtbdincrrdetail_001'] as String? ?? "",
      txtbdonestepaway_001: json['txtbdonestepaway_001'] as String? ?? "",
      txtbdonestepaway_002: json['txtbdonestepaway_002'] as String? ?? "",
      txtbdprobnerr_001: json['txtbdprobnerr_001'] as String? ?? "",
      txtbdsomewrngerr_001: json['txtbdsomewrngerr_001'] as String? ?? "",
      txtbdtcrequired_001: json['txtbdtcrequired_001'] as String? ?? "",
      txtbdunknwnerr_001: json['txtbdunknwnerr_001'] as String? ?? "",
      txtbdyacccancelled_001: json['txtbdyacccancelled_001'] as String? ?? "",
      txtbloodglucose_001: json['txtbloodglucose_001'] as String? ?? "",
      txtbloodpressure_001: json['txtbloodpressure_001'] as String? ?? "",
      txtbodyapperror_001: json['txtbodyapperror_001'] as String? ?? "",
      txtbodyconnectcompatibleapps_001:
          json['txtbodyconnectcompatibleapps_001'] as String? ?? "",
      txtbodyconnectedmsg_001: json['txtbodyconnectedmsg_001'] as String? ?? "",
      txtbodyforgotpass_001: json['txtbodyforgotpass_001'] as String? ?? "",
      txtbodyforgotpass_002: json['txtbodyforgotpass_002'] as String? ?? "",
      txtbodyhowtocomplete_001:
          json['txtbodyhowtocomplete_001'] as String? ?? "",
      txtbodymassindex_001: json['txtbodymassindex_001'] as String? ?? "",
      txtbodynohistoryavailable_001:
          json['txtbodynohistoryavailable_001'] as String? ?? "",
      txtbodypromofitnesscard_001:
          json['txtbodypromofitnesscard_001'] as String? ?? "",
      txtbodyresetpass_001: json['txtbodyresetpass_001'] as String? ?? "",
      txtbodysharedata_001: json['txtbodysharedata_001'] as String? ?? "",
      txtbodysharedata_garmin_001:
          json['txtbodysharedata_garmin_001'] as String? ?? "",
      txtbtnletsgo_001: json['txtbtnletsgo_001'] as String? ?? "",
      txttips_drink_001: json['txttips_drink_001'] as String? ?? "",
      txttips_drink_content_001:
          json['txttips_drink_content_001'] as String? ?? "",
      txttips_smoking_001: json['txttips_smoking_001'] as String? ?? "",
      txttips_smoking_content_001:
          json['txttips_smoking_content_001'] as String? ?? "",
      txttips_smoking_content_002:
          json['txttips_smoking_content_002'] as String? ?? "",
      txttipstomanagecholesterol_001:
          json['txttipstomanagecholesterol_001'] as String? ?? "",
      txttips_cardio_001: json['txttips_cardio_001'] as String? ?? "",
      txttips_cardio_content_001:
          json['txttips_cardio_content_001'] as String? ?? "",
      txtcardio_guide_001: json['txtcardio_guide_001'] as String? ?? "",
      txtcardio_intro_001: json['txtcardio_intro_001'] as String? ?? "",
      txtcardio_intro_002: json['txtcardio_intro_002'] as String? ?? "",
      txtcardio_intro_003: json['txtcardio_intro_003'] as String? ?? "",
      txtcardio_intro_004: json['txtcardio_intro_004'] as String? ?? "",
      txtconsulthealthcareprovider_001:
          json['txtconsulthealthcareprovider_001'] as String? ?? "",
      txtconsulthealthcareprovider_content_001:
          json['txtconsulthealthcareprovider_content_001'] as String? ?? "",
      txtconsulthealthcareprovider_content_002:
          json['txtconsulthealthcareprovider_content_002'] as String? ?? "",
      txtsmoking_guide_001: json['txtsmoking_guide_001'] as String? ?? "",
      txtsmoking_intro_001: json['txtsmoking_intro_001'] as String? ?? "",
      txtsmoking_intro_002: json['txtsmoking_intro_002'] as String? ?? "",
      txtsmoking_intro_003: json['txtsmoking_intro_003'] as String? ?? "",
      txtdrink_guide_001: json['txtdrink_guide_001'] as String? ?? "",
      txtdrink_intro_001: json['txtdrink_intro_001'] as String? ?? "",
      txtdrink_intro_002: json['txtdrink_intro_002'] as String? ?? "",
      txtdrink_intro_003: json['txtdrink_intro_003'] as String? ?? "",
      txtdrinkslowly_001: json['txtdrinkslowly_001'] as String? ?? "",
      txtdrinkslowly_content_001:
          json['txtdrinkslowly_content_001'] as String? ?? "",
      txtintensity_001: json['txtintensity_001'] as String? ?? "",
      txtintensity_content_001:
          json['txtintensity_content_001'] as String? ?? "",
      txtlimitdrinking_001: json['txtlimitdrinking_001'] as String? ?? "",
      txtlimitdrinking_content_001:
          json['txtlimitdrinking_content_001'] as String? ?? "",
      txtlimittoomuchatonce_001:
          json['txtlimittoomuchatonce_001'] as String? ?? "",
      txtlimittoomuchatonce_content_001:
          json['txtlimittoomuchatonce_content_001'] as String? ?? "",
      txtquitedate_001: json['txtquitedate_001'] as String? ?? "",
      txtquitedate_content_001:
          json['txtquitedate_content_001'] as String? ?? "",
      txt8020rule_001: json['txt8020rule_001'] as String? ?? "",
      txt8020rule_content_001: json['txt8020rule_content_001'] as String? ?? "",
      txt8020rule_content_002: json['txt8020rule_content_002'] as String? ?? "",
      txtplanahead_001: json['txtplanahead_001'] as String? ?? "",
      txtplanahead_content_001:
          json['txtplanahead_content_001'] as String? ?? "",
      txtlistreasons_001: json['txtlistreasons_001'] as String? ?? "",
      txtlistreasons_content_001:
          json['txtlistreasons_content_001'] as String? ?? "",
      txtaddtime_001: json['txtaddtime_001'] as String? ?? "",
      txtaddtime_content_001: json['txtaddtime_content_001'] as String? ?? "",
      txtaddtime_content_002: json['txtaddtime_content_002'] as String? ?? "",
      txtdontdrinkanddrive_001:
          json['txtdontdrinkanddrive_001'] as String? ?? "",
      txtdontdrinkanddrive_content_001:
          json['txtdontdrinkanddrive_content_001'] as String? ?? "",
      txtgathersupport_001: json['txtgathersupport_001'] as String? ?? "",
      txtgathersupport_content_001:
          json['txtgathersupport_content_001'] as String? ?? "",
      txtplantodealwithurges_001:
          json['txtplantodealwithurges_001'] as String? ?? "",
      txtplantodealwithurges_content_001:
          json['txtplantodealwithurges_content_001'] as String? ?? "",
      txtslowlyadd_001: json['txtslowlyadd_001'] as String? ?? "",
      txtslowlyadd_content_001:
          json['txtslowlyadd_content_001'] as String? ?? "",
      txtexerciseregularly_001:
          json['txtexerciseregularly_001'] as String? ?? "",
      txtexmoreoften_001: json['txtexmoreoften_001'] as String? ?? "",
      txtexmoreoften_content_001:
          json['txtexmoreoften_content_001'] as String? ?? "",
      txtmedicationsupport_001:
          json['txtmedicationsupport_001'] as String? ?? "",
      txtmedicationsupport_content_001:
          json['txtmedicationsupport_content_001'] as String? ?? "",
      txtcardiovascular_001: json['txtcardiovascular_001'] as String? ?? "",
      txtchildhood_001: json['txtchildhood_001'] as String? ?? "",
      txtcholesterol_001: json['txtcholesterol_001'] as String? ?? "",
      txtchoosereward: json['txtchoosereward'] as String? ?? "",
      txtchooseyourgiftcard_001:
          json['txtchooseyourgiftcard_001'] as String? ?? "",
      txtcoins: json['txtcoins'] as String? ?? "",
      txtcolonoscopy_001: json['txtcolonoscopy_001'] as String? ?? "",
      txtconnectaccount_001: json['txtconnectaccount_001'] as String? ?? "",
      txtconnectapp_001: json['txtconnectapp_001'] as String? ?? "",
      txtconnectionerror_001: json['txtconnectionerror_001'] as String? ?? "",
      txtconnectyougarmintoearnvitalitypoint_001:
          json['txtconnectyougarmintoearnvitalitypoint_001'] as String? ?? "",
      txtconnectyourappordevice_001:
          json['txtconnectyourappordevice_001'] as String? ?? "",
      txtdentalcheckups_001: json['txtdentalcheckups_001'] as String? ?? "",
      txtdisclaimer_001: json['txtdisclaimer_001'] as String? ?? "",
      txtdob_001: json['txtdob_001'] as String? ?? "",
      txtdod_001: json['txtdod_001'] as String? ?? "",
      txtearncoinsactivation: json['txtearncoinsactivation'] as String? ?? "",
      txtearncoinsreward_001: json['txtearncoinsreward_001'] as String? ?? "",
      txtearngiftcardreward_001:
          json['txtearngiftcardreward_001'] as String? ?? "",
      txtearnspinreward_001: json['txtearnspinreward_001'] as String? ?? "",
      txtemailadd_001: json['txtemailadd_001'] as String? ?? "",
      txtemailadd_001l_: json['txtemailadd_001l_'] as String? ?? "",
      txtemailerrsup_001: json['txtemailerrsup_001'] as String? ?? "",
      txtemailsetting_001: json['txtemailsetting_001'] as String? ?? "",
      txtenteremail_001: json['txtenteremail_001'] as String? ?? "",
      txtewrngmailadd_001: json['txtewrngmailadd_001'] as String? ?? "",
      txtexpiresinxdays_001: json['txtexpiresinxdays_001'] as String? ?? "",
      txtfastingglucose_hba1c_001:
          json['txtfastingglucose_hba1c_001'] as String? ?? "",
      txtfirsttimelog_001: json['txtfirsttimelog_001'] as String? ?? "",
      txtfirsttimelogin_001: json['txtfirsttimelogin_001'] as String? ?? "",
      txtfitbit_001: json['txtfitbit_001'] as String? ?? "",
      txtflu_001: json['txtflu_001'] as String? ?? "",
      txtfobt_001: json['txtfobt_001'] as String? ?? "",
      txtfooter_005: json['txtfooter_005'] as String? ?? "",
      txtgarmin_001: json['txtgarmin_001'] as String? ?? "",
      txtgastriccancer_001: json['txtgastriccancer_001'] as String? ?? "",
      txtgetanewgoaleveryday_001:
          json['txtgetanewgoaleveryday_001'] as String? ?? "",
      txtgetreadytostartmoving_001:
          json['txtgetreadytostartmoving_001'] as String? ?? "",
      txtglaucoma_001: json['txtglaucoma_001'] as String? ?? "",
      txtgoaldates_001: json['txtgoaldates_001'] as String? ?? "",
      txtgooglefit_001: json['txtgooglefit_001'] as String? ?? "",
      txtguideprivacy_001: json['txtguideprivacy_001'] as String? ?? "",
      txthbA1c_001: json['txthbA1c_001'] as String? ?? "",
      txthdapperror_001: json['txthdapperror_001'] as String? ?? "",
      txthdconncterr_001: json['txthdconncterr_001'] as String? ?? "",
      txthdconnectcompatibleapps_001:
          json['txthdconnectcompatibleapps_001'] as String? ?? "",
      txthdforgotpass_001: json['txthdforgotpass_001'] as String? ?? "",
      txthdincrrdetail_001: json['txthdincrrdetail_001'] as String? ?? "",
      txthdonestepaway_001: json['txthdonestepaway_001'] as String? ?? "",
      txthdprobnerr_001: json['txthdprobnerr_001'] as String? ?? "",
      txthdpromofitnesscard_001:
          json['txthdpromofitnesscard_001'] as String? ?? "",
      txthdsharedata_001: json['txthdsharedata_001'] as String? ?? "",
      txthdsomewrngerr_001: json['txthdsomewrngerr_001'] as String? ?? "",
      txthdtcrequired_001: json['txthdtcrequired_001'] as String? ?? "",
      txthdunknwnerr_001: json['txthdunknwnerr_001'] as String? ?? "",
      txthdwelcomebk_001: json['txthdwelcomebk_001'] as String? ?? "",
      txthealthresults_001: json['txthealthresults_001'] as String? ?? "",
      txthello_001: json['txthello_001'] as String? ?? "",
      txthepatitisb_001: json['txthepatitisb_001'] as String? ?? "",
      txthiv_001: json['txthiv_001'] as String? ?? "",
      txthowtocomplete_001: json['txthowtocomplete_001'] as String? ?? "",
      txthpv_001: json['txthpv_001'] as String? ?? "",
      txtincludefooter_001: json['txtincludefooter_001'] as String? ?? "",
      txtinsurervitality_001: json['txtinsurervitality_001'] as String? ?? "",
      txtjustonestep_001: json['txtjustonestep_001'] as String? ?? "",
      txtkeepemailpop_001: json['txtkeepemailpop_001'] as String? ?? "",
      txtlastsyncdate_001: json['txtlastsyncdate_001'] as String? ?? "",
      txtlblemailaddress_001: json['txtlblemailaddress_001'] as String? ?? "",
      txtlblpassword_001: json['txtlblpassword_001'] as String? ?? "",
      txtlivercancer_001: json['txtlivercancer_001'] as String? ?? "",
      txtlungcancer_001: json['txtlungcancer_001'] as String? ?? "",
      txtmaintenanceerror_001: json['txtmaintenanceerror_001'] as String? ?? "",
      txtmammogram_001: json['txtmammogram_001'] as String? ?? "",
      txtmanperms_001: json['txtmanperms_001'] as String? ?? "",
      txtmeningococcal_001: json['txtmeningococcal_001'] as String? ?? "",
      txtmobilephone_001: json['txtmobilephone_001'] as String? ?? "",
      txtneedhelp_001: json['txtneedhelp_001'] as String? ?? "",
      txtnewgoaldates_001: json['txtnewgoaldates_001'] as String? ?? "",
      txtnotconnected_001: json['txtnotconnected_001'] as String? ?? "",
      txtonlyqualifyingphysicalactivitygoal_001:
          json['txtonlyqualifyingphysicalactivitygoal_001'] as String? ?? "",
      txtovariancancer_001: json['txtovariancancer_001'] as String? ?? "",
      txtpapsmear_001: json['txtpapsmear_001'] as String? ?? "",
      txtpassreqnotmet_001: json['txtpassreqnotmet_001'] as String? ?? "",
      txtpassword_001: json['txtpassword_001'] as String? ?? "",
      txtpermissions_001: json['txtpermissions_001'] as String? ?? "",
      txtpersonalise_001: json['txtpersonalise_001'] as String? ?? "",
      txtphysicalactivitygoal_001:
          json['txtphysicalactivitygoal_001'] as String? ?? "",
      txtpleasere_enteravaliddateofbirth_001:
          json['txtpleasere_enteravaliddateofbirth_001'] as String? ?? "",
      txtpneumococcal_001: json['txtpneumococcal_001'] as String? ?? "",
      txtpoints_001: json['txtpoints_001'] as String? ?? "",
      txtpointsareavailableeverymembershipyear_001:
          json['txtpointsareavailableeverymembershipyear_001'] as String? ?? "",
      txtpointsreflectnote_001:
          json['txtpointsreflectnote_001'] as String? ?? "",
      txtpointsreflecttime_001:
          json['txtpointsreflecttime_001'] as String? ?? "",
      txtpointstwiceamembershipyearmonthsapart_001:
          json['txtpointstwiceamembershipyearmonthsapart_001'] as String? ?? "",
      txtpolar_001: json['txtpolar_001'] as String? ?? "",
      txtPTS_001: json['txtPTS_001'] as String? ?? "",
      txtqualifyingqeventsandpoints_001:
          json['txtqualifyingqeventsandpoints_001'] as String? ?? "",
      txtrecomcalories_001: json['txtrecomcalories_001'] as String? ?? "",
      txtrecomheartrate_001: json['txtrecomheartrate_001'] as String? ?? "",
      txtrecomsteps_001: json['txtrecomsteps_001'] as String? ?? "",
      txtsamesignupdetails_001:
          json['txtsamesignupdetails_001'] as String? ?? "",
      txtsamsunghealth_001: json['txtsamsunghealth_001'] as String? ?? "",
      txtsamsungsupport_001: json['txtsamsungsupport_001'] as String? ?? "",
      txtsaveemail_001: json['txtsaveemail_001'] as String? ?? "",
      txtscreenings_001: json['txtscreenings_001'] as String? ?? "",
      txtscreeningsandvaccinations_001:
          json['txtscreeningsandvaccinations_001'] as String? ?? "",
      txtscreeningspagebodycopy_001:
          json['txtscreeningspagebodycopy_001'] as String? ?? "",
      txtsharescreeninteract_001:
          json['txtsharescreeninteract_001'] as String? ?? "",
      txtshareyourdatawithus_001:
          json['txtshareyourdatawithus_001'] as String? ?? "",
      txtshow_001: json['txtshow_001'] as String? ?? "",
      txtskincancer_001: json['txtskincancer_001'] as String? ?? "",
      txtspamguide_001: json['txtspamguide_001'] as String? ?? "",
      txtspinrewardearned_001: json['txtspinrewardearned_001'] as String? ?? "",
      txtstrava_001: json['txtstrava_001'] as String? ?? "",
      txtsubmitproof_001: json['txtsubmitproof_001'] as String? ?? "",
      txtsuccesfullyconnected_001:
          json['txtsuccesfullyconnected_001'] as String? ?? "",
      txtsuunto_001: json['txtsuunto_001'] as String? ?? "",
      txtsyncyourdevicetoyourgarminaccount_001:
          json['txtsyncyourdevicetoyourgarminaccount_001'] as String? ?? "",
      txttcprivacy_001: json['txttcprivacy_001'] as String? ?? "",
      txttermcnd_001: json['txttermcnd_001'] as String? ?? "",
      txttncprivacy_001: json['txttncprivacy_001'] as String? ?? "",
      txttypesofactivities_001:
          json['txttypesofactivities_001'] as String? ?? "",
      txtunabletoconnectyoursamsunghealthapp_001:
          json['txtunabletoconnectyoursamsunghealthapp_001'] as String? ?? "",
      txturinaryprotein_001: json['txturinaryprotein_001'] as String? ?? "",
      txtusefinger_001: json['txtusefinger_001'] as String? ?? "",
      txtvaccinations_001: json['txtvaccinations_001'] as String? ?? "",
      txtwearabledevice_001: json['txtwearabledevice_001'] as String? ?? "",
      txtweight_001: json['txtweight_001'] as String? ?? "",
      txtwhyimportant_001: json['txtwhyimportant_001'] as String? ?? "",
      txtwhythisisimportant_001:
          json['txtwhythisisimportant_001'] as String? ?? "",
      txtwithings_001: json['txtwithings_001'] as String? ?? "",
      txtyoudiditnowspinit_001:
          json['txtyoudiditnowspinit_001'] as String? ?? "",
      txtyourenowconnected_001:
          json['txtyourenowconnected_001'] as String? ?? "",
      txtyourfirstgoalstartsonmonday_001:
          json['txtyourfirstgoalstartsonmonday_001'] as String? ?? "",
      txtyourspinexpiresinxdays_001:
          json['txtyourspinexpiresinxdays_001'] as String? ?? "",
      txtyourweeklypointstargetstartsondate_001:
          json['txtyourweeklypointstargetstartsondate_001'] as String? ?? "",
      txtzoster_001: json['txtzoster_001'] as String? ?? "",
      view_reg_txtemailbody_001:
          json['view_reg_txtemailbody_001'] as String? ?? "",
      welcome_screen_1: json['welcome_screen_1'] as String? ?? "",
      txtjustonestep_closer_001:
          json['txtjustonestep_closer_001'] as String? ?? "",
      txttncprivacy_agree_001: json['txttncprivacy_agree_001'] as String? ?? "",
      reg_step4_txtprivacyidentified_001:
          json['reg_step4_txtprivacyidentified_001'] as String? ?? "",
      txtmakesurepassmatch_001:
          json['txtmakesurepassmatch_001'] as String? ?? "",
      txtagreetnc_001: json['txtagreetnc_001'] as String? ?? "",
      txtdisagreelogout_001: json['txtdisagreelogout_001'] as String? ?? "",
      txtnopointsearningactivity_001:
          json['txtnopointsearningactivity_001'] as String? ?? "",
      txtniceyouhaveearnedfidcoins_001:
          json['txtniceyouhaveearnedfidcoins_001'] as String? ?? "",
      physicalactivity_modalactivated_txtyourfirststartgoal_003:
          json['physicalactivity_modalactivated_txtyourfirststartgoal_003']
                  as String? ??
              "",
      txt1spin_002: json['txt1spin_002'] as String? ?? "",
      txtactivatedyouhaveveearnedfidcoins_001:
          json['txtactivatedyouhaveveearnedfidcoins_001'] as String? ?? "",
      txtactivatedyouhaveveearnedfidcoins_002:
          json['txtactivatedyouhaveveearnedfidcoins_002'] as String? ?? "",
      txtpointsreflectnote_002:
          json['txtpointsreflectnote_002'] as String? ?? "",
      txtswiptetospin_001: json['txtswiptetospin_001'] as String? ?? "",
      ctrl_txtwhatsonthewheel_001:
          json['ctrl_txtwhatsonthewheel_001'] as String? ?? "",
      txtxcoins_001: json['txtxcoins_001'] as String? ?? "",
      txtxgiftcards_001: json['txtxgiftcards_001'] as String? ?? "",
      txtxgiftcards_002: json['txtxgiftcards_002'] as String? ?? "",
      txtxgiftcards_003: json['txtxgiftcards_003'] as String? ?? "",
      ctrl_txtgotit_001: json['ctrl_txtgotit_001'] as String? ?? "",
      txtsubtextonreward_001: json['txtsubtextonreward_001'] as String? ?? "",
      txtsubtextonreward_002: json['txtsubtextonreward_002'] as String? ?? "",
      txtsubtextonreward_004: json['txtsubtextonreward_004'] as String? ?? "",
      txtsubtextonreward_003: json['txtsubtextonreward_003'] as String? ?? "",
      txtwonreward_001: json['txtwonreward_001'] as String? ?? "",
      txtwonreward_002: json['txtwonreward_002'] as String? ?? "",
      txtwonreward_003: json['txtwonreward_003'] as String? ?? "",
      txtwonreward_004: json['txtwonreward_004'] as String? ?? "",
      body_EmailSettings_offers:
          json['body_EmailSettings_offers'] as String? ?? "",
      ctrl_btnsavingpref_your_001:
          json['ctrl_btnsavingpref_your_001'] as String? ?? "",
      txtyourspinexpiresindays_001:
          json['txtyourspinexpiresindays_001'] as String? ?? "",
      txt_earn_points_001: json['txt_earn_points_001'] as String? ?? "",
      txtrewards1081: json['txtrewards1081'] as String? ?? "",
      txtrewards1082: json['txtrewards1082'] as String? ?? "",
      txtrewards1083: json['txtrewards1083'] as String? ?? "",
      txtrewards1084: json['txtrewards1084'] as String? ?? "",
      txtrewards1085: json['txtrewards1085'] as String? ?? "",
      txtrewards1086: json['txtrewards1086'] as String? ?? "",
      txtrewards2270: json['txtrewards2270'] as String? ?? "",
      txtmonday: json['txtmonday'] as String? ?? "",
      txtpts: json['txtpts'] as String? ?? "",
      txtpending: json['txtpending'] as String? ?? "",
      txtgoalspending: json['txtgoalspending'] as String? ?? "",
      txtpointspending: json['txtpointspending'] as String? ?? "",
      txtqualifyingtext: json['txtqualifyingtext'] as String? ?? "",
      txtactivategoal: json['txtactivategoal'] as String? ?? "",
      txtopensettings: json['txtopensettings'] as String? ?? "",
      txtapplehealth: json['txtapplehealth'] as String? ?? "",
      txtvitalitymallstore: json['txtvitalitymallstore'] as String? ?? "",
      txtpermissions: json['txtpermissions'] as String? ?? "",
      iconrewards1081: json['iconrewards1081'] as String? ?? "",
      iconrewards1082: json['iconrewards1082'] as String? ?? "",
      iconrewards1083: json['iconrewards1083'] as String? ?? "",
      iconrewards1084: json['iconrewards1084'] as String? ?? "",
      iconrewards1085: json['iconrewards1085'] as String? ?? "",
      iconrewards1086: json['iconrewards1086'] as String? ?? "",
      txtandor_001: json['txtandor_001'] as String? ?? "",
      txtor_001: json['txtor_001'] as String? ?? "",
      txtfooter_003: json['txtfooter_003'] as String? ?? "",
      useToLogin: json['useToLogin'] as String? ?? "",
      biometricSettings: json['biometricSettings'] as String? ?? "",
      stepOf4: json['stepOf4'] as String? ?? "",
      sentActivationCodeError: json['sentActivationCodeError'] as String? ?? "",
      clearLabel: json['clearLabel'] as String? ?? "",
      codeVerified: json['codeVerified'] as String? ?? "",
      resendInsurerSuccess: json['resendInsurerSuccess'] as String? ?? "",
      networkError: json['networkError'] as String? ?? "",
      exceptionUserRegistered: json['exceptionUserRegistered'] as String? ?? "",
      exceptionValidateInsurer:
          json['exceptionValidateInsurer'] as String? ?? "",
      exceptionTimeout: json['exceptionTimeout'] as String? ?? "",
      dobValidationFailed: json['dobValidationFailed'] as String? ?? "",
      ctrl_navbar_tabhome: json['ctrl_navbar_tabhome'] as String? ?? "",
      ctrl_navbar_tabrewards: json['ctrl_navbar_tabrewards'] as String? ?? "",
      ctrl_navbar_tabhealth: json['ctrl_navbar_tabhealth'] as String? ?? "",
      ctrl_navbar_tabprofile: json['ctrl_navbar_tabprofile'] as String? ?? "",
      txtentercodeanddob_001: json['txtentercodeanddob_001'] as String? ?? "",
      ctrl_btnnext_001: json['ctrl_btnnext_001'] as String? ?? "",
      ctrl_btnrelatedfaqs_001: json['ctrl_btnrelatedfaqs_001'] as String? ?? "",
      ctrl_btnhelpcentre_001: json['ctrl_btnhelpcentre_001'] as String? ?? "",
      txt_footer_001: json['txt_footer_001'] as String? ?? "",
      home_landing_txtheaderplatinum_001:
          json['home_landing_txtheaderplatinum_001'] as String? ?? "",
      txt_homeintro_001: json['txt_homeintro_001'] as String? ?? "",
      txtbronzestatus_001: json['txtbronzestatus_001'] as String? ?? "",
      txtbronzestatusaia_002: json['txtbronzestatusaia_002'] as String? ?? "",
      txtsilverstatusaia_001: json['txtsilverstatusaia_001'] as String? ?? "",
      txtgoldstatusaia_001: json['txtgoldstatusaia_001'] as String? ?? "",
      txtvitalitystatus_001: json['txtvitalitystatus_001'] as String? ?? "",
      spend: json['spend'] as String? ?? "",
      txtcoins_001: json['txtcoins_001'] as String? ?? "",
      txt_goodmorningwithname_001:
          json['txt_goodmorningwithname_001'] as String? ?? "",
      txtgoodmorning_001: json['txtgoodmorning_001'] as String? ?? "",
      txt_goodafternoonwithname_001:
          json['txt_goodafternoonwithname_001'] as String? ?? "",
      txtgoodafternoon_001: json['txtgoodafternoon_001'] as String? ?? "",
      txt_goodeveningwithname_001:
          json['txt_goodeveningwithname_001'] as String? ?? "",
      txtgoodevening_001: json['txtgoodevening_001'] as String? ?? "",
      txtnopromotion_001: json['txtnopromotion_001'] as String? ?? "",
      txtnopromotionbody_001: json['txtnopromotionbody_001'] as String? ?? "",
      chip_workoutDuration: json['chip_workoutDuration'] as String? ?? "",
      txtactivationwithrewardsuccess1:
          json['txtactivationwithrewardsuccess1'] as String? ?? "",
      txtactivationsuccess: json['txtactivationsuccess'] as String? ?? "",
      txtconnectionerrorbody: json['txtconnectionerrorbody'] as String? ?? "",
      txtachieved: json['txtachieved'] as String? ?? "",
      txt_points_001: json['txt_points_001'] as String? ?? "",
      txtpointsreflectnoteandvitlaitystatus_002:
          json['txtpointsreflectnoteandvitlaitystatus_002'] as String? ?? "",
      ctrl_btnchoosegiftcard_001:
          json['ctrl_btnchoosegiftcard_001'] as String? ?? "",
      txtexpires_001: json['txtexpires_001'] as String? ?? "",
      txtexpired_001_date: json['txtexpired_001_date'] as String? ?? "",
      txtusethecodetoredeem_001:
          json['txtusethecodetoredeem_001'] as String? ?? "",
      txtusethecodetoredeem_002:
          json['txtusethecodetoredeem_002'] as String? ?? "",
      txtdiscountcode_001: json['txtdiscountcode_001'] as String? ?? "",
      ctrl_btnrevealcode_001: json['ctrl_btnrevealcode_001'] as String? ?? "",
      txtpincode_001: json['txtpincode_001'] as String? ?? "",
      ctrl_btnviewallgiftcards_001:
          json['ctrl_btnviewallgiftcards_001'] as String? ?? "",
      ctrl_btncopy_001: json['ctrl_btncopy_001'] as String? ?? "",
      ctrl_btnvisitwebsite_001:
          json['ctrl_btnvisitwebsite_001'] as String? ?? "",
      txtthisbarcodecanbeusedforinstorepurchases_001:
          json['txtthisbarcodecanbeusedforinstorepurchases_001'] as String? ??
              "",
      txtvisitthewebsitetoredeemyourgiftcard_001:
          json['txtvisitthewebsitetoredeemyourgiftcard_001'] as String? ?? "",
      txtlinkafitnessdeviceorapp_001:
          json['txtlinkafitnessdeviceorapp_001'] as String? ?? "",
      txtbodydeviceapplink_001:
          json['txtbodydeviceapplink_001'] as String? ?? "",
      ctrl_txtmaybelater_001: json['ctrl_txtmaybelater_001'] as String? ?? "",
      ctrl_txtconnectnow_001: json['ctrl_txtconnectnow_001'] as String? ?? "",
      txtselectyourgiftcard_001:
          json['txtselectyourgiftcard_001'] as String? ?? "",
      ctrl_btnselectlater_001: json['ctrl_btnselectlater_001'] as String? ?? "",
      login_errfaceiddchange_txthgfaceidchange_001:
          json['login_errfaceiddchange_txthgfaceidchange_001'] as String? ?? "",
      login_errfaceidchange_txtbdfaceidchange_001:
          json['login_errfaceidchange_txtbdfaceidchange_001'] as String? ?? "",
      ctrl_btnyescontinueusingfaceid_001:
          json['ctrl_btnyescontinueusingfaceid_001'] as String? ?? "",
      ctrl_btnnodisableusingfaceid_001:
          json['ctrl_btnnodisableusingfaceid_001'] as String? ?? "",
      txtarchivedgiftcards_001:
          json['txtarchivedgiftcards_001'] as String? ?? "",
      txtnogiftcardsarchived_001:
          json['txtnogiftcardsarchived_001'] as String? ?? "",
      txtnogiftcardsarchivedcopy_001:
          json['txtnogiftcardsarchivedcopy_001'] as String? ?? "",
      txtgiftcard_001: json['txtgiftcard_001'] as String? ?? "",
      txtyourgiftcardisbeingprepared_001:
          json['txtyourgiftcardisbeingprepared_001'] as String? ?? "",
      txtyourgiftcardisbeingpreparedcopy_001:
          json['txtyourgiftcardisbeingpreparedcopy_001'] as String? ?? "",
      txtyourgiftcards_001: json['txtyourgiftcards_001'] as String? ?? "",
      txtgiftcards_001: json['txtgiftcards_001'] as String? ?? "",
      txthowtoearncoins_001: json['txthowtoearncoins_001'] as String? ?? "",
      txtexpiressoon_001: json['txtexpiressoon_001'] as String? ?? "",
      ctrl_btnarchive_001: json['ctrl_btnarchive_001'] as String? ?? "",
      txtnogiftcards_001: json['txtnogiftcards_001'] as String? ?? "",
      txtyourgiftcardswillappearhere_001:
          json['txtyourgiftcardswillappearhere_001'] as String? ?? "",
      txtnoexpirydate_001: json['txtnoexpirydate_001'] as String? ?? "",
      txtyouhaveXgiftcards_001:
          json['txtyouhaveXgiftcards_001'] as String? ?? "",
      txtyouhaveXgiftcard_001: json['txtyouhaveXgiftcard_001'] as String? ?? "",
      ctrl_btnsubmitresultsandproof_001:
          json['ctrl_btnsubmitresultsandproof_001'] as String? ?? "",
      txtnsubmitproof_001: json['txtnsubmitproof_001'] as String? ?? "",
      txtpoints_002: json['txtpoints_002'] as String? ?? "",
      txt_fitnessevents: json['txt_fitnessevents'] as String? ?? "",
      txtpersonalisegoalsbody_001:
          json['txtpersonalisegoalsbody_001'] as String? ?? "",
      txtpersonalisegoals_001: json['txtpersonalisegoals_001'] as String? ?? "",
      txtgetweeklygoals_001: json['txtgetweeklygoals_001'] as String? ?? "",
      txteachmondaywellrecommendthreegoalsforyou_001:
          json['txteachmondaywellrecommendthreegoalsforyou_001'] as String? ??
              "",
      txtenjoyrewards_001: json['txtenjoyrewards_001'] as String? ?? "",
      txtwhenyoucompleteyourgoal_001:
          json['txtwhenyoucompleteyourgoal_001'] as String? ?? "",
      txttellusmoreaboutyou_001:
          json['txttellusmoreaboutyou_001'] as String? ?? "",
      txtthemoreweknow_001: json['txtthemoreweknow_001'] as String? ?? "",
      ctrl_btngetstarted_001: json['ctrl_btngetstarted_001'] as String? ?? "",
      txt_iunderstand_001: json['txt_iunderstand_001'] as String? ?? "",
      txtcsubmitproofofparticipation_001:
          json['txtcsubmitproofofparticipation_001'] as String? ?? "",
      txtsupportexplanation_001:
          json['txtsupportexplanation_001'] as String? ?? "",
      txteventdetails_001: json['txteventdetails_001'] as String? ?? "",
      txtproof_001: json['txtproof_001'] as String? ?? "",
      ctrl_btnsubmit_001: json['ctrl_btnsubmit_001'] as String? ?? "",
      txtFitnesseventsubmitted_001:
          json['txtFitnesseventsubmitted_001'] as String? ?? "",
      txtpointsmaynotreflect_immediately_001:
          json['txtpointsmaynotreflect_immediately_001'] as String? ?? "",
      txtfooter_006: json['txtfooter_006'] as String? ?? "",
      txthowtoearnpoints_001: json['txthowtoearnpoints_001'] as String? ?? "",
      txtsubmitted_001: json['txtsubmitted_001'] as String? ?? "",
      txteventscompletedwithinthelastsixmonthsqualify_001:
          json['txteventscompletedwithinthelastsixmonthsqualify_001']
                  as String? ??
              "",
      txtuptopointspersubmission_001:
          json['txtuptopointspersubmission_001'] as String? ?? "",
      txteventtype_001: json['txteventtype_001'] as String? ?? "",
      txt_label_resultURL_001: json['txt_label_resultURL_001'] as String? ?? "",
      txtpleaseenteravalidurl_001:
          json['txtpleaseenteravalidurl_001'] as String? ?? "",
      ctrl_btnupload_001: json['ctrl_btnupload_001'] as String? ?? "",
      txtformatnotrecognised_pleasetryagain_001:
          json['txtformatnotrecognised_pleasetryagain_001'] as String? ?? "",
      txtforyou_001: json['txtforyou_001'] as String? ?? "",
      txtwhyitsimportantcopy_001:
          json['txtwhyitsimportantcopy_001'] as String? ?? "",
      txtwlgonbaordingerror_001:
          json['txtwlgonbaordingerror_001'] as String? ?? "",
      txtwlgonboaringerror_body_001:
          json['txtwlgonboaringerror_body_001'] as String? ?? "",
      txtwlgonboaringerror_body_002:
          json['txtwlgonboaringerror_body_002'] as String? ?? "",
      txtpoints_003: json['txtpoints_003'] as String? ?? "",
      txtgetproof_001: json['txtgetproof_001'] as String? ?? "",
      ctrl_btnsubmitproof_001: json['ctrl_btnsubmitproof_001'] as String? ?? "",
      ctrl_btnsubmitproof_002: json['ctrl_btnsubmitproof_002'] as String? ?? "",
      txtnote_001: json['txtnote_001'] as String? ?? "",
      txtpointsforsubmitting_001:
          json['txtpointsforsubmitting_001'] as String? ?? "",
      txtabout_001: json['txtabout_001'] as String? ?? "",
      txtmammogramaboutcopy_001:
          json['txtmammogramaboutcopy_001'] as String? ?? "",
      ctrl_btnseeless_001: json['ctrl_btnseeless_001'] as String? ?? "",
      body_submitproofparagraph_001:
          json['body_submitproofparagraph_001'] as String? ?? "",
      txttype_001: json['txttype_001'] as String? ?? "",
      txtselecttype_001: json['txtselecttype_001'] as String? ?? "",
      txtdateofactivity_001: json['txtdateofactivity_001'] as String? ?? "",
      txtdateofactivitynotedate_001:
          json['txtdateofactivitynotedate_001'] as String? ?? "",
      txtbody_cholestrolAbout_001:
          json['txtbody_cholestrolAbout_001'] as String? ?? "",
      txtbody_cholestrolAbout_002:
          json['txtbody_cholestrolAbout_002'] as String? ?? "",
      txtbody_cholestrolAbout_003:
          json['txtbody_cholestrolAbout_003'] as String? ?? "",
      txtbody_cholestrolAbout_004:
          json['txtbody_cholestrolAbout_004'] as String? ?? "",
      txtbody_cholestrolAbout_005:
          json['txtbody_cholestrolAbout_005'] as String? ?? "",
      txtbuttondone_001: json['txtbuttondone_001'] as String? ?? "",
      txtbuttondone_002: json['txtbuttondone_002'] as String? ?? "",
      txtpoints_004: json['txtpoints_004'] as String? ?? "",
      txtcomplete_001: json['txtcomplete_001'] as String? ?? "",
      txtcompleteddate_001: json['txtcompleteddate_001'] as String? ?? "",
      iconrewardsclip: json['iconrewardsclip'] as String? ?? "",
      iconrewardsarrowdown: json['iconrewardsarrowdown'] as String? ?? "",
      txthealth_001: json['txthealth_001'] as String? ?? "",
      txtlearnmoreaboutyourhealth_001:
          json['txtlearnmoreaboutyourhealth_001'] as String? ?? "",
      txtcompleteha_001: json['txtcompleteha_001'] as String? ?? "",
      txtforaccurateresults_001:
          json['txtforaccurateresults_001'] as String? ?? "",
      txtrelatedactivities_001:
          json['txtrelatedactivities_001'] as String? ?? "",
      txthealthassessment_001: json['txthealthassessment_001'] as String? ?? "",
      txthealthcheck_001: json['txthealthcheck_001'] as String? ?? "",
      ctrl_textlearnmoreaboutthescience:
          json['ctrl_textlearnmoreaboutthescience'] as String? ?? "",
      ctrl_helpcentre_001: json['ctrl_helpcentre_001'] as String? ?? "",
      txtstateoftheartscience_001:
          json['txtstateoftheartscience_001'] as String? ?? "",
      txtcompletehctoget_001: json['txtcompletehctoget_001'] as String? ?? "",
      txttoimprove_001: json['txttoimprove_001'] as String? ?? "",
      txtdoingwell_001: json['txtdoingwell_001'] as String? ?? "",
      txtunknown_001: json['txtunknown_001'] as String? ?? "",
      txthealthpriority_001: json['txthealthpriority_001'] as String? ?? "",
      txtimproveyoursleep_001: json['txtimproveyoursleep_001'] as String? ?? "",
      txtfuturehealth_001: json['txtfuturehealth_001'] as String? ?? "",
      txtdiscoverthehealthiestversionofyou_001:
          json['txtdiscoverthehealthiestversionofyou_001'] as String? ?? "",
      txtcompleted_on_date_001:
          json['txtcompleted_on_date_001'] as String? ?? "",
      ctrl_btnseeallactivities_001:
          json['ctrl_btnseeallactivities_001'] as String? ?? "",
      txtvitalityageandhealthresults_001:
          json['txtvitalityageandhealthresults_001'] as String? ?? "",
      txtexploreyourresults_001:
          json['txtexploreyourresults_001'] as String? ?? "",
      txtloweryourbloodpressure_001:
          json['txtloweryourbloodpressure_001'] as String? ?? "",
      txtvitalityage_001: json['txtvitalityage_001'] as String? ?? "",
      txtwhyitsimportant_001: json['txtwhyitsimportant_001'] as String? ?? "",
      ctrl_btnselectgoal_001: json['ctrl_btnselectgoal_001'] as String? ?? "",
      txtselectandcompletealifestylegoalrecommendedforyou_001:
          json['txtselectandcompletealifestylegoalrecommendedforyou_001']
                  as String? ??
              "",
      txtcheckbackonmonday_001:
          json['txtcheckbackonmonday_001'] as String? ?? "",
      ctrl_btnseemore_001: json['ctrl_btnseemore_001'] as String? ?? "",
      txtyourlifestylegoalismet_001:
          json['txtyourlifestylegoalismet_001'] as String? ?? "",
      txtyourlifestylegoalismetcopy_001:
          json['txtyourlifestylegoalismetcopy_001'] as String? ?? "",
      ctrl_btnviewsummary_001: json['ctrl_btnviewsummary_001'] as String? ?? "",
      txtscreeningorvaccination_001:
          json['txtscreeningorvaccination_001'] as String? ?? "",
      ctrl_btnsubmitanotheractivity_001:
          json['ctrl_btnsubmitanotheractivity_001'] as String? ?? "",
      txteventdate_001: json['txteventdate_001'] as String? ?? "",
      txtonrewardsubtext_001: json['txtonrewardsubtext_001'] as String? ?? "",
      txtrewards_001: json['txtrewards_001'] as String? ?? "",
      ctrl_btngotothefidelidadestore_001:
          json['ctrl_btngotothefidelidadestore_001'] as String? ?? "",
      txtweeklyrewards_001: json['txtweeklyrewards_001'] as String? ?? "",
      txtfooter_007: json['txtfooter_007'] as String? ?? "",
      txtonreward_001: json['txtonreward_001'] as String? ?? "",
      txtonrewardfidcoins_001: json['txtonrewardfidcoins_001'] as String? ?? "",
      txtaboutthispartner_001: json['txtaboutthispartner_001'] as String? ?? "",
      txtgiftcardpartners_001: json['txtgiftcardpartners_001'] as String? ?? "",
      txtonrewardsubtext_002: json['txtonrewardsubtext_002'] as String? ?? "",
      txtenjoytherewards_001: json['txtenjoytherewards_001'] as String? ?? "",
      ctrl_btnilldothislater_001:
          json['ctrl_btnilldothislater_001'] as String? ?? "",
      txthowtoearncoinsintrotext_001:
          json['txthowtoearncoinsintrotext_001'] as String? ?? "",
      txthowtoearncoinsintrotext_002:
          json['txthowtoearncoinsintrotext_002'] as String? ?? "",
      txthowtoearncoinsintrotext_003:
          json['txthowtoearncoinsintrotext_003'] as String? ?? "",
      textcoins_002: json['textcoins_002'] as String? ?? "",
      txtselectimage_001: json['txtselectimage_001'] as String? ?? "",
      txt_takeaphoto_001: json['txt_takeaphoto_001'] as String? ?? "",
      ctrl_txtselectfromgallery_001:
          json['ctrl_txtselectfromgallery_001'] as String? ?? "",
      txtexercise_guide_001: json['txtexercise_guide_001'] as String? ?? "",
      txtexercise_intro_001: json['txtexercise_intro_001'] as String? ?? "",
      txtexercise_intro_002: json['txtexercise_intro_002'] as String? ?? "",
      txtexercise_intro_003: json['txtexercise_intro_003'] as String? ?? "",
      txtexercise_intro_004: json['txtexercise_intro_004'] as String? ?? "",
      txtexercise_intro_005: json['txtexercise_intro_005'] as String? ?? "",
      txtexercise_intro_006: json['txtexercise_intro_006'] as String? ?? "",
      txttips_excercise_001: json['txttips_excercise_001'] as String? ?? "",
      txttips_excercise_content_001:
          json['txttips_excercise_content_001'] as String? ?? "",
      txtrecommendations_001: json['txtrecommendations_001'] as String? ?? "",
      txtrecommendations_content_001:
          json['txtrecommendations_content_001'] as String? ?? "",
      txtwhatcounts_001: json['txtwhatcounts_001'] as String? ?? "",
      txtwhatcounts_content_001:
          json['txtwhatcounts_content_001'] as String? ?? "",
      txtfitmorein_001: json['txtfitmorein_001'] as String? ?? "",
      txtfitmorein_content_001:
          json['txtfitmorein_content_001'] as String? ?? "",
      txtavoidboredom_001: json['txtavoidboredom_001'] as String? ?? "",
      txtavoidboredom_content_001:
          json['txtavoidboredom_content_001'] as String? ?? "",
      txtexcsafely_001: json['txtexcsafely_001'] as String? ?? "",
      txtexcsafely_content_001:
          json['txtexcsafely_content_001'] as String? ?? "",
      txtexcsafely_content_002:
          json['txtexcsafely_content_002'] as String? ?? "",
      txtconsultwithyourdoctor_001:
          json['txtconsultwithyourdoctor_001'] as String? ?? "",
      txtconsultwithyourdoctor_content_001:
          json['txtconsultwithyourdoctor_content_001'] as String? ?? "",
      txtconsultwithyourdoctor_content_002:
          json['txtconsultwithyourdoctor_content_002'] as String? ?? "",
      txtmentalwellbeing_guide_001:
          json['txtmentalwellbeing_guide_001'] as String? ?? "",
      txtmentalwellbeing_intro_001:
          json['txtmentalwellbeing_intro_001'] as String? ?? "",
      txtmentalwellbeing_intro_002:
          json['txtmentalwellbeing_intro_002'] as String? ?? "",
      txttips_mentalwellbeing_001:
          json['txttips_mentalwellbeing_001'] as String? ?? "",
      txttips_mentalwellbeing_content_001:
          json['txttips_mentalwellbeing_content_001'] as String? ?? "",
      txtprioritizesleep_001: json['txtprioritizesleep_001'] as String? ?? "",
      txtprioritizesleep_content_001:
          json['txtprioritizesleep_content_001'] as String? ?? "",
      txtprioritizesleep_content_002:
          json['txtprioritizesleep_content_002'] as String? ?? "",
      txtprioritizesleep_content_003:
          json['txtprioritizesleep_content_003'] as String? ?? "",
      txtconnectwithothers_001:
          json['txtconnectwithothers_001'] as String? ?? "",
      txtconnectwithothers_content_001:
          json['txtconnectwithothers_content_001'] as String? ?? "",
      txthealthymindset_001: json['txthealthymindset_001'] as String? ?? "",
      txthealthymindset_content_001:
          json['txthealthymindset_content_001'] as String? ?? "",
      txtcalmingtechniques_001:
          json['txtcalmingtechniques_001'] as String? ?? "",
      txtcalmingtechniques_content_001:
          json['txtcalmingtechniques_content_001'] as String? ?? "",
      txtresiliencetechniques_001:
          json['txtresiliencetechniques_001'] as String? ?? "",
      txtresiliencetechniques_content_001:
          json['txtresiliencetechniques_content_001'] as String? ?? "",
      txtdiabetes_guide_001: json['txtdiabetes_guide_001'] as String? ?? "",
      txtdiabetes_intro_001: json['txtdiabetes_intro_001'] as String? ?? "",
      txtdiabetes_intro_002: json['txtdiabetes_intro_002'] as String? ?? "",
      txtdiabetes_intro_003: json['txtdiabetes_intro_003'] as String? ?? "",
      txttips_diabetes_001: json['txttips_diabetes_001'] as String? ?? "",
      txttips_diabetes_content_001:
          json['txttips_diabetes_content_001'] as String? ?? "",
      txtcheckbloodsugarreg_001:
          json['txtcheckbloodsugarreg_001'] as String? ?? "",
      txtcheckbloodsugarreg_content_001:
          json['txtcheckbloodsugarreg_content_001'] as String? ?? "",
      txteatwell_001: json['txteatwell_001'] as String? ?? "",
      txteatwell_content_001: json['txteatwell_content_001'] as String? ?? "",
      txteatwell_content_002: json['txteatwell_content_002'] as String? ?? "",
      txteatwell_content_003: json['txteatwell_content_003'] as String? ?? "",
      txteatwell_content_004: json['txteatwell_content_004'] as String? ?? "",
      txteatwell_content_005: json['txteatwell_content_005'] as String? ?? "",
      txteatwell_content_006: json['txteatwell_content_006'] as String? ?? "",
      txteatwell_content_007: json['txteatwell_content_007'] as String? ?? "",
      txttakemedasdirected_001:
          json['txttakemedasdirected_001'] as String? ?? "",
      txttakemedasdirected_content_001:
          json['txttakemedasdirected_content_001'] as String? ?? "",
      txtbeactive_001: json['txtbeactive_001'] as String? ?? "",
      txtbeactive_content001: json['txtbeactive_content001'] as String? ?? "",
      txtbeactive_content002: json['txtbeactive_content002'] as String? ?? "",
      txtwatchyourweight_001: json['txtwatchyourweight_001'] as String? ?? "",
      txtwatchyourweight_content_001:
          json['txtwatchyourweight_content_001'] as String? ?? "",
      txtwatchyourweight_content_002:
          json['txtwatchyourweight_content_002'] as String? ?? "",
      txtwatchyourweight_content_003:
          json['txtwatchyourweight_content_003'] as String? ?? "",
      txtwatchyourweight_content_004:
          json['txtwatchyourweight_content_004'] as String? ?? "",
      txtwatchyourweight_content_005:
          json['txtwatchyourweight_content_005'] as String? ?? "",
      txtwatchyourweight_content_006:
          json['txtwatchyourweight_content_006'] as String? ?? "",
      txtwatchyourweight_content_007:
          json['txtwatchyourweight_content_007'] as String? ?? "",
      txtwatchyourweight_content_008:
          json['txtwatchyourweight_content_008'] as String? ?? "",
      txtwatchyourweight_content_009:
          json['txtwatchyourweight_content_009'] as String? ?? "",
      txtwatchyourweight_content_010:
          json['txtwatchyourweight_content_010'] as String? ?? "",
      txtwatchyourweight_content_011:
          json['txtwatchyourweight_content_011'] as String? ?? "",
      txtreceivecare_001: json['txtreceivecare_001'] as String? ?? "",
      txtreceivecare_content_001:
          json['txtreceivecare_content_001'] as String? ?? "",
      txtplantbased_guide_001: json['txtplantbased_guide_001'] as String? ?? "",
      txtplantbased_intro_001: json['txtplantbased_intro_001'] as String? ?? "",
      txtplantbased_intro_002: json['txtplantbased_intro_002'] as String? ?? "",
      txttips_plantbased_001: json['txttips_plantbased_001'] as String? ?? "",
      txttips_plantbased_content_001:
          json['txttips_plantbased_content_001'] as String? ?? "",
      txthowmanysohuldIeat_001:
          json['txthowmanysohuldIeat_001'] as String? ?? "",
      txthowmanysohuldIeat_content_001:
          json['txthowmanysohuldIeat_content_001'] as String? ?? "",
      txthowmanysohuldIeat_content_002:
          json['txthowmanysohuldIeat_content_002'] as String? ?? "",
      txthowmanysohuldIeat_content_003:
          json['txthowmanysohuldIeat_content_003'] as String? ?? "",
      txtchooserawandwhole_001:
          json['txtchooserawandwhole_001'] as String? ?? "",
      txtchooserawandwhole_content_001:
          json['txtchooserawandwhole_content_001'] as String? ?? "",
      txtchooserawandwhole_content_002:
          json['txtchooserawandwhole_content_002'] as String? ?? "",
      txteatmorecolour_001: json['txteatmorecolour_001'] as String? ?? "",
      txteatmorecolour_content_001:
          json['txteatmorecolour_content_001'] as String? ?? "",
      txteatmorecolour_content_002:
          json['txteatmorecolour_content_002'] as String? ?? "",
      txtsneakbeansandpeas_001:
          json['txtsneakbeansandpeas_001'] as String? ?? "",
      txtsneakbeansandpeas_content_001:
          json['txtsneakbeansandpeas_content_001'] as String? ?? "",
      txtsneakbeansandpeas_content_002:
          json['txtsneakbeansandpeas_content_002'] as String? ?? "",
      txtsneakbeansandpeas_content_003:
          json['txtsneakbeansandpeas_content_003'] as String? ?? "",
      txtoptforwholegrains_001:
          json['txtoptforwholegrains_001'] as String? ?? "",
      txtoptforwholegrains_content_001:
          json['txtoptforwholegrains_content_001'] as String? ?? "",
      txtoptforwholegrains_content_002:
          json['txtoptforwholegrains_content_002'] as String? ?? "",
      txtoptforwholegrains_content_003:
          json['txtoptforwholegrains_content_003'] as String? ?? "",
      txthealthyeating_guide_001:
          json['txthealthyeating_guide_001'] as String? ?? "",
      txthealthyeating_intro_001:
          json['txthealthyeating_intro_001'] as String? ?? "",
      txthealthyeating_intro_002:
          json['txthealthyeating_intro_002'] as String? ?? "",
      txttips_eatinghealthy_001:
          json['txttips_eatinghealthy_001'] as String? ?? "",
      txttips_eatinghealthy_content_001:
          json['txttips_eatinghealthy_content_001'] as String? ?? "",
      txtsweetcravings_001: json['txtsweetcravings_001'] as String? ?? "",
      txtsweetcravings_content_001:
          json['txtsweetcravings_content_001'] as String? ?? "",
      txtsweetcravings_content_002:
          json['txtsweetcravings_content_002'] as String? ?? "",
      txtsugarswaps_001: json['txtsugarswaps_001'] as String? ?? "",
      txtsugarswaps_content_001:
          json['txtsugarswaps_content_001'] as String? ?? "",
      txtherbsandspices_001: json['txtherbsandspices_001'] as String? ?? "",
      txtherbsandspices_content_001:
          json['txtherbsandspices_content_001'] as String? ?? "",
      txtherbsandspices_content_002:
          json['txtherbsandspices_content_002'] as String? ?? "",
      txtflavourcombos_001: json['txtflavourcombos_001'] as String? ?? "",
      txtflavourcombos_content_001:
          json['txtflavourcombos_content_001'] as String? ?? "",
      txtprotein_guide_001: json['txtprotein_guide_001'] as String? ?? "",
      txtprotein_intro_001: json['txtprotein_intro_001'] as String? ?? "",
      txtprotein_intro_002: json['txtprotein_intro_002'] as String? ?? "",
      txtfreshorfrozeninsteadofprocessed_001:
          json['txtfreshorfrozeninsteadofprocessed_001'] as String? ?? "",
      txtfreshorfrozeninsteadofprocessed_content_001:
          json['txtfreshorfrozeninsteadofprocessed_content_001'] as String? ??
              "",
      txtfreshorfrozeninsteadofprocessed_content_002:
          json['txtfreshorfrozeninsteadofprocessed_content_002'] as String? ??
              "",
      txtchooseredleancuts_001:
          json['txtchooseredleancuts_001'] as String? ?? "",
      txtchooseredleancuts_content_001:
          json['txtchooseredleancuts_content_001'] as String? ?? "",
      txtchooseredleancuts_content_002:
          json['txtchooseredleancuts_content_002'] as String? ?? "",
      txtchooseredleancuts_content_003:
          json['txtchooseredleancuts_content_003'] as String? ?? "",
      txthealthierprep_001: json['txthealthierprep_001'] as String? ?? "",
      txthealthierprep_content_001:
          json['txthealthierprep_content_001'] as String? ?? "",
      txtvarietyproteinsources_001:
          json['txtvarietyproteinsources_001'] as String? ?? "",
      txtvarietyproteinsources_content_001:
          json['txtvarietyproteinsources_content_001'] as String? ?? "",
      txtvarietyproteinsources_content_002:
          json['txtvarietyproteinsources_content_002'] as String? ?? "",
      txtvarietyproteinsources_content_003:
          json['txtvarietyproteinsources_content_003'] as String? ?? "",
      txtmediterraneanstyle_001:
          json['txtmediterraneanstyle_001'] as String? ?? "",
      txtmediterraneanstyle_content_001:
          json['txtmediterraneanstyle_content_001'] as String? ?? "",
      txtbloodpressure_guide_001:
          json['txtbloodpressure_guide_001'] as String? ?? "",
      txtbloodpressure_intro_001:
          json['txtbloodpressure_intro_001'] as String? ?? "",
      txtbloodpressure_intro_002:
          json['txtbloodpressure_intro_002'] as String? ?? "",
      txtbloodpressure_intro_003:
          json['txtbloodpressure_intro_003'] as String? ?? "",
      txtbloodpressure_intro_004:
          json['txtbloodpressure_intro_004'] as String? ?? "",
      txtbloodpressure_intro_005:
          json['txtbloodpressure_intro_005'] as String? ?? "",
      txtbloodpressure_intro_006:
          json['txtbloodpressure_intro_006'] as String? ?? "",
      txtbloodpressure_intro_007:
          json['txtbloodpressure_intro_007'] as String? ?? "",
      txttips_bloodpressure_001:
          json['txttips_bloodpressure_001'] as String? ?? "",
      txttips_bloodpressure_content_001:
          json['txttips_bloodpressure_content_001'] as String? ?? "",
      txttips_bloodpressure_content_002:
          json['txttips_bloodpressure_content_002'] as String? ?? "",
      txttakeyourmedications_001:
          json['txttakeyourmedications_001'] as String? ?? "",
      txttakeyourmedications_content_001:
          json['txttakeyourmedications_content_001'] as String? ?? "",
      txttakeyourmedications_content_002:
          json['txttakeyourmedications_content_002'] as String? ?? "",
      txttakeyourmedications_content_003:
          json['txttakeyourmedications_content_003'] as String? ?? "",
      txtmindfulofacl_001: json['txtmindfulofacl_001'] as String? ?? "",
      txtmindfulofacl_content_001:
          json['txtmindfulofacl_content_001'] as String? ?? "",
      txtmindfulofacl_content_002:
          json['txtmindfulofacl_content_002'] as String? ?? "",
      txtmindfulofacl_content_003:
          json['txtmindfulofacl_content_003'] as String? ?? "",
      txtmindfulofacl_content_004:
          json['txtmindfulofacl_content_004'] as String? ?? "",
      txtmindfulofacl_content_005:
          json['txtmindfulofacl_content_005'] as String? ?? "",
      txtmindfulofacl_content_006:
          json['txtmindfulofacl_content_006'] as String? ?? "",
      txtmindfulofacl_content_007:
          json['txtmindfulofacl_content_007'] as String? ?? "",
      txtmindfulofacl_content_008:
          json['txtmindfulofacl_content_008'] as String? ?? "",
      txtmindfulofacl_content_009:
          json['txtmindfulofacl_content_009'] as String? ?? "",
      txtmindfulofacl_content_010:
          json['txtmindfulofacl_content_010'] as String? ?? "",
      txtmindfulofacl_content_011:
          json['txtmindfulofacl_content_011'] as String? ?? "",
      txtquitsmoking_001: json['txtquitsmoking_001'] as String? ?? "",
      txtquitsmoking_content_001:
          json['txtquitsmoking_content_001'] as String? ?? "",
      txtquitsmoking_content_002:
          json['txtquitsmoking_content_002'] as String? ?? "",
      txtquitsmoking_content_003:
          json['txtquitsmoking_content_003'] as String? ?? "",
      txtquitsmoking_content_004:
          json['txtquitsmoking_content_004'] as String? ?? "",
      txtquitsmoking_content_005:
          json['txtquitsmoking_content_005'] as String? ?? "",
      txtquitsmoking_content_006:
          json['txtquitsmoking_content_006'] as String? ?? "",
      txttakemedicationswhenprescribed_001:
          json['txttakemedicationswhenprescribed_001'] as String? ?? "",
      txttakemedicationswhenprescribed_content_001:
          json['txttakemedicationswhenprescribed_content_001'] as String? ?? "",
      txttakemedicationswhenprescribed_content_002:
          json['txttakemedicationswhenprescribed_content_002'] as String? ?? "",
      txteathearthealthy_001: json['txteathearthealthy_001'] as String? ?? "",
      txteathearthealthy_content_001:
          json['txteathearthealthy_content_001'] as String? ?? "",
      txteathearthealthy_content_002:
          json['txteathearthealthy_content_002'] as String? ?? "",
      txtspendyourvitalitycoins_001:
          json['txtspendyourvitalitycoins_001'] as String? ?? "",
      txtspendyourmedals_001: json['txtspendyourmedals_001'] as String? ?? "",
      txtspendyourcoins_001: json['txtspendyourcoins_001'] as String? ?? "",
      ctrl_btnfinish_001: json['ctrl_btnfinish_001'] as String? ?? "",
      ctrl_btndone_001: json['ctrl_btndone_001'] as String? ?? "",
      txtcheckinruleguide_001: json['txtcheckinruleguide_001'] as String? ?? "",
      txtyesterday_001: json['txtyesterday_001'] as String? ?? "",
      txttoday_001: json['txttoday_001'] as String? ?? "",
      txtcheckin_001: json['txtcheckin_001'] as String? ?? "",
      txtcheckingin_001: json['txtcheckingin_001'] as String? ?? "",
      txtweekday_001: json['txtweekday_001'] as String? ?? "",
      txtweekday_002: json['txtweekday_002'] as String? ?? "",
      txtweekday_003: json['txtweekday_003'] as String? ?? "",
      txtweekday_004: json['txtweekday_004'] as String? ?? "",
      txtweekday_005: json['txtweekday_005'] as String? ?? "",
      txtweekday_006: json['txtweekday_006'] as String? ?? "",
      txtweekday_007: json['txtweekday_007'] as String? ?? "",
      txtwheretogo: json['txtwheretogo'] as String? ?? "",
      txtactivitycontentnote: json['txtactivitycontentnote'] as String? ?? "",
      txtcurrentperiod_001: json['txtcurrentperiod_001'] as String? ?? "",
      txttotalpointsearned_001:
          json['txttotalpointsearned_001'] as String? ?? "",
      txtdaysleft_001: json['txtdaysleft_001'] as String? ?? "",
      txtstatusdetails_001: json['txtstatusdetails_001'] as String? ?? "",
      txtpointshistory_001: json['txtpointshistory_001'] as String? ?? "",
      ctrl_btnseeall_001: json['ctrl_btnseeall_001'] as String? ?? "",
      txttbronze_001: json['txttbronze_001'] as String? ?? "",
      txtsilver_001: json['txtsilver_001'] as String? ?? "",
      txtgold_001: json['txtgold_001'] as String? ?? "",
      txttplatinum_001: json['txttplatinum_001'] as String? ?? "",
      txttblue_001: json['txttblue_001'] as String? ?? "",
      txtsettings_001: json['txtsettings_001'] as String? ?? "",
      section_manage_001: json['section_manage_001'] as String? ?? "",
      txtappsanddevices_001: json['txtappsanddevices_001'] as String? ?? "",
      txtloginpreferences_001: json['txtloginpreferences_001'] as String? ?? "",
      txtemailcommunication_001:
          json['txtemailcommunication_001'] as String? ?? "",
      txtnotifications_001: json['txtnotifications_001'] as String? ?? "",
      txtdatasharing_001: json['txtdatasharing_001'] as String? ?? "",
      section_general_001: json['section_general_001'] as String? ?? "",
      txtaccountdetails_001: json['txtaccountdetails_001'] as String? ?? "",
      txthelpcentre_001: json['txthelpcentre_001'] as String? ?? "",
      txtprivacypolicy_001: json['txtprivacypolicy_001'] as String? ?? "",
      txttermsandconditions_001:
          json['txttermsandconditions_001'] as String? ?? "",
      txtlegal1_001: json['txtlegal1_001'] as String? ?? "",
      txtlegal2_001: json['txtlegal2_001'] as String? ?? "",
      ctrl_logout_001: json['ctrl_logout_001'] as String? ?? "",
      dialog_body_areyouwanttologout_001:
          json['dialog_body_areyouwanttologout_001'] as String? ?? "",
      dialog_btn1_yeslogout_001:
          json['dialog_btn1_yeslogout_001'] as String? ?? "",
      txtname_001: json['txtname_001'] as String? ?? "",
      txtmobile_001: json['txtmobile_001'] as String? ?? "",
      txtgender_001: json['txtgender_001'] as String? ?? "",
      txtdateofbirth_001: json['txtdateofbirth_001'] as String? ?? "",
      txtmembershipnumber_001: json['txtmembershipnumber_001'] as String? ?? "",
      txtcustomernumber_001: json['txtcustomernumber_001'] as String? ?? "",
      txtmembershipstartdate_001:
          json['txtmembershipstartdate_001'] as String? ?? "",
      txtmembershiptype_001: json['txtmembershiptype_001'] as String? ?? "",
      txtmembershipcancellation_001:
          json['txtmembershipcancellation_001'] as String? ?? "",
      txtthisinformationfrominsurer_001:
          json['txtthisinformationfrominsurer_001'] as String? ?? "",
      txtpointsandstatus_001: json['txtpointsandstatus_001'] as String? ?? "",
      ctrl_receiveemailsabout_001:
          json['ctrl_receiveemailsabout_001'] as String? ?? "",
      label_commsemaiaddress_001:
          json['label_commsemaiaddress_001'] as String? ?? "",
      ctrl_btnsavechanges_001: json['ctrl_btnsavechanges_001'] as String? ?? "",
      alert_updatedemailaddress_001:
          json['alert_updatedemailaddress_001'] as String? ?? "",
      dialog_hd_disablecommunications_001:
          json['dialog_hd_disablecommunications_001'] as String? ?? "",
      dialog_body_disable_email_communication_001:
          json['dialog_body_disable_email_communication_001'] as String? ?? "",
      dialog_btn2_cancel_001: json['dialog_btn2_cancel_001'] as String? ?? "",
      dialog_btn1_continue_001:
          json['dialog_btn1_continue_001'] as String? ?? "",
      supportText_changeyourcurrentemail_001:
          json['supportText_changeyourcurrentemail_001'] as String? ?? "",
      dialog_hd_changeemail_001:
          json['dialog_hd_changeemail_001'] as String? ?? "",
      ctrl_txt_sharescreeninteractions_001:
          json['ctrl_txt_sharescreeninteractions_001'] as String? ?? "",
      ctrl_txt_shareappcrashes_001:
          json['ctrl_txt_shareappcrashes_001'] as String? ?? "",
      txt_pushnotification_contentnote_001:
          json['txt_pushnotification_contentnote_001'] as String? ?? "",
      txt_opensettings_001: json['txt_opensettings_001'] as String? ?? "",
      txtyoucancompletethisprofilenow_001:
          json['txtyoucancompletethisprofilenow_001'] as String? ?? "",
      txttherewillbeafewquestionsaboutyou_001:
          json['txttherewillbeafewquestionsaboutyou_001'] as String? ?? "",
      ctrl_btncompletelater_001:
          json['ctrl_btncompletelater_001'] as String? ?? "",
      ctrl_btncompletenow_001: json['ctrl_btncompletenow_001'] as String? ?? "",
      txtwelldoneyouscored_001:
          json['txtwelldoneyouscored_001'] as String? ?? "",
      txtwelldoneyouscoredcopy_001:
          json['txtwelldoneyouscoredcopy_001'] as String? ?? "",
      ctrl_btnviewresults_001: json['ctrl_btnviewresults_001'] as String? ?? "",
      txtbetterlucknexttime_001:
          json['txtbetterlucknexttime_001'] as String? ?? "",
      txtbetterlucknexttimecopy_001:
          json['txtbetterlucknexttimecopy_001'] as String? ?? "",
      Visit_BiW_mall_001: json['Visit_BiW_mall_001'] as String? ?? "",
      ctrl_btngotocygmall_001: json['ctrl_btngotocygmall_001'] as String? ?? "",
      txt15creditshavebeendepositedinyourcygmall_001:
          json['txt15creditshavebeendepositedinyourcygmall_001'] as String? ??
              "",
      txtcreditcopy_001: json['txtcreditcopy_001'] as String? ?? "",
      ctrl_btnyourgoalhistory_001:
          json['ctrl_btnyourgoalhistory_001'] as String? ?? "",
      txtmanualcheck_in_001: json['txtmanualcheck_in_001'] as String? ?? "",
      txtyourprofilesummary_001:
          json['txtyourprofilesummary_001'] as String? ?? "",
      txtyouranswer_001: json['txtyouranswer_001'] as String? ?? "",
      txtguidance_001: json['txtguidance_001'] as String? ?? "",
      txtnewgoaleverymonday_001:
          json['txtnewgoaleverymonday_001'] as String? ?? "",
      txtscorehigherthan80_001:
          json['txtscorehigherthan80_001'] as String? ?? "",
      txtwhenwouldyouliketocompletethisgoal_001:
          json['txtwhenwouldyouliketocompletethisgoal_001'] as String? ?? "",
      txtifyouchooselateryouwillfinditingoalsonhome_001:
          json['txtifyouchooselateryouwillfinditingoalsonhome_001']
                  as String? ??
              "",
      txtspendyourcredit_001: json['txtspendyourcredit_001'] as String? ?? "",
      txt_reward_2048: json['txt_reward_2048'] as String? ?? "",
      txt_reward_2050: json['txt_reward_2050'] as String? ?? "",
      txtnsubmitproof_002: json['txtnsubmitproof_002'] as String? ?? "",
      txtpoints_006: json['txtpoints_006'] as String? ?? "",
      txtpoints_005: json['txtpoints_005'] as String? ?? "",
      txtuploading_001: json['txtuploading_001'] as String? ?? "",
      txtoptional_001: json['txtoptional_001'] as String? ?? "",
      txtselectPDF_001: json['txtselectPDF_001'] as String? ?? "",
      txtproofnote_003: json['txtproofnote_003'] as String? ?? "",
      txtproofnote_001: json['txtproofnote_001'] as String? ?? "",
      txtproofnote_002: json['txtproofnote_002'] as String? ?? "",
      how_to_earn_gift_cards_001:
          json['how_to_earn_gift_cards_001'] as String? ?? "",
      how_to_earn_gift_cards_details_001:
          json['how_to_earn_gift_cards_details_001'] as String? ?? "",
      txthhmmss_001: json['txthhmmss_001'] as String? ?? "",
      txtselect_001: json['txtselect_001'] as String? ?? "",
      txt_fitness_disclaimer_001:
          json['txt_fitness_disclaimer_001'] as String? ?? "",
      txtfirstdaynextdaycheckingoal_001:
          json['txtfirstdaynextdaycheckingoal_001'] as String? ?? "",
      txtcheckintomorrow_001: json['txtcheckintomorrow_001'] as String? ?? "",
      txtcheckintomorrowdialog_001:
          json['txtcheckintomorrowdialog_001'] as String? ?? "",
      txtyourquizsummary_001: json['txtyourquizsummary_001'] as String? ?? "",
      login_dialog_txtbdincorrectpwd_002:
          json['login_dialog_txtbdincorrectpwd_002'] as String? ?? "",
      txtnonsmokerdeclaration_001:
          json['txtnonsmokerdeclaration_001'] as String? ?? "",
      txtnonsmokerstatus_001: json['txtnonsmokerstatus_001'] as String? ?? "",
      txtnonsmokerstatus_002: json['txtnonsmokerstatus_002'] as String? ?? "",
      txttime_001: json['txttime_001'] as String? ?? "",
      txtguide_participatingpartners_001:
          json['txtguide_participatingpartners_001'] as String? ?? "",
      txtpointsareavailableeverymembershipyear_002:
          json['txtpointsareavailableeverymembershipyear_002'] as String? ?? "",
      ctrl_btndeclarenonsmokingstatus_001:
          json['ctrl_btndeclarenonsmokingstatus_001'] as String? ?? "",
      txtguide_participatingpartners_002:
          json['txtguide_participatingpartners_002'] as String? ?? "",
      txtparticpatingpartners_001:
          json['txtparticpatingpartners_001'] as String? ?? "",
      txtnonsmokerstatusdeclarationcomplete_001:
          json['txtnonsmokerstatusdeclarationcomplete_001'] as String? ?? "",
      txtstatus_001: json['txtstatus_001'] as String? ?? "",
      txtsmokingdeclarationcomplete_001:
          json['txtsmokingdeclarationcomplete_001'] as String? ?? "",
      nsd_declaration_txtsmoker_001:
          json['nsd_declaration_txtsmoker_001'] as String? ?? "",
      nsd_declaration_txtnonsmoker_001:
          json['nsd_declaration_txtnonsmoker_001'] as String? ?? "",
      nsd_modalcompleted_txtstatusconfirmed_002:
          json['nsd_modalcompleted_txtstatusconfirmed_002'] as String? ?? "",
      nsd_confirm_smoker_001: json['nsd_confirm_smoker_001'] as String? ?? "",
      nsd_confirm_smoker_002: json['nsd_confirm_smoker_002'] as String? ?? "",
      nsd_confirm_non_smoker_001:
          json['nsd_confirm_non_smoker_001'] as String? ?? "",
      nsd_confirm_non_smoker_002:
          json['nsd_confirm_non_smoker_002'] as String? ?? "",
      txtkmtokm_001: json['txtkmtokm_001'] as String? ?? "",
      txtkmandmore_001: json['txtkmandmore_001'] as String? ?? "",
      txtxpoints_001: json['txtxpoints_001'] as String? ?? "",
      txtpointsfordistance_001:
          json['txtpointsfordistance_001'] as String? ?? "",
      txtautomaticallytracked_001:
          json['txtautomaticallytracked_001'] as String? ?? "",
      txt_how_to_earn_Points_body_001:
          json['txt_how_to_earn_Points_body_001'] as String? ?? "",
      txtheartratenote_001: json['txtheartratenote_001'] as String? ?? "",
      txt_heart_rate_condition_001:
          json['txt_heart_rate_condition_001'] as String? ?? "",
      txt_potential_points_001:
          json['txt_potential_points_001'] as String? ?? "",
      txtgymworkout_001: json['txtgymworkout_001'] as String? ?? "",
      txtgymnote_001: json['txtgymnote_001'] as String? ?? "",
      txt_steps_range_001: json['txt_steps_range_001'] as String? ?? "",
      txt_steps_range_002: json['txt_steps_range_002'] as String? ?? "",
      txt_steps_range_003: json['txt_steps_range_003'] as String? ?? "",
      txt_register_sport_classes_001:
          json['txt_register_sport_classes_001'] as String? ?? "",
      txt_sport_classes_details_001:
          json['txt_sport_classes_details_001'] as String? ?? "",
      txt_min_at_kCal_001: json['txt_min_at_kCal_001'] as String? ?? "",
      txt_min_and_more_at_kCal_001:
          json['txt_min_and_more_at_kCal_001'] as String? ?? "",
      txtmindistanceofxkmatanaveragespeedof_001:
          json['txtmindistanceofxkmatanaveragespeedof_001'] as String? ?? "",
      txvalidtimeformat_0001: json['txvalidtimeformat_0001'] as String? ?? "",
      txtgetverifiedprooffromhealthcareprovider_001:
          json['txtgetverifiedprooffromhealthcareprovider_001'] as String? ??
              "",
      txtguide_participatingpartners_003:
          json['txtguide_participatingpartners_003'] as String? ?? "",
      txtrelatedfaqs_001: json['txtrelatedfaqs_001'] as String? ?? "",
      txtpointsforsubmitting_002:
          json['txtpointsforsubmitting_002'] as String? ?? "",
      txtpointsforresultsinhealthyrange_001:
          json['txtpointsforresultsinhealthyrange_001'] as String? ?? "",
      txtsubmittedearnpoints_001:
          json['txtsubmittedearnpoints_001'] as String? ?? "",
      txthealthyrange: json['txthealthyrange'] as String? ?? "",
      txtyoucansubmiteverysixmonths_001:
          json['txtyoucansubmiteverysixmonths_001'] as String? ?? "",
      txtsubmitresultsandform_001:
          json['txtsubmitresultsandform_001'] as String? ?? "",
      txtsubmitresultsandproofexplanationcopy_001:
          json['txtsubmitresultsandproofexplanationcopy_001'] as String? ?? "",
      txthealthcheckdate_001: json['txthealthcheckdate_001'] as String? ?? "",
      txtyouractivitydatemusthaveoccurredbetweendate_001:
          json['txtyouractivitydatemusthaveoccurredbetweendate_001']
                  as String? ??
              "",
      txtsubmitresultsandproofexplanationcopy_002:
          json['txtsubmitresultsandproofexplanationcopy_002'] as String? ?? "",
      guide_: json['guide_'] as String? ?? "",
      txtproofnote_004: json['txtproofnote_004'] as String? ?? "",
      txtproofsubmittedsuccessfully_001:
          json['txtproofsubmittedsuccessfully_001'] as String? ?? "",
      txtpointsmaynotreflectimmediately_001:
          json['txtpointsmaynotreflectimmediately_001'] as String? ?? "",
      txtpointsawardedtopreviousmembershipyear_001:
          json['txtpointsawardedtopreviousmembershipyear_001'] as String? ?? "",
      ctrl_btnsubmitascreeningorvaccination_001:
          json['ctrl_btnsubmitascreeningorvaccination_001'] as String? ?? "",
      txtyoucansubmiteverysixmonths_002:
          json['txtyoucansubmiteverysixmonths_002'] as String? ?? "",
      txtlastmembershipyear_001:
          json['txtlastmembershipyear_001'] as String? ?? "",
      ctrl_txtlearnmoreaboutbodymassindex_001:
          json['ctrl_txtlearnmoreaboutbodymassindex_001'] as String? ?? "",
      txtrange1250_001: json['txtrange1250_001'] as String? ?? "",
      txtresubmitresultsandform_001:
          json['txtresubmitresultsandform_001'] as String? ?? "",
      body_smokesResultSummary:
          json['body_smokesResultSummary'] as String? ?? "",
      crd_statusMarker_outOfRange_001:
          json['crd_statusMarker_outOfRange_001'] as String? ?? "",
      crd_title_001: json['crd_title_001'] as String? ?? "",
      crd_subtext_001: json['crd_subtext_001'] as String? ?? "",
      crd_subtext_002: json['crd_subtext_002'] as String? ?? "",
      txtreferences_001: json['txtreferences_001'] as String? ?? "",
      crd_title_002: json['crd_title_002'] as String? ?? "",
      crd_subtext_003: json['crd_subtext_003'] as String? ?? "",
      crd_statusMarker_outOfRange_002:
          json['crd_statusMarker_outOfRange_002'] as String? ?? "",
      ctrl_txthistory_001: json['ctrl_txthistory_001'] as String? ?? "",
      ctrl_txtquickguide_001: json['ctrl_txtquickguide_001'] as String? ?? "",
      ctrl_txtlearnmoreaboutcholesterol_001:
          json['ctrl_txtlearnmoreaboutcholesterol_001'] as String? ?? "",
      txtsectionHd_001: json['txtsectionHd_001'] as String? ?? "",
      ctrl_txt_001: json['ctrl_txt_001'] as String? ?? "",
      txtfooterhealth: json['txtfooterhealth'] as String? ?? "",
      ctrl_btnshareyoursmokingstatus_001:
          json['ctrl_btnshareyoursmokingstatus_001'] as String? ?? "",
      txtcurrentlysmokes_001: json['txtcurrentlysmokes_001'] as String? ?? "",
      txtnonsmoker_001: json['txtnonsmoker_001'] as String? ?? "",
      txtproofnote_005: json['txtproofnote_005'] as String? ?? "",
      txtpoints_007: json['txtpoints_007'] as String? ?? "",
      txtpoints_008: json['txtpoints_008'] as String? ?? "",
      sport_class_url: json['sport_class_url'] as String? ?? "",
      txt_fingerprint_facial: json['txt_fingerprint_facial'] as String? ?? "",
      txt_goals_001: json['txt_goals_001'] as String? ?? "",
      txthealthcheck_002: json['txthealthcheck_002'] as String? ?? "",
      txtactivities_001: json['txtactivities_001'] as String? ?? "",
      txtcheckinruleguide_002: json['txtcheckinruleguide_002'] as String? ?? "",
      txtmaximumstatuspointsearnedfromactivities_001:
          json['txtmaximumstatuspointsearnedfromactivities_001'] as String? ??
              "",
      txtkeepearningpointstomeetyourgoals_001:
          json['txtkeepearningpointstomeetyourgoals_001'] as String? ?? "",
      ctrl_txtpointshistory_001:
          json['ctrl_txtpointshistory_001'] as String? ?? "",
      txtfootnoteVitalityprogramme_001:
          json['txtfootnoteVitalityprogramme_001'] as String? ?? "",
      txt_sv_disclaimer_001: json['txt_sv_disclaimer_001'] as String? ?? "",
      txtgiftcardsuccessfullyarchived_001:
          json['txtgiftcardsuccessfullyarchived_001'] as String? ?? "",
      txtgiftcardsuccessfullyarchived_002:
          json['txtgiftcardsuccessfullyarchived_002'] as String? ?? "",
      txtnohistoryavailable_001:
          json['txtnohistoryavailable_001'] as String? ?? "",
      txtthepointsyouearnforcompletingactivitieswillposthere_001:
          json['txtthepointsyouearnforcompletingactivitieswillposthere_001']
                  as String? ??
              "",
      ctrl_btngotoyouractivities_001:
          json['ctrl_btngotoyouractivities_001'] as String? ?? "",
      txtxpointsmonth_002: json['txtxpointsmonth_002'] as String? ?? "",
      txtnohistoryavailableyet_001:
          json['txtnohistoryavailableyet_001'] as String? ?? "",
      txt30million_001: json['txt30million_001'] as String? ?? "",
      txtvitalitymembersworldwide_001:
          json['txtvitalitymembersworldwide_001'] as String? ?? "",
      txtvitalitymemberslive_001:
          json['txtvitalitymemberslive_001'] as String? ?? "",
      txtlongerhealthierlives_001:
          json['txtlongerhealthierlives_001'] as String? ?? "",
      txttheworldslargestsciencebased_001:
          json['txttheworldslargestsciencebased_001'] as String? ?? "",
      txtonboardingearnpoints_001:
          json['txtonboardingearnpoints_001'] as String? ?? "",
      txtonboardingvitalitystatus_001:
          json['txtonboardingvitalitystatus_001'] as String? ?? "",
      txtonboardingdiscountonpremium_001:
          json['txtonboardingdiscountonpremium_001'] as String? ?? "",
      txtonboardinggoals_001: json['txtonboardinggoals_001'] as String? ?? "",
      txtonboardingrewardsheader_002:
          json['txtonboardingrewardsheader_002'] as String? ?? "",
      txtonboardingearnpoints_002:
          json['txtonboardingearnpoints_002'] as String? ?? "",
      txtonboardingvitalitystatus_002:
          json['txtonboardingvitalitystatus_002'] as String? ?? "",
      txtonboardingdiscountonpremium_002:
          json['txtonboardingdiscountonpremium_002'] as String? ?? "",
      txtonboardinggoals_002: json['txtonboardinggoals_002'] as String? ?? "",
      txtonboardingrewardscopy_002:
          json['txtonboardingrewardscopy_002'] as String? ?? "",
      ctrl_btnideclare_001: json['ctrl_btnideclare_001'] as String? ?? "",
      title_featured_articles_001:
          json['title_featured_articles_001'] as String? ?? "",
      txtxpoints_003: json['txtxpoints_003'] as String? ?? "",
      txtxpoints_002: json['txtxpoints_002'] as String? ?? "",
      txtyouremissingoutonpointscopy_001:
          json['txtyouremissingoutonpointscopy_001'] as String? ?? "",
      txtyouremissingoutonpoints_001:
          json['txtyouremissingoutonpoints_001'] as String? ?? "",
      txtfuturehealthintro_001:
          json['txtfuturehealthintro_001'] as String? ?? "",
      txtyouareclosetomaximising_001:
          json['txtyouareclosetomaximising_001'] as String? ?? "",
      txtbasedonyourvitalityprofile_002:
          json['txtbasedonyourvitalityprofile_002'] as String? ?? "",
      txtfuturehealthsectionheader_001:
          json['txtfuturehealthsectionheader_001'] as String? ?? "",
      txtfuturehealthsectiontext_001:
          json['txtfuturehealthsectiontext_001'] as String? ?? "",
      txtfuturehealthsectionheader_002:
          json['txtfuturehealthsectionheader_002'] as String? ?? "",
      txtfuturehealthsectiontext_002:
          json['txtfuturehealthsectiontext_002'] as String? ?? "",
      txtbasedonyourvitalityprofile_001:
          json['txtbasedonyourvitalityprofile_001'] as String? ?? "",
      txtyoucanadduptohealthyyears_001:
          json['txtyoucanadduptohealthyyears_001'] as String? ?? "",
      txtbasedonyourvitalityprofile_003:
          json['txtbasedonyourvitalityprofile_003'] as String? ?? "",
      txtfuturehealthsectionheader_003:
          json['txtfuturehealthsectionheader_003'] as String? ?? "",
      txtfuturehealthsectiontext_003:
          json['txtfuturehealthsectiontext_003'] as String? ?? "",
      txtfuturehealthsectiontext_004:
          json['txtfuturehealthsectiontext_004'] as String? ?? "",
      txtyoucanadd10ormorehealthyyears_001:
          json['txtyoucanadd10ormorehealthyyears_001'] as String? ?? "",
      txtyoucanaddmorehealthyyears_001:
          json['txtyoucanaddmorehealthyyears_001'] as String? ?? "",
      txtfuturehealthsectiontext_005:
          json['txtfuturehealthsectiontext_005'] as String? ?? "",
      txtfuturehealthsectiontext_006:
          json['txtfuturehealthsectiontext_006'] as String? ?? "",
      txtbasedonyourvitalityprofile_004:
          json['txtbasedonyourvitalityprofile_004'] as String? ?? "",
      ctrl_submitresultsandproof_001:
          json['ctrl_submitresultsandproof_001'] as String? ?? "",
      ctrl_updateassessmentresults_001:
          json['ctrl_updateassessmentresults_001'] as String? ?? "",
      ctrl_txtmoreaboutsmoking_001:
          json['ctrl_txtmoreaboutsmoking_001'] as String? ?? "",
      ctrl_txtmoreaboutsleep_001:
          json['ctrl_txtmoreaboutsleep_001'] as String? ?? "",
      ctrl_txtmoreaboutbloodglucose_001:
          json['ctrl_txtmoreaboutbloodglucose_001'] as String? ?? "",
      ctrl_txtmoreaboutbodycomposition_001:
          json['ctrl_txtmoreaboutbodycomposition_001'] as String? ?? "",
      ctrl_txtmoreaboutcholesterol_001:
          json['ctrl_txtmoreaboutcholesterol_001'] as String? ?? "",
      ctrl_txtmoreaboutalcoholconsumption_001:
          json['ctrl_txtmoreaboutalcoholconsumption_001'] as String? ?? "",
      ctrl_txtmoreaboutphysicalactivity_001:
          json['ctrl_txtmoreaboutphysicalactivity_001'] as String? ?? "",
      ctrl_txtmoreaboutcardiofitness_001:
          json['ctrl_txtmoreaboutcardiofitness_001'] as String? ?? "",
      ctrl_txtmoreaboutmentalwellbeing_001:
          json['ctrl_txtmoreaboutmentalwellbeing_001'] as String? ?? "",
      ctrl_txtmoreaboutbloodpressure_001:
          json['ctrl_txtmoreaboutbloodpressure_001'] as String? ?? "",
      ctrl_txtmoreaboutnutrition_001:
          json['ctrl_txtmoreaboutnutrition_001'] as String? ?? "",
      txthealthpartnersapps_001:
          json['txthealthpartnersapps_001'] as String? ?? "",
      txtdomoreofthis_001: json['txtdomoreofthis_001'] as String? ?? "",
      txtkeeparecord_001: json['txtkeeparecord_001'] as String? ?? "",
      txtmakeacommitment_001: json['txtmakeacommitment_001'] as String? ?? "",
      ctrl_txtlearnmoreaboutthescience_001:
          json['ctrl_txtlearnmoreaboutthescience_001'] as String? ?? "",
      txtunabletologin_001: json['txtunabletologin_001'] as String? ?? "",
      txtunabletologincopy_001:
          json['txtunabletologincopy_001'] as String? ?? "",
      partner_description_1044:
          json['partner_description_1044'] as String? ?? "",
      partner_description_224: json['partner_description_224'] as String? ?? "",
      partner_description_116: json['partner_description_116'] as String? ?? "",
      partner_description_1008:
          json['partner_description_1008'] as String? ?? "",
      partner_description_128: json['partner_description_128'] as String? ?? "",
      partner_description_129: json['partner_description_129'] as String? ?? "",
      partner_description_1040:
          json['partner_description_1040'] as String? ?? "",
      txtcalculatingyourresults_001:
          json['txtcalculatingyourresults_001'] as String? ?? "",
      txtcalculatingresultssubtext_001:
          json['txtcalculatingresultssubtext_001'] as String? ?? "",
      txtsourcetext_001: json['txtsourcetext_001'] as String? ?? "",
      txtsourcedateonlytext_001:
          json['txtsourcedateonlytext_001'] as String? ?? "",
      txttarget_001: json['txttarget_001'] as String? ?? "",
      partner_description_254: json['partner_description_254'] as String? ?? "",
      partner_description_252: json['partner_description_252'] as String? ?? "",
      partner_description_255: json['partner_description_255'] as String? ?? "",
      partner_description_2025:
          json['partner_description_2025'] as String? ?? "",
      partner_description_1167:
          json['partner_description_1167'] as String? ?? "",
      partner_description_1168:
          json['partner_description_1168'] as String? ?? "",
      partner_description_1161:
          json['partner_description_1161'] as String? ?? "",
      partner_description_1162:
          json['partner_description_1162'] as String? ?? "",
      partner_description_1169:
          json['partner_description_1169'] as String? ?? "",
      partner_description_1170:
          json['partner_description_1170'] as String? ?? "",
      partner_description_2081:
          json['partner_description_2081'] as String? ?? "",
      partner_description_2082:
          json['partner_description_2082'] as String? ?? "",
      partner_description_1124:
          json['partner_description_1124'] as String? ?? "",
      partner_description_1123:
          json['partner_description_1123'] as String? ?? "",
      partner_description_654079:
          json['partner_description_654079'] as String? ?? "",
      partner_description_1065:
          json['partner_description_1065'] as String? ?? "",
      partner_description_654081:
          json['partner_description_654081'] as String? ?? "",
      whatsNew_item1On: json['whatsNew_item1On'] as String? ?? "",
      whatsNew_item2On: json['whatsNew_item2On'] as String? ?? "",
      whatsNew_item3On: json['whatsNew_item3On'] as String? ?? "",
      whatsNew_item4On: json['whatsNew_item4On'] as String? ?? "",
      txtwevemadesomeupdates_001:
          json['txtwevemadesomeupdates_001'] as String? ?? "",
      txtwevemadesomeupdatescopy_001:
          json['txtwevemadesomeupdatescopy_001'] as String? ?? "",
      txtwevemadesomeupdatescopy_002:
          json['txtwevemadesomeupdatescopy_002'] as String? ?? "",
      txtwevemadesomeupdatescopy_003:
          json['txtwevemadesomeupdatescopy_003'] as String? ?? "",
      txtwevemadesomeupdatescopy_004:
          json['txtwevemadesomeupdatescopy_004'] as String? ?? "",
      txtinch_001: json['txtinch_001'] as String? ?? "",
      txtfeet_001: json['txtfeet_001'] as String? ?? "",
      txt_processing_begin_shortly:
          json['txt_processing_begin_shortly'] as String? ?? "",
      txt_processing_file: json['txt_processing_file'] as String? ?? "",
      txt_taking_longer_than_usual:
          json['txt_taking_longer_than_usual'] as String? ?? "",
      txt_no_internet: json['txt_no_internet'] as String? ?? "",
      txt_maximum_file_exceeds:
          json['txt_maximum_file_exceeds'] as String? ?? "",
      txtyourpointsmaynot_001: json['txtyourpointsmaynot_001'] as String? ?? "",
      no_coins_account_threshold_001:
          json['no_coins_account_threshold_001'] as String? ?? "",
      no_account_description_001:
          json['no_account_description_001'] as String? ?? "",
      txtnopromotionbody_002: json['txtnopromotionbody_002'] as String? ?? "",
      ActivityCategoryLabeltxt_81:
          json['ActivityCategoryLabeltxt_81'] as String? ?? "",
      ActivityCategoryLabeltxt_82:
          json['ActivityCategoryLabeltxt_82'] as String? ?? "",
      ActivityCategoryLabeltxt_83:
          json['ActivityCategoryLabeltxt_83'] as String? ?? "",
      ActivityCategoryLabeltxt_84:
          json['ActivityCategoryLabeltxt_84'] as String? ?? "",
      txttime_002: json['txttime_002'] as String? ?? "",
      ctrl_btnstartassessment_001:
          json['ctrl_btnstartassessment_001'] as String? ?? "",
      ctrl_btncontinueassessment_001:
          json['ctrl_btncontinueassessment_001'] as String? ?? "",
      ctrl_btnrestartassessment_001:
          json['ctrl_btnrestartassessment_001'] as String? ?? "",
      txtnextdate_001: json['txtnextdate_001'] as String? ?? "",
      ctrl_btneditresponses_001:
          json['ctrl_btneditresponses_001'] as String? ?? "",
      healtassessment_modalsuccess_txthealthassessmentcomplete_001:
          json['healtassessment_modalsuccess_txthealthassessmentcomplete_001']
                  as String? ??
              "",
      healtassessment_modalsuccess_txthealthassessmentcomplete_002:
          json['healtassessment_modalsuccess_txthealthassessmentcomplete_002']
                  as String? ??
              "",
      ctrl_btncalculateresults_001:
          json['ctrl_btncalculateresults_001'] as String? ?? "",
      txtphysicalactivityandfitness_001:
          json['txtphysicalactivityandfitness_001'] as String? ?? "",
      txtphysicalactivityquestion_001:
          json['txtphysicalactivityquestion_001'] as String? ?? "",
      txtnutrition_001: json['txtnutrition_001'] as String? ?? "",
      txtbiometricsandhealthconditions_001:
          json['txtbiometricsandhealthconditions_001'] as String? ?? "",
      txtmentalhealth_001: json['txtmentalhealth_001'] as String? ?? "",
      txtalcoholandtobacco_001:
          json['txtalcoholandtobacco_001'] as String? ?? "",
      txtsleep_001: json['txtsleep_001'] as String? ?? "",
      txtsleep_guide_001: json['txtsleep_guide_001'] as String? ?? "",
      txtsleep_intro_001: json['txtsleep_intro_001'] as String? ?? "",
      txtsleep_intro_002: json['txtsleep_intro_002'] as String? ?? "",
      txttips_sleep_001: json['txttips_sleep_001'] as String? ?? "",
      txttips_sleep_content_001:
          json['txttips_sleep_content_001'] as String? ?? "",
      txtconsistent_001: json['txtconsistent_001'] as String? ?? "",
      txtconsistent_content_001:
          json['txtconsistent_content_001'] as String? ?? "",
      txtconsistent_content_002:
          json['txtconsistent_content_002'] as String? ?? "",
      txtsleepenvironment_001: json['txtsleepenvironment_001'] as String? ?? "",
      txtsleepenvironment_content_001:
          json['txtsleepenvironment_content_001'] as String? ?? "",
      txtexcercisereg_001: json['txtexcercisereg_001'] as String? ?? "",
      txtexcercisereg_content_001:
          json['txtexcercisereg_content_001'] as String? ?? "",
      txtexcercisereg_content_002:
          json['txtexcercisereg_content_002'] as String? ?? "",
      txtexcercisereg_content_003:
          json['txtexcercisereg_content_003'] as String? ?? "",
      txtexcercisereg_content_004:
          json['txtexcercisereg_content_004'] as String? ?? "",
      txtexcercisereg_content_005:
          json['txtexcercisereg_content_005'] as String? ?? "",
      txtexcercisereg_content_006:
          json['txtexcercisereg_content_006'] as String? ?? "",
      txtexcercisereg_content_007:
          json['txtexcercisereg_content_007'] as String? ?? "",
      txtexcercisereg_content_008:
          json['txtexcercisereg_content_008'] as String? ?? "",
      txtexcercisereg_content_009:
          json['txtexcercisereg_content_009'] as String? ?? "",
      txtexcercisereg_content_010:
          json['txtexcercisereg_content_010'] as String? ?? "",
      txtexcercisereg_content_011:
          json['txtexcercisereg_content_011'] as String? ?? "",
      txtexcercisereg_content_012:
          json['txtexcercisereg_content_012'] as String? ?? "",
      txtexcercisereg_content_013:
          json['txtexcercisereg_content_013'] as String? ?? "",
      txtexcercisereg_content_014:
          json['txtexcercisereg_content_014'] as String? ?? "",
      txtexcercisereg_content_015:
          json['txtexcercisereg_content_015'] as String? ?? "",
      txtexcercisereg_content_016:
          json['txtexcercisereg_content_016'] as String? ?? "",
      txtexcercisereg_content_017:
          json['txtexcercisereg_content_017'] as String? ?? "",
      txtexcercisereg_content_018:
          json['txtexcercisereg_content_018'] as String? ?? "",
      txtroutine_001: json['txtroutine_001'] as String? ?? "",
      txtroutine_content_001: json['txtroutine_content_001'] as String? ?? "",
      txtwatchdrink_001: json['txtwatchdrink_001'] as String? ?? "",
      txtwatchdrink_content_001:
          json['txtwatchdrink_content_001'] as String? ?? "",
      txtwatchdrink_content_002:
          json['txtwatchdrink_content_002'] as String? ?? "",
      ctrl_btnstart_001: json['ctrl_btnstart_001'] as String? ?? "",
      txtactivitiescardsubtext_007:
          json['txtactivitiescardsubtext_007'] as String? ?? "",
      txt_vitality_age_measure_001:
          json['txt_vitality_age_measure_001'] as String? ?? "",
      ctrl_txtsaveandcompletelater_001:
          json['ctrl_txtsaveandcompletelater_001'] as String? ?? "",
      ctrl_btnsubmitassessment_001:
          json['ctrl_btnsubmitassessment_001'] as String? ?? "",
      txtdialogsaveandcompletelater_001:
          json['txtdialogsaveandcompletelater_001'] as String? ?? "",
      txtdialogbodysaveandcompletelater_001:
          json['txtdialogbodysaveandcompletelater_001'] as String? ?? "",
      txtrange_001: json['txtrange_001'] as String? ?? "",
      txtpersonalisegoalsbody_002:
          json['txtpersonalisegoalsbody_002'] as String? ?? "",
      txtpersonalisegoals_002: json['txtpersonalisegoals_002'] as String? ?? "",
      healthassessment_txtdividersleep_001:
          json['healthassessment_txtdividersleep_001'] as String? ?? "",
      healthassessment_txtdivideralcoholandtobacco_001:
          json['healthassessment_txtdivideralcoholandtobacco_001'] as String? ??
              "",
      healthassessment_txtdividermentalhealth_001:
          json['healthassessment_txtdividermentalhealth_001'] as String? ?? "",
      healthassessment_txtdividerbiometrics_001:
          json['healthassessment_txtdividerbiometrics_001'] as String? ?? "",
      healthassessment_txtdividernutrition_001:
          json['healthassessment_txtdividernutrition_001'] as String? ?? "",
      healthassessment_txtdividerphysicalactivity_002:
          json['healthassessment_txtdividerphysicalactivity_002'] as String? ??
              "",
      txtsectionof_001: json['txtsectionof_001'] as String? ?? "",
      txtactivitiescardsubtext_002:
          json['txtactivitiescardsubtext_002'] as String? ?? "",
      healthassessment_txtdividerphysicalactivity_001:
          json['healthassessment_txtdividerphysicalactivity_001'] as String? ??
              "",
      onboarding_modalactivated_txtnowletsgetyouconnected_001:
          json['onboarding_modalactivated_txtnowletsgetyouconnected_001']
                  as String? ??
              "",
      txtareyousure_001: json['txtareyousure_001'] as String? ?? "",
      ctrl_btnyesconnectlater_001:
          json['ctrl_btnyesconnectlater_001'] as String? ?? "",
      txtareyousurecopy_001: json['txtareyousurecopy_001'] as String? ?? "",
      txtthesciencebehindyourhealthmeasures_001:
          json['txtthesciencebehindyourhealthmeasures_001'] as String? ?? "",
      txtsciencesectiontext_001:
          json['txtsciencesectiontext_001'] as String? ?? "",
      txtsciencesectiontext_002:
          json['txtsciencesectiontext_002'] as String? ?? "",
      txtsciencesectiontext_003:
          json['txtsciencesectiontext_003'] as String? ?? "",
      txtsciencesectiontext_004:
          json['txtsciencesectiontext_004'] as String? ?? "",
      txtsciencereference_001: json['txtsciencereference_001'] as String? ?? "",
      txtvitalityhealthpriority_001:
          json['txtvitalityhealthpriority_001'] as String? ?? "",
      txtvitalityfuturehealth_001:
          json['txtvitalityfuturehealth_001'] as String? ?? "",
      txtfooter_002: json['txtfooter_002'] as String? ?? "",
      txtweight_guide_001: json['txtweight_guide_001'] as String? ?? "",
      txtweight_intro_001: json['txtweight_intro_001'] as String? ?? "",
      txtweight_intro_002: json['txtweight_intro_002'] as String? ?? "",
      txttips_weight_001: json['txttips_weight_001'] as String? ?? "",
      txttips_weight_content_001:
          json['txttips_weight_content_001'] as String? ?? "",
      txtrevieweating_001: json['txtrevieweating_001'] as String? ?? "",
      txtrevieweating_content_001:
          json['txtrevieweating_content_001'] as String? ?? "",
      txtrevieweating_content_002:
          json['txtrevieweating_content_002'] as String? ?? "",
      txtimproveyourskills_001:
          json['txtimproveyourskills_001'] as String? ?? "",
      txtimproveyourskills_content_001:
          json['txtimproveyourskills_content_001'] as String? ?? "",
      txtfindsocialsupport_001:
          json['txtfindsocialsupport_001'] as String? ?? "",
      txtfindsocialsupport_content_001:
          json['txtfindsocialsupport_content_001'] as String? ?? "",
      txtfindsocialsupport_content_002:
          json['txtfindsocialsupport_content_002'] as String? ?? "",
      txtpregnancy_guide_001: json['txtpregnancy_guide_001'] as String? ?? "",
      txtpregnancy_intro_001: json['txtpregnancy_intro_001'] as String? ?? "",
      txttips_pregnancy_001: json['txttips_pregnancy_001'] as String? ?? "",
      txttips_pregnancy_content_001:
          json['txttips_pregnancy_content_001'] as String? ?? "",
      txtnutrition_content_001:
          json['txtnutrition_content_001'] as String? ?? "",
      txtnutrition_content_002:
          json['txtnutrition_content_002'] as String? ?? "",
      txtvisitdrearlyandoften_001:
          json['txtvisitdrearlyandoften_001'] as String? ?? "",
      txtvisitdrearlyandoften_content_001:
          json['txtvisitdrearlyandoften_content_001'] as String? ?? "",
      txtthinkbabysafety_001: json['txtthinkbabysafety_001'] as String? ?? "",
      txtthinkbabysafety_content_001:
          json['txtthinkbabysafety_content_001'] as String? ?? "",
      txtavoidharmful_001: json['txtavoidharmful_001'] as String? ?? "",
      txtavoidharmful_content_001:
          json['txtavoidharmful_content_001'] as String? ?? "",
      txtglucose_guide_001: json['txtglucose_guide_001'] as String? ?? "",
      txtglucose_intro_001: json['txtglucose_intro_001'] as String? ?? "",
      txtglucose_intro_002: json['txtglucose_intro_002'] as String? ?? "",
      txtglucose_intro_003: json['txtglucose_intro_003'] as String? ?? "",
      txttips_glucose_001: json['txttips_glucose_001'] as String? ?? "",
      txttips_glucose_content_001:
          json['txttips_glucose_content_001'] as String? ?? "",
      txtmindfulofaddedsugar_001:
          json['txtmindfulofaddedsugar_001'] as String? ?? "",
      txtmindfulofaddedsugar_content_001:
          json['txtmindfulofaddedsugar_content_001'] as String? ?? "",
      txtmindfulofaddedsugar_content_002:
          json['txtmindfulofaddedsugar_content_002'] as String? ?? "",
      txtmindfulofaddedsugar_content_003:
          json['txtmindfulofaddedsugar_content_003'] as String? ?? "",
      txtfibrercihfoods_001: json['txtfibrercihfoods_001'] as String? ?? "",
      txtfibrercihfoods_content_001:
          json['txtfibrercihfoods_content_001'] as String? ?? "",
      txtfibrercihfoods_content_002:
          json['txtfibrercihfoods_content_002'] as String? ?? "",
      txtfibrercihfoods_content_003:
          json['txtfibrercihfoods_content_003'] as String? ?? "",
      btn_filter_by_001: json['btn_filter_by_001'] as String? ?? "",
      filter_category_01: json['filter_category_01'] as String? ?? "",
      txtmembershipyear_001: json['txtmembershipyear_001'] as String? ?? "",
      clear_all_filter_001: json['clear_all_filter_001'] as String? ?? "",
      apply_filter_001: json['apply_filter_001'] as String? ?? "",
      txtDate_001: json['txtDate_001'] as String? ?? "",
      txtactivity_001: json['txtactivity_001'] as String? ?? "",
      txtaverageheartrate_001: json['txtaverageheartrate_001'] as String? ?? "",
      txtmaximumheartrate_001: json['txtmaximumheartrate_001'] as String? ?? "",
      Duration: json['Duration'] as String? ?? "",
      txtsource_001: json['txtsource_001'] as String? ?? "",
      txtburnedcalories_001: json['txtburnedcalories_001'] as String? ?? "",
      txtactivitydetail_001: json['txtactivitydetail_001'] as String? ?? "",
      txttotal_001: json['txttotal_001'] as String? ?? "",
      txtstepcount_001: json['txtstepcount_001'] as String? ?? "",
      heart_rate_bpm_001: json['heart_rate_bpm_001'] as String? ?? "",
      categories_filtered_001: json['categories_filtered_001'] as String? ?? "",
      txtcurrent_001: json['txtcurrent_001'] as String? ?? "",
      txtprevious_001: json['txtprevious_001'] as String? ?? "",
      ctrl_btnfilter_001: json['ctrl_btnfilter_001'] as String? ?? "",
      txtkeepemailaddresspopulated_001:
          json['txtkeepemailaddresspopulated_001'] as String? ?? "",
      txtusefingerprinttologin_001:
          json['txtusefingerprinttologin_001'] as String? ?? "",
      txtloginemailaddress_001:
          json['txtloginemailaddress_001'] as String? ?? "",
      txtcreatenewpassword_001:
          json['txtcreatenewpassword_001'] as String? ?? "",
      txtsavechanges_001: json['txtsavechanges_001'] as String? ?? "",
      txtauthenticationrequired_001:
          json['txtauthenticationrequired_001'] as String? ?? "",
      txtvitalityoverallstatus_001:
          json['txtvitalityoverallstatus_001'] as String? ?? "",
      dialog_body_changingemaillogin_001:
          json['dialog_body_changingemaillogin_001'] as String? ?? "",
      chip_BMI: json['chip_BMI'] as String? ?? "",
      chip_cashback_amount_001:
          json['chip_cashback_amount_001'] as String? ?? "",
      ctrl_btnorder_device_001:
          json['ctrl_btnorder_device_001'] as String? ?? "",
      txtmonthly_Apple_Watch_cashback_001:
          json['txtmonthly_Apple_Watch_cashback_001'] as String? ?? "",
      txtmonthly_Generic_GProduct_cashback_001:
          json['txtmonthly_Generic_GProduct_cashback_001'] as String? ?? "",
      ctrl_btnchoose_device_001:
          json['ctrl_btnchoose_device_001'] as String? ?? "",
      txtuptoXcashback_001: json['txtuptoXcashback_001'] as String? ?? "",
      txt_purchase_device_001: json['txt_purchase_device_001'] as String? ?? "",
      chip_commitment_period_001:
          json['chip_commitment_period_001'] as String? ?? "",
      get_active_001: json['get_active_001'] as String? ?? "",
      txtearncashback_001: json['txtearncashback_001'] as String? ?? "",
      cashback_amount_001: json['cashback_amount_001'] as String? ?? "",
      cashback_amount_002: json['cashback_amount_002'] as String? ?? "",
      txtactivatedevicechallenge_001:
          json['txtactivatedevicechallenge_001'] as String? ?? "",
      previous_device_rewards_001:
          json['previous_device_rewards_001'] as String? ?? "",
      txtscored_001: json['txtscored_001'] as String? ?? "",
      txtcorrectanswer_001: json['txtcorrectanswer_001'] as String? ?? "",
      txtchipnotachieved: json['txtchipnotachieved'] as String? ?? "",
      txtenjoyrewardsandexclusivebenefitsjustforyou_001:
          json['txtenjoyrewardsandexclusivebenefitsjustforyou_001']
                  as String? ??
              "",
      txtmonthlyrewards_001: json['txtmonthlyrewards_001'] as String? ?? "",
      txtstatusrewards_001: json['txtstatusrewards_001'] as String? ?? "",
      txtexclusivebenefits_001:
          json['txtexclusivebenefits_001'] as String? ?? "",
      txtvitalitymemberslikeyou_001:
          json['txtvitalitymemberslikeyou_001'] as String? ?? "",
      ctrl_btnuploadanotherfile_001:
          json['ctrl_btnuploadanotherfile_001'] as String? ?? "",
      ha_section_Iconkey_94: json['ha_section_Iconkey_94'] as String? ?? "",
      ha_section_Iconkey_95: json['ha_section_Iconkey_95'] as String? ?? "",
      ha_section_Iconkey_96: json['ha_section_Iconkey_96'] as String? ?? "",
      ha_section_Iconkey_97: json['ha_section_Iconkey_97'] as String? ?? "",
      ha_section_Iconkey_98: json['ha_section_Iconkey_98'] as String? ?? "",
      ha_section_Iconkey_99: json['ha_section_Iconkey_99'] as String? ?? "",
      txt_apple_watch_001: json['txt_apple_watch_001'] as String? ?? "",
      txtpurchaseapplewatch_001:
          json['txtpurchaseapplewatch_001'] as String? ?? "",
      cashback_amount_003: json['cashback_amount_003'] as String? ?? "",
      txtturnoffnotifications_001:
          json['txtturnoffnotifications_001'] as String? ?? "",
      txthaveaquestiongetintouch_001:
          json['txthaveaquestiongetintouch_001'] as String? ?? "",
      txtwebsite_001: json['txtwebsite_001'] as String? ?? "",
      txtemail_001: json['txtemail_001'] as String? ?? "",
      txtyoureabouttoleaveourapp_001:
          json['txtyoureabouttoleaveourapp_001'] as String? ?? "",
      txtyouarebeingredirectedtowebsite_001:
          json['txtyouarebeingredirectedtowebsite_001'] as String? ?? "",
      txtyouarebeingredirectedtomakeacall_001:
          json['txtyouarebeingredirectedtomakeacall_001'] as String? ?? "",
      txtyouarebeingredirectedtosendanemail_001:
          json['txtyouarebeingredirectedtosendanemail_001'] as String? ?? "",
      dialog_authenticationrequired_001:
          json['dialog_authenticationrequired_001'] as String? ?? "",
      dialog_entercurrentpassword_001:
          json['dialog_entercurrentpassword_001'] as String? ?? "",
      dialog_existingpassword_001:
          json['dialog_existingpassword_001'] as String? ?? "",
      dialog_weveupdatedyourpassword_001:
          json['dialog_weveupdatedyourpassword_001'] as String? ?? "",
      apple_watch_breakout: json['apple_watch_breakout'] as String? ?? "",
      aw_cashbackpts_1: json['aw_cashbackpts_1'] as String? ?? "",
      aw_cashbackpts_2: json['aw_cashbackpts_2'] as String? ?? "",
      aw_cashbackpts_3: json['aw_cashbackpts_3'] as String? ?? "",
      aw_cashbackpts_4: json['aw_cashbackpts_4'] as String? ?? "",
      genericfd_cashbackpts_1: json['genericfd_cashbackpts_1'] as String? ?? "",
      genericfd_cashbackpts_2: json['genericfd_cashbackpts_2'] as String? ?? "",
      genericfd_cashbackpts_3: json['genericfd_cashbackpts_3'] as String? ?? "",
      genericfd_cashbackpts_4: json['genericfd_cashbackpts_4'] as String? ?? "",
      aw_circlecontent_1: json['aw_circlecontent_1'] as String? ?? "",
      aw_circlecontent_2: json['aw_circlecontent_2'] as String? ?? "",
      aw_circlecontent_3: json['aw_circlecontent_3'] as String? ?? "",
      aw_circlecontent_4: json['aw_circlecontent_4'] as String? ?? "",
      genericfd_circlecontent_1:
          json['genericfd_circlecontent_1'] as String? ?? "",
      genericfd_circlecontent_2:
          json['genericfd_circlecontent_2'] as String? ?? "",
      genericfd_circlecontent_3:
          json['genericfd_circlecontent_3'] as String? ?? "",
      genericfd_circlecontent_4:
          json['genericfd_circlecontent_4'] as String? ?? "",
      aw_cashback_1: json['aw_cashback_1'] as String? ?? "",
      aw_cashback_2: json['aw_cashback_2'] as String? ?? "",
      aw_cashback_3: json['aw_cashback_3'] as String? ?? "",
      aw_cashback_4: json['aw_cashback_4'] as String? ?? "",
      genericfd_cashback_1: json['genericfd_cashback_1'] as String? ?? "",
      genericfd_cashback_2: json['genericfd_cashback_2'] as String? ?? "",
      genericfd_cashback_3: json['genericfd_cashback_3'] as String? ?? "",
      genericfd_cashback_4: json['genericfd_cashback_4'] as String? ?? "",
      txtearn_reward_001: json['txtearn_reward_001'] as String? ?? "",
      txtearn_reward_002: json['txtearn_reward_002'] as String? ?? "",
      chip_cashback_fd_001: json['chip_cashback_fd_001'] as String? ?? "",
      chip_commitment_period_002:
          json['chip_commitment_period_002'] as String? ?? "",
      txtpurchasedevice_001: json['txtpurchasedevice_001'] as String? ?? "",
      txtyourfirstgoalstartson_date_001:
          json['txtyourfirstgoalstartson_date_001'] as String? ?? "",
      txtuptoxperc_cashback_001:
          json['txtuptoxperc_cashback_001'] as String? ?? "",
      txtcashbackstartssoon_001:
          json['txtcashbackstartssoon_001'] as String? ?? "",
      device_invoice_number_001:
          json['device_invoice_number_001'] as String? ?? "",
      device_purchase_date_001:
          json['device_purchase_date_001'] as String? ?? "",
      device_purchase_amount_001:
          json['device_purchase_amount_001'] as String? ?? "",
      txtchooseadevice_001: json['txtchooseadevice_001'] as String? ?? "",
      txtonedevicecashback_001:
          json['txtonedevicecashback_001'] as String? ?? "",
      txttotal_002: json['txttotal_002'] as String? ?? "",
      cashback_effective_date_001:
          json['cashback_effective_date_001'] as String? ?? "",
      cashback_months_completed_001:
          json['cashback_months_completed_001'] as String? ?? "",
      device_order_information_001:
          json['device_order_information_001'] as String? ?? "",
      cashback_history_001: json['cashback_history_001'] as String? ?? "",
      monthly_device_cashback_001:
          json['monthly_device_cashback_001'] as String? ?? "",
      txtwonreward_001_2277: json['txtwonreward_001_2277'] as String? ?? "",
      txtachieveyourgoalstoearncoins_001:
          json['txtachieveyourgoalstoearncoins_001'] as String? ?? "",
      txtwonreward_002_2277: json['txtwonreward_002_2277'] as String? ?? "",
      txtwonreward_004_2277: json['txtwonreward_004_2277'] as String? ?? "",
      txtachieveyourgoalstoearngiftcards_001:
          json['txtachieveyourgoalstoearngiftcards_001'] as String? ?? "",
      txt_vhc_disclaimer_001: json['txt_vhc_disclaimer_001'] as String? ?? "",
      txthowvitalityworks_001: json['txthowvitalityworks_001'] as String? ?? "",
      txtpointsandstatus_002: json['txtpointsandstatus_002'] as String? ?? "",
      txtcoinsandgiftcards_001:
          json['txtcoinsandgiftcards_001'] as String? ?? "",
      txtmonthlygoalsandrewards_001:
          json['txtmonthlygoalsandrewards_001'] as String? ?? "",
      ctrl_txtcontactus_001: json['ctrl_txtcontactus_001'] as String? ?? "",
      txtinformationaboutyourdevice_001:
          json['txtinformationaboutyourdevice_001'] as String? ?? "",
      txthowcanwehelpyou_001: json['txthowcanwehelpyou_001'] as String? ?? "",
      ctrl_btnupload_002: json['ctrl_btnupload_002'] as String? ?? "",
      ctrl_txtabout_001: json['ctrl_txtabout_001'] as String? ?? "",
      txtexperiencingtechnicalissue_001:
          json['txtexperiencingtechnicalissue_001'] as String? ?? "",
      txtneedhelpwithvitalioty_001:
          json['txtneedhelpwithvitalioty_001'] as String? ?? "",
      txtlabelfeedbacktype_001:
          json['txtlabelfeedbacktype_001'] as String? ?? "",
      txtalertfeedback_001: json['txtalertfeedback_001'] as String? ?? "",
      txtdialogheaderfeedback_001:
          json['txtdialogheaderfeedback_001'] as String? ?? "",
      txtdialogbodyfeedback_001:
          json['txtdialogbodyfeedback_001'] as String? ?? "",
      txt_onboarding_reward_imagepath_001:
          json['txt_onboarding_reward_imagepath_001'] as String? ?? "",
      txt_onboarding3_header_vitalityactivefull:
          json['txt_onboarding3_header_vitalityactivefull'] as String? ?? "",
      txt_onboarding3_subtext_vitalityactivefull:
          json['txt_onboarding3_subtext_vitalityactivefull'] as String? ?? "",
      txt_onboarding4_header_vitalityactivefull:
          json['txt_onboarding4_header_vitalityactivefull'] as String? ?? "",
      txt_onboarding4_subtext_vitalityactivefull:
          json['txt_onboarding4_subtext_vitalityactivefull'] as String? ?? "",
      txt_onboarding5_header_vitalityactivefull:
          json['txt_onboarding5_header_vitalityactivefull'] as String? ?? "",
      txt_onboarding5_subtext_vitalityactivefull:
          json['txt_onboarding5_subtext_vitalityactivefull'] as String? ?? "",
      txt_onboarding_reward_imagepath_vitalityactivefull:
          json['txt_onboarding_reward_imagepath_vitalityactivefull']
                  as String? ??
              "",
      txt_onboarding3_header_vitalityactivelite:
          json['txt_onboarding3_header_vitalityactivelite'] as String? ?? "",
      txt_onboarding3_subtext_vitalityactivelite:
          json['txt_onboarding3_subtext_vitalityactivelite'] as String? ?? "",
      txt_onboarding4_header_vitalityactivelite:
          json['txt_onboarding4_header_vitalityactivelite'] as String? ?? "",
      txt_onboarding4_subtext_vitalityactivelite:
          json['txt_onboarding4_subtext_vitalityactivelite'] as String? ?? "",
      txt_onboarding5_header_vitalityactivelite:
          json['txt_onboarding5_header_vitalityactivelite'] as String? ?? "",
      txt_onboarding5_subtext_vitalityactivelite:
          json['txt_onboarding5_subtext_vitalityactivelite'] as String? ?? "",
      txt_onboarding_reward_imagepath_vitalityactivelite:
          json['txt_onboarding_reward_imagepath_vitalityactivelite']
                  as String? ??
              "",
      txt_onboarding3_header_individual:
          json['txt_onboarding3_header_individual'] as String? ?? "",
      txt_onboarding3_subtext_individual:
          json['txt_onboarding3_subtext_individual'] as String? ?? "",
      txt_onboarding4_header_individual:
          json['txt_onboarding4_header_individual'] as String? ?? "",
      txt_onboarding4_subtext_individual:
          json['txt_onboarding4_subtext_individual'] as String? ?? "",
      txt_onboarding5_header_individual:
          json['txt_onboarding5_header_individual'] as String? ?? "",
      txt_onboarding5_subtext_individual:
          json['txt_onboarding5_subtext_individual'] as String? ?? "",
      txt_onboarding_reward_imagepath_individual:
          json['txt_onboarding_reward_imagepath_individual'] as String? ?? "",
      txt_onboarding3_header_sme:
          json['txt_onboarding3_header_sme'] as String? ?? "",
      txt_onboarding3_subtext_sme:
          json['txt_onboarding3_subtext_sme'] as String? ?? "",
      txt_onboarding4_header_sme:
          json['txt_onboarding4_header_sme'] as String? ?? "",
      txt_onboarding4_subtext_sme:
          json['txt_onboarding4_subtext_sme'] as String? ?? "",
      txt_onboarding5_header_sme:
          json['txt_onboarding5_header_sme'] as String? ?? "",
      txt_onboarding5_subtext_sme:
          json['txt_onboarding5_subtext_sme'] as String? ?? "",
      txt_onboarding_reward_imagepath_sme:
          json['txt_onboarding_reward_imagepath_sme'] as String? ?? "",
      txt_onboarding3_header_ind_sme:
          json['txt_onboarding3_header_ind_sme'] as String? ?? "",
      txt_onboarding3_subtext_ind_sme:
          json['txt_onboarding3_subtext_ind_sme'] as String? ?? "",
      txt_onboarding4_header_ind_sme:
          json['txt_onboarding4_header_ind_sme'] as String? ?? "",
      txt_onboarding4_subtext_ind_sme:
          json['txt_onboarding4_subtext_ind_sme'] as String? ?? "",
      txt_onboarding5_header_ind_sme:
          json['txt_onboarding5_header_ind_sme'] as String? ?? "",
      txt_onboarding5_subtext_ind_sme:
          json['txt_onboarding5_subtext_ind_sme'] as String? ?? "",
      txt_onboarding_reward_imagepath_ind_sme:
          json['txt_onboarding_reward_imagepath_ind_sme'] as String? ?? "",
      teamchallenges_extend_already_dialog_description_001:
          json['teamchallenges_extend_already_dialog_description_001']
                  as String? ??
              "",
      teamchallenges_extend_already_dialog_title_001:
          json['teamchallenges_extend_already_dialog_title_001'] as String? ??
              "",
      teamchallenges_extend_button_001:
          json['teamchallenges_extend_button_001'] as String? ?? "",
      teamchallenges_extend_dialog_message_001:
          json['teamchallenges_extend_dialog_message_001'] as String? ?? "",
      teamchallenges_extend_dialog_title_001:
          json['teamchallenges_extend_dialog_title_001'] as String? ?? "",
      teamchallenges_extend_first_week_dialog_description_001:
          json['teamchallenges_extend_first_week_dialog_description_001']
                  as String? ??
              "",
      teamchallenges_extend_first_week_dialog_title_001:
          json['teamchallenges_extend_first_week_dialog_title_001']
                  as String? ??
              "",
      teamchallenges_extend_success_001:
          json['teamchallenges_extend_success_001'] as String? ?? "",
      teamchallenges_manage_data_consent_title_6346:
          json['teamchallenges_manage_data_consent_title_6346'] as String? ??
              "",
      teamchallenges_manage_leave_team_title_6347:
          json['teamchallenges_manage_leave_team_title_6347'] as String? ?? "",
      teamchallenges_manage_delete_team_title_6348:
          json['teamchallenges_manage_delete_team_title_6348'] as String? ?? "",
      teamchallenges_manage_createdby_title_6349:
          json['teamchallenges_manage_createdby_title_6349'] as String? ?? "",
      teamchallenges_manage_team_invite_6350:
          json['teamchallenges_manage_team_invite_6350'] as String? ?? "",
      teamchallenges_manage_leave_messgae_6351:
          json['teamchallenges_manage_leave_messgae_6351'] as String? ?? "",
      teamchallenges_manage_rejoin_team_title_6352:
          json['teamchallenges_manage_rejoin_team_title_6352'] as String? ?? "",
      teamchallenges_manage_cancel_title_6353:
          json['teamchallenges_manage_cancel_title_6353'] as String? ?? "",
      teamchallenges_manage_leave_title_6354:
          json['teamchallenges_manage_leave_title_6354'] as String? ?? "",
      teamchallenges_manage_delete_team_title_6355:
          json['teamchallenges_manage_delete_team_title_6355'] as String? ?? "",
      teamchallenges_manage_undo_title_6356:
          json['teamchallenges_manage_undo_title_6356'] as String? ?? "",
      proof_attachments_delete_button_2349:
          json['proof_attachments_delete_button_2349'] as String? ?? "",
      teamchallenges_invite_team_sub_title_6336:
          json['teamchallenges_invite_team_sub_title_6336'] as String? ?? "",
      teamchallenges_invite_team_main_title_6337:
          json['teamchallenges_invite_team_main_title_6337'] as String? ?? "",
      teamchallenges_invite_team_progress_6343:
          json['teamchallenges_invite_team_progress_6343'] as String? ?? "",
      teamchallenges_invite_team_manage_button_6344:
          json['teamchallenges_invite_team_manage_button_6344'] as String? ??
              "",
      VHR_Onboarding_title_7182:
          json['VHR_Onboarding_title_7182'] as String? ?? "",
      teamchallenges_invite_team_button_6335:
          json['teamchallenges_invite_team_button_6335'] as String? ?? "",
      teamchallenges_chip_steps:
          json['teamchallenges_chip_steps'] as String? ?? "",
      teamchallenges_your_team_now_active:
          json['teamchallenges_your_team_now_active'] as String? ?? "",
      teamchallenges_btn_got_it:
          json['teamchallenges_btn_got_it'] as String? ?? "",
      txt_team_created_001: json['txt_team_created_001'] as String? ?? "",
      chip_applewatch_cashback_amount_001:
          json['chip_applewatch_cashback_amount_001'] as String? ?? "",
      chip_applewatch_commitment_period_001:
          json['chip_applewatch_commitment_period_001'] as String? ?? "",
      chip_garmin_cashback_fd_001:
          json['chip_garmin_cashback_fd_001'] as String? ?? "",
      chip_garmin_commitment_period_002:
          json['chip_garmin_commitment_period_002'] as String? ?? "",
      chip_genp_cashback_amount_001:
          json['chip_genp_cashback_amount_001'] as String? ?? "",
      chip_genpcommitment_period_001:
          json['chip_genpcommitment_period_001'] as String? ?? "",
      chip_fitbit_cashback_fd_001:
          json['chip_fitbit_cashback_fd_001'] as String? ?? "",
      chip_fitbit_commitment_period_002:
          json['chip_fitbit_commitment_period_002'] as String? ?? "",
      chip_samsung_cashback_fd_001:
          json['chip_samsung_cashback_fd_001'] as String? ?? "",
      chip_samsung_commitment_period_002:
          json['chip_samsung_commitment_period_002'] as String? ?? "",
      chip_polar_cashback_fd_001:
          json['chip_polar_cashback_fd_001'] as String? ?? "",
      chip_polar_commitment_period_002:
          json['chip_polar_commitment_period_002'] as String? ?? "",
      txtpurchase_polar_device_001:
          json['txtpurchase_polar_device_001'] as String? ?? "",
      txtpurchase_samsung_device_001:
          json['txtpurchase_samsung_device_001'] as String? ?? "",
      txtpurchase_fitbit_device_001:
          json['txtpurchase_fitbit_device_001'] as String? ?? "",
      txtpurchase_garmin_device_001:
          json['txtpurchase_garmin_device_001'] as String? ?? "",
      txtpurchase_genprod_001: json['txtpurchase_genprod_001'] as String? ?? "",
      old_wheel_txtdialogbodyfeedback_001:
          json['old_wheel_txtdialogbodyfeedback_001'] as String? ?? "",
      old_wheel_txtswipequicklytospin_001:
          json['old_wheel_txtswipequicklytospin_001'] as String? ?? "",
      old_wheel_txttargetachieved_001:
          json['old_wheel_txttargetachieved_001'] as String? ?? "",
      old_wheel_ctrl_btnspinnow_001:
          json['old_wheel_ctrl_btnspinnow_001'] as String? ?? "",
      old_wheel_ctrl_btnparticipatingpartners_001:
          json['old_wheel_ctrl_btnparticipatingpartners_001'] as String? ?? "",
      old_wheel_ctrl_btnswapforotherreward_001:
          json['old_wheel_ctrl_btnswapforotherreward_001'] as String? ?? "",
      old_wheel_txtswapforotherreward_001:
          json['old_wheel_txtswapforotherreward_001'] as String? ?? "",
      old_wheel_ctrl_btnconfirmnewreward_001:
          json['old_wheel_ctrl_btnconfirmnewreward_001'] as String? ?? "",
      old_wheel_txtswapforotherrewardfooter_001:
          json['old_wheel_txtswapforotherrewardfooter_001'] as String? ?? "",
      old_wheel_txtyourcurrentreward_001:
          json['old_wheel_txtyourcurrentreward_001'] as String? ?? "",
      old_wheel_txtwhatsonthewheel_001:
          json['old_wheel_txtwhatsonthewheel_001'] as String? ?? "",
      old_wheel_txtmakeselection_001:
          json['old_wheel_txtmakeselection_001'] as String? ?? "",
      old_wheel_txtthewheelistakingawhiletoload_001:
          json['old_wheel_txtthewheelistakingawhiletoload_001'] as String? ??
              "",
      old_wheel_txtwouldyouliketocontinuetowaitortrylater_001:
          json['old_wheel_txtwouldyouliketocontinuetowaitortrylater_001']
                  as String? ??
              "",
      old_wheel_ctrl_btnwait_001:
          json['old_wheel_ctrl_btnwait_001'] as String? ?? "",
      old_wheel_ctrl_btntrylater_001:
          json['old_wheel_ctrl_btntrylater_001'] as String? ?? "",
      old_wheel_txtthewheelistemporarilyunavailable_001:
          json['old_wheel_txtthewheelistemporarilyunavailable_001']
                  as String? ??
              "",
      old_wheel_dialog_body_: json['old_wheel_dialog_body_'] as String? ?? "",
      super_roulette_txtwaytogoyouearneda_superwheelspin_001:
          json['super_roulette_txtwaytogoyouearneda_superwheelspin_001']
                  as String? ??
              "",
      super_roulette_txtswipequicklytospin_001:
          json['super_roulette_txtswipequicklytospin_001'] as String? ?? "",
      super_roulette_ctrl_txtwhatsonthewheel_001:
          json['super_roulette_ctrl_txtwhatsonthewheel_001'] as String? ?? "",
      super_roulette_dialog_hd:
          json['super_roulette_dialog_hd'] as String? ?? "",
      super_roulette_dialog_body:
          json['super_roulette_dialog_body'] as String? ?? "",
      txtyourdeviceisonitsway_apple_001:
          json['txtyourdeviceisonitsway_apple_001'] as String? ?? "",
      txtyourdeviceisonitsway_garmin_001:
          json['txtyourdeviceisonitsway_garmin_001'] as String? ?? "",
      txtyourdeviceisonitsway_fitbit_001:
          json['txtyourdeviceisonitsway_fitbit_001'] as String? ?? "",
      txtyourdeviceisonitsway_samsung_001:
          json['txtyourdeviceisonitsway_samsung_001'] as String? ?? "",
      txtyourdeviceisonitsway_polar_001:
          json['txtyourdeviceisonitsway_polar_001'] as String? ?? "",
      teamchallenges_team_enddate_title_001:
          json['teamchallenges_team_enddate_title_001'] as String? ?? "",
      teamchallenges_team_created_001:
          json['teamchallenges_team_created_001'] as String? ?? "",
      teamchallenges_watch_team_progress_001:
          json['teamchallenges_watch_team_progress_001'] as String? ?? "",
      teamchallenges_successfully_joined_team_001:
          json['teamchallenges_successfully_joined_team_001'] as String? ?? "",
      teamchallenges_reached_the_maximum_capacity_001:
          json['teamchallenges_reached_the_maximum_capacity_001'] as String? ??
              "",
      txt2factorverificationbody_001:
          json['txt2factorverificationbody_001'] as String? ?? "",
      txt2factorverificationcopy_002:
          json['txt2factorverificationcopy_002'] as String? ?? "",
      txt2factorverificationenabled_001:
          json['txt2factorverificationenabled_001'] as String? ?? "",
      txt2factorverificationenabled_002:
          json['txt2factorverificationenabled_002'] as String? ?? "",
      txt2factorverificationheader_001:
          json['txt2factorverificationheader_001'] as String? ?? "",
      ctrL_btnenable_001: json['ctrL_btnenable_001'] as String? ?? "",
      ctrL_btnnothanksmaybelater_001:
          json['ctrL_btnnothanksmaybelater_001'] as String? ?? "",
      txtpleaseselectyourmethodofverification_001:
          json['txtpleaseselectyourmethodofverification_001'] as String? ?? "",
      txtverifywithemail_001: json['txtverifywithemail_001'] as String? ?? "",
      txtverifywithemail_002: json['txtverifywithemail_002'] as String? ?? "",
      txtverifywithemailbody_002:
          json['txtverifywithemailbody_002'] as String? ?? "",
      txtverifywithtext_001: json['txtverifywithtext_001'] as String? ?? "",
      txtverifywithtextbody_001:
          json['txtverifywithtextbody_001'] as String? ?? "",
      ctrL_btnselect_001: json['ctrL_btnselect_001'] as String? ?? "",
      txtphonenumber_001: json['txtphonenumber_001'] as String? ?? "",
      ctr__btnsendverificationcode_001:
          json['ctr__btnsendverificationcode_001'] as String? ?? "",
      ctr_btnemail_001: json['ctr_btnemail_001'] as String? ?? "",
      ctrL_btnresendverificationcode_001:
          json['ctrL_btnresendverificationcode_001'] as String? ?? "",
      txtenterverificationcode_001:
          json['txtenterverificationcode_001'] as String? ?? "",
      txtenterverificationcode_002:
          json['txtenterverificationcode_002'] as String? ?? "",
      ctrL_btnsubmit_001: json['ctrL_btnsubmit_001'] as String? ?? "",
      ctrL_btndone_001: json['ctrL_btndone_001'] as String? ?? "",
      txtyouhaveenteredthewrongcodepleasetryagain_001:
          json['txtyouhaveenteredthewrongcodepleasetryagain_001'] as String? ??
              "",
      txtyouhaveenteredthewrongcodepleasetryagain_002:
          json['txtyouhaveenteredthewrongcodepleasetryagain_002'] as String? ??
              "",
      ctrL_btncancel_001: json['ctrL_btncancel_001'] as String? ?? "",
      ctrL_btntryagain_001: json['ctrL_btntryagain_001'] as String? ?? "",
      ctrL_btnlogout_001: json['ctrL_btnlogout_001'] as String? ?? "",
      ctrL_btnresetpassword_001:
          json['ctrL_btnresetpassword_001'] as String? ?? "",
      txtsomethinghasgonewrong_001:
          json['txtsomethinghasgonewrong_001'] as String? ?? "",
      txtsomethinghasgonewrong_002:
          json['txtsomethinghasgonewrong_002'] as String? ?? "",
      txtincorrectcode_001: json['txtincorrectcode_001'] as String? ?? "",
      txtincorrectcode_002: json['txtincorrectcode_002'] as String? ?? "",
      ctrL_btnok_001: json['ctrL_btnok_001'] as String? ?? "",
      txtcodeexpired_001: json['txtcodeexpired_001'] as String? ?? "",
      txtcodeexpired_002: json['txtcodeexpired_002'] as String? ?? "",
      ctrL_btnsendnewcode_001: json['ctrL_btnsendnewcode_001'] as String? ?? "",
      txtuse2faverification_001:
          json['txtuse2faverification_001'] as String? ?? "",
      txtupdate2faverification_001:
          json['txtupdate2faverification_001'] as String? ?? "",
      txtcurrentmethod_001: json['txtcurrentmethod_001'] as String? ?? "",
      txtchoosemethod_001: json['txtchoosemethod_001'] as String? ?? "",
      tc_txtphysicalactivitygoal_001:
          json['tc_txtphysicalactivitygoal_001'] as String? ?? "",
      tc_txtgetproof_001: json['tc_txtgetproof_001'] as String? ?? "",
      tc_txtautotracked_001: json['tc_txtautotracked_001'] as String? ?? "",
      tc_ctrl_btnselectgoal_001:
          json['tc_ctrl_btnselectgoal_001'] as String? ?? "",
      tc_ctrl_btn_001: json['tc_ctrl_btn_001'] as String? ?? "",
      tc_txthowtocomplete_001: json['tc_txthowtocomplete_001'] as String? ?? "",
      tc_txttakeatwominutemovementbreakeverywakinghour_001:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_001']
                  as String? ??
              "",
      tc_txttakeatwominutemovementbreakeverywakinghour_002:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_002']
                  as String? ??
              "",
      tc_txttakeatwominutemovementbreakeverywakinghour_002_1:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_002_1']
                  as String? ??
              "",
      tc_txttakeatwominutemovementbreakeverywakinghour_002_2:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_002_2']
                  as String? ??
              "",
      tc_txttakeatwominutemovementbreakeverywakinghour_002_3:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_002_3']
                  as String? ??
              "",
      tc_txttakeatwominutemovementbreakeverywakinghour_002_4:
          json['tc_txttakeatwominutemovementbreakeverywakinghour_002_4']
                  as String? ??
              "",
      tc_txtcheckintoyourgoalthesameday_002:
          json['tc_txtcheckintoyourgoalthesameday_002'] as String? ?? "",
      tc_txtcheckintoyourgoalthesameday_001:
          json['tc_txtcheckintoyourgoalthesameday_001'] as String? ?? "",
      tc_leaving_a_team_001: json['tc_leaving_a_team_001'] as String? ?? "",
      tc_leaving_a_team_002: json['tc_leaving_a_team_002'] as String? ?? "",
      tc_txtwhyitsimportant_001:
          json['tc_txtwhyitsimportant_001'] as String? ?? "",
      tc_wlg_healthcheck_txbodytip_001:
          json['tc_wlg_healthcheck_txbodytip_001'] as String? ?? "",
      tc_txthowtocomplete_002: json['tc_txthowtocomplete_002'] as String? ?? "",
      tc_txtsubmitresultsandproofexplanationcopy_001:
          json['tc_txtsubmitresultsandproofexplanationcopy_001'] as String? ??
              "",
      tc_label_001: json['tc_label_001'] as String? ?? "",
      tc_ctrl_btnnext_001: json['tc_ctrl_btnnext_001'] as String? ?? "",
      tc_dialog_hd_001: json['tc_dialog_hd_001'] as String? ?? "",
      tc_dialog_body_001: json['tc_dialog_body_001'] as String? ?? "",
      tc_dialog_hd_002: json['tc_dialog_hd_002'] as String? ?? "",
      tc_dialog_body_002: json['tc_dialog_body_002'] as String? ?? "",
      tc_btn_Agree: json['tc_btn_Agree'] as String? ?? "",
      tc_btn_Disagree: json['tc_btn_Disagree'] as String? ?? "",
      tc_txthdunknwnerr_001: json['tc_txthdunknwnerr_001'] as String? ?? "",
      tc_txtbdunknwnerr_002: json['tc_txtbdunknwnerr_002'] as String? ?? "",
      tc_ctrl_btncancel_001: json['tc_ctrl_btncancel_001'] as String? ?? "",
      tc_ctrl_btnyeslogout_001:
          json['tc_ctrl_btnyeslogout_001'] as String? ?? "",
      tc_txt_001: json['tc_txt_001'] as String? ?? "",
      tc_txtsubmitresultsandproofexplanationcopy_002:
          json['tc_txtsubmitresultsandproofexplanationcopy_002'] as String? ??
              "",
      tc_label_002: json['tc_label_002'] as String? ?? "",
      tc_ctrl_btnnext_002: json['tc_ctrl_btnnext_002'] as String? ?? "",
      tc_txtbdunknwnerr_003: json['tc_txtbdunknwnerr_003'] as String? ?? "",
      txtaccountlocked_001: json['txtaccountlocked_001'] as String? ?? "",
      txtaccountlockedcopy_001:
          json['txtaccountlockedcopy_001'] as String? ?? "",
      ctrl_btnactivatereward_001:
          json['ctrl_btnactivatereward_001'] as String? ?? "",
      txtmonthlyhealthyfoodcashback_001:
          json['txtmonthlyhealthyfoodcashback_001'] as String? ?? "",
      txt_requiredactivity_001:
          json['txt_requiredactivity_001'] as String? ?? "",
      txtpurchasehealthy_food_001:
          json['txtpurchasehealthy_food_001'] as String? ?? "",
      txtearn_hf_reward_001: json['txtearn_hf_reward_001'] as String? ?? "",
      txtearn_hf_reward_002: json['txtearn_hf_reward_002'] as String? ?? "",
      txtexercise_001: json['txtexercise_001'] as String? ?? "",
      txtpointsperday_001: json['txtpointsperday_001'] as String? ?? "",
      txtmanual_gym_checkin: json['txtmanual_gym_checkin'] as String? ?? "",
      ctrl_btngymcheckin_001: json['ctrl_btngymcheckin_001'] as String? ?? "",
      ctrl_btnconnectanappordevice_001:
          json['ctrl_btnconnectanappordevice_001'] as String? ?? "",
      hf_footer_email_001: json['hf_footer_email_001'] as String? ?? "",
      txt_hf_footer_001: json['txt_hf_footer_001'] as String? ?? "",
      chip_cashback_HF_001: json['chip_cashback_HF_001'] as String? ?? "",
      highest_cashback_achieved:
          json['highest_cashback_achieved'] as String? ?? "",
      cashback_date_range_001: json['cashback_date_range_001'] as String? ?? "",
      txtxpointsmonth_001: json['txtxpointsmonth_001'] as String? ?? "",
      txtcashbackmonth_001: json['txtcashbackmonth_001'] as String? ?? "",
      cashback_calc_day_001: json['cashback_calc_day_001'] as String? ?? "",
      total_cashback_002: json['total_cashback_002'] as String? ?? "",
      Total_cashback_earned_001:
          json['Total_cashback_earned_001'] as String? ?? "",
      txtyourprogress_001: json['txtyourprogress_001'] as String? ?? "",
      levels_cashbackpts_range:
          json['levels_cashbackpts_range'] as String? ?? "",
      levels_cashbackpts_range_last:
          json['levels_cashbackpts_range_last'] as String? ?? "",
      healthy_food_complete_hc_001:
          json['healthy_food_complete_hc_001'] as String? ?? "",
      Supermaxi_membership_001:
          json['Supermaxi_membership_001'] as String? ?? "",
      hf_onboarding_level_points_004:
          json['hf_onboarding_level_points_004'] as String? ?? "",
      hf_onboarding_level_points_003:
          json['hf_onboarding_level_points_003'] as String? ?? "",
      hf_onboarding_level_points_002:
          json['hf_onboarding_level_points_002'] as String? ?? "",
      hf_onboarding_level_points_001:
          json['hf_onboarding_level_points_001'] as String? ?? "",
      hf_onboarding_cycle_cashback_004:
          json['hf_onboarding_cycle_cashback_004'] as String? ?? "",
      hf_onboarding_cycle_cashback_003:
          json['hf_onboarding_cycle_cashback_003'] as String? ?? "",
      hf_onboarding_cycle_cashback_002:
          json['hf_onboarding_cycle_cashback_002'] as String? ?? "",
      hf_onboarding_cycle_cashback_001:
          json['hf_onboarding_cycle_cashback_001'] as String? ?? "",
      hf_onboarding_cashback_004:
          json['hf_onboarding_cashback_004'] as String? ?? "",
      hf_onboarding_cashback_003:
          json['hf_onboarding_cashback_003'] as String? ?? "",
      hf_onboarding_cashback_002:
          json['hf_onboarding_cashback_002'] as String? ?? "",
      hf_onboarding_cashback_001:
          json['hf_onboarding_cashback_001'] as String? ?? "",
      hf_visit_website: json['hf_visit_website'] as String? ?? "",
      question_input_hint_102503:
          json['question_input_hint_102503'] as String? ?? "",
      question_input_hint_102452:
          json['question_input_hint_102452'] as String? ?? "",
      question_input_hint_100111:
          json['question_input_hint_100111'] as String? ?? "",
      question_input_hint_100105:
          json['question_input_hint_100105'] as String? ?? "",
      question_input_hint_100102:
          json['question_input_hint_100102'] as String? ?? "",
      question_input_hint_100109:
          json['question_input_hint_100109'] as String? ?? "",
      question_input_hint_102476:
          json['question_input_hint_102476'] as String? ?? "",
      question_input_hint_100113:
          json['question_input_hint_100113'] as String? ?? "",
      question_input_hint_100118:
          json['question_input_hint_100118'] as String? ?? "",
      question_input_hint_100123:
          json['question_input_hint_100123'] as String? ?? "",
      question_input_hint_100112:
          json['question_input_hint_100112'] as String? ?? "",
      question_input_hint_102501:
          json['question_input_hint_102501'] as String? ?? "",
      question_input_hint_100114:
          json['question_input_hint_100114'] as String? ?? "",
      question_input_hint_101627:
          json['question_input_hint_101627'] as String? ?? "",
      question_input_hint_101626:
          json['question_input_hint_101626'] as String? ?? "",
      question_input_hint_100120:
          json['question_input_hint_100120'] as String? ?? "",
      question_input_hint_101505:
          json['question_input_hint_101505'] as String? ?? "",
      question_input_hint_100127:
          json['question_input_hint_100127'] as String? ?? "",
      question_input_hint_100128:
          json['question_input_hint_100128'] as String? ?? "",
      question_input_hint_height_ft:
          json['question_input_hint_height_ft'] as String? ?? "",
      question_input_hint_height_in:
          json['question_input_hint_height_in'] as String? ?? "",
      question_input_hint_height_cm:
          json['question_input_hint_height_cm'] as String? ?? "",
      question_input_hint_420000001:
          json['question_input_hint_420000001'] as String? ?? "",
      question_input_hint_310000075:
          json['question_input_hint_310000075'] as String? ?? "",
      question_input_hint_410000001:
          json['question_input_hint_410000001'] as String? ?? "",
      healtassessment_modalsuccess_txthealthassessmentcomplete_003:
          json['healtassessment_modalsuccess_txthealthassessmentcomplete_003']
                  as String? ??
              "",
      txtidontknow_001: json['txtidontknow_001'] as String? ?? "",
      txtsection: json['txtsection'] as String? ?? "",
      txtrange: json['txtrange'] as String? ?? "",
      txttimeforyourfirstspin_001:
          json['txttimeforyourfirstspin_001'] as String? ?? "",
      txtswiptetospin_002: json['txtswiptetospin_002'] as String? ?? "",
      tracking_screen_header_001:
          json['tracking_screen_header_001'] as String? ?? "",
      txtxcoinsmonth_001: json['txtxcoinsmonth_001'] as String? ?? "",
      txtcasback_001: json['txtcasback_001'] as String? ?? "",
      total_coins_earned_001: json['total_coins_earned_001'] as String? ?? "",
      txtapplewatch_001: json['txtapplewatch_001'] as String? ?? "",
      txt_reward_activation_time_001:
          json['txt_reward_activation_time_001'] as String? ?? "",
      txt_reward_hf_starts_soon_001:
          json['txt_reward_hf_starts_soon_001'] as String? ?? "",
      tc_how_it_works_one: json['tc_how_it_works_one'] as String? ?? "",
      tc_how_it_works_two: json['tc_how_it_works_two'] as String? ?? "",
      tc_how_it_works_three: json['tc_how_it_works_three'] as String? ?? "",
      tc_how_it_works_four: json['tc_how_it_works_four'] as String? ?? "",
      txtmentalwellbeingassessment_001:
          json['txtmentalwellbeingassessment_001'] as String? ?? "",
      txtlowrisk_001: json['txtlowrisk_001'] as String? ?? "",
      txtstayconnectedtofriendsandfamily_001:
          json['txtstayconnectedtofriendsandfamily_001'] as String? ?? "",
      mentalwellbeing_assessment_txtblowrisk_001:
          json['mentalwellbeing_assessment_txtblowrisk_001'] as String? ?? "",
      txtmoderaterisk_001: json['txtmoderaterisk_001'] as String? ?? "",
      txtgetsupportnow_001: json['txtgetsupportnow_001'] as String? ?? "",
      mentalwellbeing_assessment_txtbmoderaterisk_001:
          json['mentalwellbeing_assessment_txtbmoderaterisk_001'] as String? ??
              "",
      txthighrisk_001: json['txthighrisk_001'] as String? ?? "",
      txtconsultyourhealthcareprovider_001:
          json['txtconsultyourhealthcareprovider_001'] as String? ?? "",
      mentalwellbeing_assessment_txtbhighrisk_001:
          json['mentalwellbeing_assessment_txtbhighrisk_001'] as String? ?? "",
      mha_section_Iconkey_118: json['mha_section_Iconkey_118'] as String? ?? "",
      mha_section_Iconkey_119: json['mha_section_Iconkey_119'] as String? ?? "",
      mha_section_Iconkey_120: json['mha_section_Iconkey_120'] as String? ?? "",
      txtearnpoints_001: json['txtearnpoints_001'] as String? ?? "",
      txtfeedsheadergoals_001: json['txtfeedsheadergoals_001'] as String? ?? "",
      txtassessments_001: json['txtassessments_001'] as String? ?? "",
      txtprevention_001: json['txtprevention_001'] as String? ?? "",
      ctrl_txt_onlysendreport_001:
          json['ctrl_txt_onlysendreport_001'] as String? ?? "",
      ctrl_txt_haveyoubeenaskedtosend_001:
          json['ctrl_txt_haveyoubeenaskedtosend_001'] as String? ?? "",
      ctrl_txt_sendreport_001: json['ctrl_txt_sendreport_001'] as String? ?? "",
      ctrl_txt_sendatroubleshootingreport_001:
          json['ctrl_txt_sendatroubleshootingreport_001'] as String? ?? "",
      ctrl_txt_onlysendareport_001:
          json['ctrl_txt_onlysendareport_001'] as String? ?? "",
      txtareyousureyouwanttoclosethesurvey_001:
          json['txtareyousureyouwanttoclosethesurvey_001'] as String? ?? "",
      txt_closesurvey_001: json['txt_closesurvey_001'] as String? ?? "",
      ctrl_txtcancel_001: json['ctrl_txtcancel_001'] as String? ?? "",
      ctrl_txtyes_001: json['ctrl_txtyes_001'] as String? ?? "",
      ctrl_btncontinuetofeedback_001:
          json['ctrl_btncontinuetofeedback_001'] as String? ?? "",
      vhc_submission_screen_date_of_activity_date_format:
          json['vhc_submission_screen_date_of_activity_date_format']
                  as String? ??
              "",
      dt_format_date_short_month_full_year:
          json['dt_format_date_short_month_full_year'] as String? ?? "",
      txtbronze_001: json['txtbronze_001'] as String? ?? "",
      txtbronze_subtext_001: json['txtbronze_subtext_001'] as String? ?? "",
      txtsilver_subtext_001: json['txtsilver_subtext_001'] as String? ?? "",
      txtgold_subtext_001: json['txtgold_subtext_001'] as String? ?? "",
      txtplatinum_subtext_001: json['txtplatinum_subtext_001'] as String? ?? "",
      pag_history_header_date_format:
          json['pag_history_header_date_format'] as String? ?? "",
      pag_history_list_week_date_format:
          json['pag_history_list_week_date_format'] as String? ?? "",
      wlg_history_header_date_format:
          json['wlg_history_header_date_format'] as String? ?? "",
      wlg_history_list_week_date_format:
          json['wlg_history_list_week_date_format'] as String? ?? "",
      dt_format_short_month_full_year:
          json['dt_format_short_month_full_year'] as String? ?? "",
      dt_format_date_short_month:
          json['dt_format_date_short_month'] as String? ?? "",
      how_to_earn_points_steps_number_format:
          json['how_to_earn_points_steps_number_format'] as String? ?? "",
      how_to_earn_points_points_number_format:
          json['how_to_earn_points_points_number_format'] as String? ?? "",
      num_format_non_decimal_number:
          json['num_format_non_decimal_number'] as String? ?? "",
      txtbronze_subtext_002: json['txtbronze_subtext_002'] as String? ?? "",
      txtsilver_subtext_002: json['txtsilver_subtext_002'] as String? ?? "",
      txtgold_subtext_002: json['txtgold_subtext_002'] as String? ?? "",
      txtplatinum_subtext_002: json['txtplatinum_subtext_002'] as String? ?? "",
      txtblue_subtext_001: json['txtblue_subtext_001'] as String? ?? "",
      txtmentalwellbeingasessmentcomplete_001:
          json['txtmentalwellbeingasessmentcomplete_001'] as String? ?? "",
      txtVHC_Submissions_Date_001:
          json['txtVHC_Submissions_Date_001'] as String? ?? "",
      txtVHC_Submissions_Date_002:
          json['txtVHC_Submissions_Date_002'] as String? ?? "",
      txtavailabledevices_001: json['txtavailabledevices_001'] as String? ?? "",
      txtonlyonedevicecashback_001:
          json['txtonlyonedevicecashback_001'] as String? ?? "",
      pending_calculation_001: json['pending_calculation_001'] as String? ?? "",
      txt_uptoperc_cashback_001:
          json['txt_uptoperc_cashback_001'] as String? ?? "",
      txt_ptstostatus_001: json['txt_ptstostatus_001'] as String? ?? "",
      txtdaterange_001: json['txtdaterange_001'] as String? ?? "",
      txthowtoearnsubtext_001: json['txthowtoearnsubtext_001'] as String? ?? "",
      txthowtoearnsubtext_004: json['txthowtoearnsubtext_004'] as String? ?? "",
      txthowtoearnsubtext_005: json['txthowtoearnsubtext_005'] as String? ?? "",
      txtcheckups_001: json['txtcheckups_001'] as String? ?? "",
      txt_pag_earn_maximum: json['txt_pag_earn_maximum'] as String? ?? "",
      txt_pag_no_maximum: json['txt_pag_no_maximum'] as String? ?? "",
      txt_hf_paused_001: json['txt_hf_paused_001'] as String? ?? "",
      txt_automatic_gym_note: json['txt_automatic_gym_note'] as String? ?? "",
      txt_reward_pendactivation_time_001:
          json['txt_reward_pendactivation_time_001'] as String? ?? "",
      txtrewardsmall_001: json['txtrewardsmall_001'] as String? ?? "",
      txt_hf_purchase_fresh_001:
          json['txt_hf_purchase_fresh_001'] as String? ?? "",
      ctrl_txt_pts_001: json['ctrl_txt_pts_001'] as String? ?? "",
      ctrl_txt_yourcurrentmembershipyear_001:
          json['ctrl_txt_yourcurrentmembershipyear_001'] as String? ?? "",
      ctrl_txt_totalpointsearned_001:
          json['ctrl_txt_totalpointsearned_001'] as String? ?? "",
      ctrl_txt_daysleft_001: json['ctrl_txt_daysleft_001'] as String? ?? "",
      ctrl_txt_statusrewards_001:
          json['ctrl_txt_statusrewards_001'] as String? ?? "",
      ctrl_txt_yourrewardlevel_001:
          json['ctrl_txt_yourrewardlevel_001'] as String? ?? "",
      ctrl_txt_discounton_001: json['ctrl_txt_discounton_001'] as String? ?? "",
      ctrl_txt_activitiestoearnpts_001:
          json['ctrl_txt_activitiestoearnpts_001'] as String? ?? "",
      ctrl_txt_keepearningpts_001:
          json['ctrl_txt_keepearningpts_001'] as String? ?? "",
      ctrl_txt_thehealthieryouare_001:
          json['ctrl_txt_thehealthieryouare_001'] as String? ?? "",
      ctrl_txt_relatedfaqs_001:
          json['ctrl_txt_relatedfaqs_001'] as String? ?? "",
      txtotheractivitiestext_001:
          json['txtotheractivitiestext_001'] as String? ?? "",
      pg_txt_title_001: json['pg_txt_title_001'] as String? ?? "",
      pg_calendar_day_002: json['pg_calendar_day_002'] as String? ?? "",
      pg_btnactivatereward_003:
          json['pg_btnactivatereward_003'] as String? ?? "",
      pg_txthowitworks_004: json['pg_txthowitworks_004'] as String? ?? "",
      pg_txtheaderactivatebenifit_005:
          json['pg_txtheaderactivatebenifit_005'] as String? ?? "",
      pg_txtactivatebenifitdesc_line1_005:
          json['pg_txtactivatebenifitdesc_line1_005'] as String? ?? "",
      pg_txtactivatebenifitdesc_line2_005:
          json['pg_txtactivatebenifitdesc_line2_005'] as String? ?? "",
      pg_txtheaderletexercise_006:
          json['pg_txtheaderletexercise_006'] as String? ?? "",
      pg_txtdescletexercise_006:
          json['pg_txtdescletexercise_006'] as String? ?? "",
      pg_txtheaderletexercise_007:
          json['pg_txtheaderletexercise_007'] as String? ?? "",
      pg_txtdescletexercise_line1_007:
          json['pg_txtdescletexercise_line1_007'] as String? ?? "",
      pg_txtdescletexercise_line2_007:
          json['pg_txtdescletexercise_line2_007'] as String? ?? "",
      pg_txtheaderpromocodes_008:
          json['pg_txtheaderpromocodes_008'] as String? ?? "",
      pg_txtdescpromocodes_line1_008:
          json['pg_txtdescpromocodes_line1_008'] as String? ?? "",
      pg_txtdescpromocodes_line2_008:
          json['pg_txtdescpromocodes_line2_008'] as String? ?? "",
      pg_txtdescpromocodes_line3_008:
          json['pg_txtdescpromocodes_line3_008'] as String? ?? "",
      pg_txtcardtitleweeklyphyactgoal_009:
          json['pg_txtcardtitleweeklyphyactgoal_009'] as String? ?? "",
      pg_txtcardtitleRelatedfaqs_010:
          json['pg_txtcardtitleRelatedfaqs_010'] as String? ?? "",
      pg_txt_notinstalldialog_hd_011:
          json['pg_txt_notinstalldialog_hd_011'] as String? ?? "",
      pg_txt_notinstalldialog_body_012:
          json['pg_txt_notinstalldialog_body_012'] as String? ?? "",
      pg_txtheaderactivatedgoal_013:
          json['pg_txtheaderactivatedgoal_013'] as String? ?? "",
      pg_txtsubtitleactivategoal:
          json['pg_txtsubtitleactivategoal'] as String? ?? "",
      pg_btngotit_014: json['pg_btngotit_014'] as String? ?? "",
      pg_txtyourfirstgoalstartsondate_016:
          json['pg_txtyourfirstgoalstartsondate_016'] as String? ?? "",
      pg_txtcardtitlearcsupitem_017:
          json['pg_txtcardtitlearcsupitem_017'] as String? ?? "",
      pg_txtarchivedgiftcards_018:
          json['pg_txtarchivedgiftcards_018'] as String? ?? "",
      pg_txtnogiftcardsarchived_019:
          json['pg_txtnogiftcardsarchived_019'] as String? ?? "",
      pg_txtnogiftcardsarchiveddesc_020:
          json['pg_txtnogiftcardsarchiveddesc_020'] as String? ?? "",
      pg_btnarchive_021: json['pg_btnarchive_021'] as String? ?? "",
      pg_txtitemswillshowhere_022:
          json['pg_txtitemswillshowhere_022'] as String? ?? "",
      pg_txtclaimsurpriseitem_023:
          json['pg_txtclaimsurpriseitem_023'] as String? ?? "",
      pg_txtrevealyourpromocode_024:
          json['pg_txtrevealyourpromocode_024'] as String? ?? "",
      pg_txtbrandName_025: json['pg_txtbrandName_025'] as String? ?? "",
      pg_btnrevealcode_026: json['pg_btnrevealcode_026'] as String? ?? "",
      pg_txtusethecodetoredeem_027:
          json['pg_txtusethecodetoredeem_027'] as String? ?? "",
      pg_txtdiscountcode_028: json['pg_txtdiscountcode_028'] as String? ?? "",
      pg_btncopy_029: json['pg_btncopy_029'] as String? ?? "",
      pg_btnvisitwebsite_030: json['pg_btnvisitwebsite_030'] as String? ?? "",
      pg_btnarchivegiftcard_031:
          json['pg_btnarchivegiftcard_031'] as String? ?? "",
      pg_txtarchivedgiftcards_032:
          json['pg_txtarchivedgiftcards_032'] as String? ?? "",
      pg_txtaechivesurpriseitemtitle_033:
          json['pg_txtaechivesurpriseitemtitle_033'] as String? ?? "",
      pg_txtexpiesInDays_034: json['pg_txtexpiesInDays_034'] as String? ?? "",
      pg_txtexpies_035: json['pg_txtexpies_035'] as String? ?? "",
      pg_txtheadererror_036: json['pg_txtheadererror_036'] as String? ?? "",
      pg_txterrordescription_037:
          json['pg_txterrordescription_037'] as String? ?? "",
      pg_txtredeemcodehelp_038:
          json['pg_txtredeemcodehelp_038'] as String? ?? "",
      pg_txtanynotefromsupplier_039:
          json['pg_txtanynotefromsupplier_039'] as String? ?? "",
      pg_txtheadersurprise_040:
          json['pg_txtheadersurprise_040'] as String? ?? "",
      pg_txtsubheadersurprise_041:
          json['pg_txtsubheadersurprise_041'] as String? ?? "",
      pg_btninstall_042: json['pg_btninstall_042'] as String? ?? "",
      pg_btncancel_043: json['pg_btncancel_043'] as String? ?? "",
      vj_pageHeaderH2_v1: json['vj_pageHeaderH2_v1'] as String? ?? "",
      vj_pointsTracker_v1: json['vj_pointsTracker_v1'] as String? ?? "",
      vj_singleContentLine_v1: json['vj_singleContentLine_v1'] as String? ?? "",
      vj_ctrl_btnselectgoal_001:
          json['vj_ctrl_btnselectgoal_001'] as String? ?? "",
      vj_txthowtocomplete_001: json['vj_txthowtocomplete_001'] as String? ?? "",
      vj_txttakeatwominutemovementbreakeverywakinghour_001:
          json['vj_txttakeatwominutemovementbreakeverywakinghour_001']
                  as String? ??
              "",
      vj_txttakeatwominutemovementbreakeverywakinghour_002:
          json['vj_txttakeatwominutemovementbreakeverywakinghour_002']
                  as String? ??
              "",
      vj_sectionHeaderH5_v1: json['vj_sectionHeaderH5_v1'] as String? ?? "",
      vj_paragraphText_v1: json['vj_paragraphText_v1'] as String? ?? "",
      vj_txtcheckintoyourgoalthesameday_001:
          json['vj_txtcheckintoyourgoalthesameday_001'] as String? ?? "",
      vj_txtcheckintoyourgoalthesameday_002:
          json['vj_txtcheckintoyourgoalthesameday_002'] as String? ?? "",
      vj_txtcheckintoyourgoalthesameday_003:
          json['vj_txtcheckintoyourgoalthesameday_003'] as String? ?? "",
      vj_txtcheckintoyourgoalthesameday_004:
          json['vj_txtcheckintoyourgoalthesameday_004'] as String? ?? "",
      vj_ctrl_txtappsanddevices_001:
          json['vj_ctrl_txtappsanddevices_001'] as String? ?? "",
      vj_ctrl_txtaboutphysicalactivitygoal_001:
          json['vj_ctrl_txtaboutphysicalactivitygoal_001'] as String? ?? "",
      vj_txtsetstridelength_001:
          json['vj_txtsetstridelength_001'] as String? ?? "",
      vj_txtsubmitresultsandproofexplanationcopy_001:
          json['vj_txtsubmitresultsandproofexplanationcopy_001'] as String? ??
              "",
      vj_txtpointsmaytakeupto24hourstoreflect_001_1:
          json['vj_txtpointsmaytakeupto24hourstoreflect_001_1'] as String? ??
              "",
      vj_txtpointsmaytakeupto24hourstoreflect_001_2:
          json['vj_txtpointsmaytakeupto24hourstoreflect_001_2'] as String? ??
              "",
      vj_txtpointsmaytakeupto24hourstoreflect_001_3:
          json['vj_txtpointsmaytakeupto24hourstoreflect_001_3'] as String? ??
              "",
      vj_label_: json['vj_label_'] as String? ?? "",
      vj_ctrl_btnnext_001: json['vj_ctrl_btnnext_001'] as String? ?? "",
      vj_txtdisconnected_001: json['vj_txtdisconnected_001'] as String? ?? "",
      vj_supportText_: json['vj_supportText_'] as String? ?? "",
      vj_txtgetreadytostartmoving_001:
          json['vj_txtgetreadytostartmoving_001'] as String? ?? "",
      vj_txtyourfirststartgoal_001:
          json['vj_txtyourfirststartgoal_001'] as String? ?? "",
      vj_ctrl_btngotit_001: json['vj_ctrl_btngotit_001'] as String? ?? "",
      vj_txtautotracked_001: json['vj_txtautotracked_001'] as String? ?? "",
      vj_comboHd1: json['vj_comboHd1'] as String? ?? "",
      vj_txtpointsreflectnote_001:
          json['vj_txtpointsreflectnote_001'] as String? ?? "",
      vj_comboHd2: json['vj_comboHd2'] as String? ?? "",
      vj_comboHd3: json['vj_comboHd3'] as String? ?? "",
      vj_txt_001: json['vj_txt_001'] as String? ?? "",
      vj_txtaccountdetails_001:
          json['vj_txtaccountdetails_001'] as String? ?? "",
      vj_ctrl_txthowitworks_001:
          json['vj_ctrl_txthowitworks_001'] as String? ?? "",
      vj_txtbodyhowtocomplete_001:
          json['vj_txtbodyhowtocomplete_001'] as String? ?? "",
      vj_sectionHd1: json['vj_sectionHd1'] as String? ?? "",
      vj_sectionHd2: json['vj_sectionHd2'] as String? ?? "",
      vj_txt_002: json['vj_txt_002'] as String? ?? "",
      vj_txtmilestoneachieved_001:
          json['vj_txtmilestoneachieved_001'] as String? ?? "",
      vj_txtmilestoneachieved_002:
          json['vj_txtmilestoneachieved_002'] as String? ?? "",
      vj_success: json['vj_success'] as String? ?? "",
      vj_success_sub_text: json['vj_success_sub_text'] as String? ?? "",
      vj_ctrl_btn_001: json['vj_ctrl_btn_001'] as String? ?? "",
      vj_physicalactivity_modalactivated_txtactivatedgoal_002:
          json['vj_physicalactivity_modalactivated_txtactivatedgoal_002']
                  as String? ??
              "",
      vj_physicalactivity_modalactivated_txtyourfirststartgoal_002:
          json['vj_physicalactivity_modalactivated_txtyourfirststartgoal_002']
                  as String? ??
              "",
      vj_ctrl_btngotit_002: json['vj_ctrl_btngotit_002'] as String? ?? "",
      vj_ctrl_btn_002: json['vj_ctrl_btn_002'] as String? ?? "",
      vj_txtwhatsonthewheel_001:
          json['vj_txtwhatsonthewheel_001'] as String? ?? "",
      vj_dialog_body_: json['vj_dialog_body_'] as String? ?? "",
      vj_label_text: json['vj_label_text'] as String? ?? "",
      vj_dialog_btn1_: json['vj_dialog_btn1_'] as String? ?? "",
      vj_dialog_btn1_1: json['vj_dialog_btn1_1'] as String? ?? "",
      ctrl_txt_youarestatus_001:
          json['ctrl_txt_youarestatus_001'] as String? ?? "",
      ctrl_txt_yourmembershipyearreset_001:
          json['ctrl_txt_yourmembershipyearreset_001'] as String? ?? "",
      ctrl_txt_youhavedaysleft_001:
          json['ctrl_txt_youhavedaysleft_001'] as String? ?? "",
      ctrl_txt_moreonvitalitystatus_001:
          json['ctrl_txt_moreonvitalitystatus_001'] as String? ?? "",
      ctrl_txt_atthestartofeverymonth_001:
          json['ctrl_txt_atthestartofeverymonth_001'] as String? ?? "",
      ctrl_txt_achievethehighestvitalitystatus_001:
          json['ctrl_txt_achievethehighestvitalitystatus_001'] as String? ?? "",
      ctrl_txt_whyitsimportant_001:
          json['ctrl_txt_whyitsimportant_001'] as String? ?? "",
      ctrl_txt_yourvitalitystatusisameasure_001:
          json['ctrl_txt_yourvitalitystatusisameasure_001'] as String? ?? "",
      ctrl_txt_howstatusworks_001:
          json['ctrl_txt_howstatusworks_001'] as String? ?? "",
      ctrl_txt_howvitalitystatusworks_001:
          json['ctrl_txt_howvitalitystatusworks_001'] as String? ?? "",
      ctrl_txt_duringthistime_001:
          json['ctrl_txt_duringthistime_001'] as String? ?? "",
      ctrl_txt_atthestartofeachmembershipyear_001:
          json['ctrl_txt_atthestartofeachmembershipyear_001'] as String? ?? "",
      ctrl_txt_maintainorearnhigherrewards_001:
          json['ctrl_txt_maintainorearnhigherrewards_001'] as String? ?? "",
      ctrl_txt_yourvitalitystatusmeasure_001:
          json['ctrl_txt_yourvitalitystatusmeasure_001'] as String? ?? "",
      txtrewards2047: json['txtrewards2047'] as String? ?? "",
      txtrewards2048: json['txtrewards2048'] as String? ?? "",
      txtrewards2049: json['txtrewards2049'] as String? ?? "",
      txtrewards2050: json['txtrewards2050'] as String? ?? "",
      txtrewards2051: json['txtrewards2051'] as String? ?? "",
      iconrewards2047: json['iconrewards2047'] as String? ?? "",
      iconrewards2048: json['iconrewards2048'] as String? ?? "",
      iconrewards2049: json['iconrewards2049'] as String? ?? "",
      iconrewards2050: json['iconrewards2050'] as String? ?? "",
      iconrewards2051: json['iconrewards2051'] as String? ?? "",
      txt_no_history_001: json['txt_no_history_001'] as String? ?? "",
      txt_hf_cashback_001: json['txt_hf_cashback_001'] as String? ?? "",
      txt_hf_history_avail_001:
          json['txt_hf_history_avail_001'] as String? ?? "",
      txt_no_cashback_earned_001:
          json['txt_no_cashback_earned_001'] as String? ?? "",
      txt_perc_cashback_spend_001:
          json['txt_perc_cashback_spend_001'] as String? ?? "",
      txt_earned_cashback_001: json['txt_earned_cashback_001'] as String? ?? "",
      tc_no_history_available: json['tc_no_history_available'] as String? ?? "",
      tc_last_week_empty_list_msg:
          json['tc_last_week_empty_list_msg'] as String? ?? "",
      tc_active_challenge_starts_next_monday:
          json['tc_active_challenge_starts_next_monday'] as String? ?? "",
      tc_goals_met: json['tc_goals_met'] as String? ?? "",
      tc_points_earned: json['tc_points_earned'] as String? ?? "",
      tc_unknown_error: json['tc_unknown_error'] as String? ?? "",
      tc_error_occurred_try_again:
          json['tc_error_occurred_try_again'] as String? ?? "",
      tc_Check_out_this_link: json['tc_Check_out_this_link'] as String? ?? "",
      tc_you: json['tc_you'] as String? ?? "",
      dt_format_day_date_short_month:
          json['dt_format_day_date_short_month'] as String? ?? "",
      dt_format_day_date_short_month_full_year:
          json['dt_format_day_date_short_month_full_year'] as String? ?? "",
      txt_maintainpoinstatus_001:
          json['txt_maintainpoinstatus_001'] as String? ?? "",
      question_input_hint_102453:
          json['question_input_hint_102453'] as String? ?? "",
      txt_systolic: json['txt_systolic'] as String? ?? "",
      txt_dialostolic: json['txt_dialostolic'] as String? ?? "",
      txt_feet: json['txt_feet'] as String? ?? "",
      txt_inch: json['txt_inch'] as String? ?? "",
      txt_range_001: json['txt_range_001'] as String? ?? "",
      apps_devices_sync_date_format:
          json['apps_devices_sync_date_format'] as String? ?? "",
      dt_format_date_short_month_full_year_twenty_four_hours_mins:
          json['dt_format_date_short_month_full_year_twenty_four_hours_mins']
                  as String? ??
              "",
      dt_format_date_short_month_full_year_twelve_hours_mins:
          json['dt_format_date_short_month_full_year_twelve_hours_mins']
                  as String? ??
              "",
      txtbody_maxfile_006: json['txtbody_maxfile_006'] as String? ?? "",
      txt_points_limit: json['txt_points_limit'] as String? ?? "",
      txt_points_limit_description_001:
          json['txt_points_limit_description_001'] as String? ?? "",
      dialog_body_changingpassword_001:
          json['dialog_body_changingpassword_001'] as String? ?? "",
      dialog_hd_changepassword_001:
          json['dialog_hd_changepassword_001'] as String? ?? "",
      dialog_btn2_reenterpassword_001:
          json['dialog_btn2_reenterpassword_001'] as String? ?? "",
      dialog_btn1_changepassword_001:
          json['dialog_btn1_changepassword_001'] as String? ?? "",
      txtblue_subtext_001_individual:
          json['txtblue_subtext_001_individual'] as String? ?? "",
      txtblue_subtext_001_sme: json['txtblue_subtext_001_sme'] as String? ?? "",
      txtbronze_subtext_001_individual:
          json['txtbronze_subtext_001_individual'] as String? ?? "",
      txtbronze_subtext_002_individual:
          json['txtbronze_subtext_002_individual'] as String? ?? "",
      txtbronze_subtext_001_sme:
          json['txtbronze_subtext_001_sme'] as String? ?? "",
      txtbronze_subtext_002_sme:
          json['txtbronze_subtext_002_sme'] as String? ?? "",
      txtbronze_subtext_001_ind_sme:
          json['txtbronze_subtext_001_ind_sme'] as String? ?? "",
      txtbronze_subtext_002_ind_sme:
          json['txtbronze_subtext_002_ind_sme'] as String? ?? "",
      txtsilver_subtext_001_individual:
          json['txtsilver_subtext_001_individual'] as String? ?? "",
      txtsilver_subtext_002_individual:
          json['txtsilver_subtext_002_individual'] as String? ?? "",
      txtsilver_subtext_001_sme:
          json['txtsilver_subtext_001_sme'] as String? ?? "",
      txtsilver_subtext_002_sme:
          json['txtsilver_subtext_002_sme'] as String? ?? "",
      txtsilver_subtext_001_ind_sme:
          json['txtsilver_subtext_001_ind_sme'] as String? ?? "",
      txtsilver_subtext_002_ind_sme:
          json['txtsilver_subtext_002_ind_sme'] as String? ?? "",
      txtgold_subtext_001_individual:
          json['txtgold_subtext_001_individual'] as String? ?? "",
      txtgold_subtext_002_individual:
          json['txtgold_subtext_002_individual'] as String? ?? "",
      txtgold_subtext_001_sme: json['txtgold_subtext_001_sme'] as String? ?? "",
      txtgold_subtext_002_sme: json['txtgold_subtext_002_sme'] as String? ?? "",
      txtgold_subtext_001_ind_sme:
          json['txtgold_subtext_001_ind_sme'] as String? ?? "",
      txtgold_subtext_002_ind_sme:
          json['txtgold_subtext_002_ind_sme'] as String? ?? "",
      txtplatinum_subtext_001_individual:
          json['txtplatinum_subtext_001_individual'] as String? ?? "",
      txtplatinum_subtext_002_individual:
          json['txtplatinum_subtext_002_individual'] as String? ?? "",
      txtplatinum_subtext_001_sme:
          json['txtplatinum_subtext_001_sme'] as String? ?? "",
      txtplatinum_subtext_002_sme:
          json['txtplatinum_subtext_002_sme'] as String? ?? "",
      txtplatinum_subtext_001_ind_sme:
          json['txtplatinum_subtext_001_ind_sme'] as String? ?? "",
      txtplatinum_subtext_002_ind_sme:
          json['txtplatinum_subtext_002_ind_sme'] as String? ?? "",
      link_hf_supermaxi_001: json['link_hf_supermaxi_001'] as String? ?? "",
      txt_waiting_001: json['txt_waiting_001'] as String? ?? "",
      txt_try_again_001: json['txt_try_again_001'] as String? ?? "",
      tc_Sticker_history: json['tc_Sticker_history'] as String? ?? "",
      tc__only_the_10_latest_stickers_will_be_displayed:
          json['tc__only_the_10_latest_stickers_will_be_displayed']
                  as String? ??
              "",
      tc_sent_by: json['tc_sent_by'] as String? ?? "",
      tc_send_sticker: json['tc_send_sticker'] as String? ?? "",
      tc_sticker_sent: json['tc_sticker_sent'] as String? ?? "",
      two_factor_txtinvalidnumber_001:
          json['two_factor_txtinvalidnumber_001'] as String? ?? "",
      two_factor_txtmethodemail:
          json['two_factor_txtmethodemail'] as String? ?? "",
      two_factor_txtmethodtext:
          json['two_factor_txtmethodtext'] as String? ?? "",
      txtgooglefitunabletoconnect_001:
          json['txtgooglefitunabletoconnect_001'] as String? ?? "",
      txtgooglefitunabletoconnectcopy_001:
          json['txtgooglefitunabletoconnectcopy_001'] as String? ?? "",
      ctrl_btncancelconnection_001:
          json['ctrl_btncancelconnection_001'] as String? ?? "",
      ctrl_btnopensettings_001:
          json['ctrl_btnopensettings_001'] as String? ?? "",
      txtpermissionrequired_001:
          json['txtpermissionrequired_001'] as String? ?? "",
      txtpermissionrequiredcopy_006:
          json['txtpermissionrequiredcopy_006'] as String? ?? "",
      ctrl_btnskipfornow_001: json['ctrl_btnskipfornow_001'] as String? ?? "",
      txtpermissionrequiredcopy_005:
          json['txtpermissionrequiredcopy_005'] as String? ?? "",
      txtgooglefitwillbedisconnected_001:
          json['txtgooglefitwillbedisconnected_001'] as String? ?? "",
      txtgooglefitwillbedisconnectedcopy_001:
          json['txtgooglefitwillbedisconnectedcopy_001'] as String? ?? "",
      ctrl_btnback_001: json['ctrl_btnback_001'] as String? ?? "",
      txtpermissionrequiredcopy_001:
          json['txtpermissionrequiredcopy_001'] as String? ?? "",
      txtpermissionrequiredcopy_002:
          json['txtpermissionrequiredcopy_002'] as String? ?? "",
      btn_viewreawards_001: json['btn_viewreawards_001'] as String? ?? "",
      txtwelcometoanewmembershipyear_001:
          json['txtwelcometoanewmembershipyear_001'] as String? ?? "",
      txtyourvitalitypointshavebeenreset_001:
          json['txtyourvitalitypointshavebeenreset_001'] as String? ?? "",
      txtstatus_silver: json['txtstatus_silver'] as String? ?? "",
      txtstatus_gold: json['txtstatus_gold'] as String? ?? "",
      txtstatus_platinum: json['txtstatus_platinum'] as String? ?? "",
      txtstatus_bronze: json['txtstatus_bronze'] as String? ?? "",
      txtstatus_new_status: json['txtstatus_new_status'] as String? ?? "",
      ctrl_btnviewrewards_001: json['ctrl_btnviewrewards_001'] as String? ?? "",
      question_text_box_hint_10034:
          json['question_text_box_hint_10034'] as String? ?? "",
      question_text_box_hint_10033:
          json['question_text_box_hint_10033'] as String? ?? "",
      question_text_box_hint_10040:
          json['question_text_box_hint_10040'] as String? ?? "",
      question_text_box_hint_10042:
          json['question_text_box_hint_10042'] as String? ?? "",
      question_text_box_hint_10045:
          json['question_text_box_hint_10045'] as String? ?? "",
      question_text_box_hint_10013:
          json['question_text_box_hint_10013'] as String? ?? "",
      question_text_box_hint_10046:
          json['question_text_box_hint_10046'] as String? ?? "",
      question_text_box_hint_10059:
          json['question_text_box_hint_10059'] as String? ?? "",
      question_text_box_hint_10211:
          json['question_text_box_hint_10211'] as String? ?? "",
      question_text_box_hint_10063:
          json['question_text_box_hint_10063'] as String? ?? "",
      question_text_box_hint_10062:
          json['question_text_box_hint_10062'] as String? ?? "",
      question_input_hint_100103:
          json['question_input_hint_100103'] as String? ?? "",
      question_input_hint_100117:
          json['question_input_hint_100117'] as String? ?? "",
      question_input_hint_100122:
          json['question_input_hint_100122'] as String? ?? "",
      question_input_hint_100125:
          json['question_input_hint_100125'] as String? ?? "",
      question_input_hint_100129:
          json['question_input_hint_100129'] as String? ?? "",
      question_text_box_hint_10032:
          json['question_text_box_hint_10032'] as String? ?? "",
      txtwhatsappchat_001: json['txtwhatsappchat_001'] as String? ?? "",
      txtchatdialogsubtext_002:
          json['txtchatdialogsubtext_002'] as String? ?? "",
      txtcontactnumber_001: json['txtcontactnumber_001'] as String? ?? "",
      ctrl_btnemail_001: json['ctrl_btnemail_001'] as String? ?? "",
      ctrl_btnsendfeedback_001:
          json['ctrl_btnsendfeedback_001'] as String? ?? "",
      ctrl_btnstartwhatsappchat_001:
          json['ctrl_btnstartwhatsappchat_001'] as String? ?? "",
      txt_foot_note: json['txt_foot_note'] as String? ?? "",
      ctrl_txt_onlysendareport_ios_001:
          json['ctrl_txt_onlysendareport_ios_001'] as String? ?? "",
      vj_cm: json['vj_cm'] as String? ?? "",
      txt_profile_header_points_001:
          json['txt_profile_header_points_001'] as String? ?? "",
      ctrl_btncontactnumber_001:
          json['ctrl_btncontactnumber_001'] as String? ?? "",
      earn_max_coins_001: json['earn_max_coins_001'] as String? ?? "",
      earn_max_coins_term_001: json['earn_max_coins_term_001'] as String? ?? "",
      txtpointstowardsgoal_001:
          json['txtpointstowardsgoal_001'] as String? ?? "",
      txtgoal_achieved_001: json['txtgoal_achieved_001'] as String? ?? "",
      txtpointstowardsgoal_002:
          json['txtpointstowardsgoal_002'] as String? ?? "",
      txttowardsgoalbreakdown_001:
          json['txttowardsgoalbreakdown_001'] as String? ?? "",
      txt_membership_name_vitalityactivefull:
          json['txt_membership_name_vitalityactivefull'] as String? ?? "",
      txt_membership_name_vitalityactivelite:
          json['txt_membership_name_vitalityactivelite'] as String? ?? "",
      txt_membership_name_individual:
          json['txt_membership_name_individual'] as String? ?? "",
      txt_membership_name_sme: json['txt_membership_name_sme'] as String? ?? "",
      txt_membership_name_ind_sme:
          json['txt_membership_name_ind_sme'] as String? ?? "",
      txtpointstowardsstatus_001:
          json['txtpointstowardsstatus_001'] as String? ?? "",
      hc_askchatbot: json['hc_askchatbot'] as String? ?? "",
      hc_faqs: json['hc_faqs'] as String? ?? "",
      hc_new_bulletin_board: json['hc_new_bulletin_board'] as String? ?? "",
      hc_video_tutorials_on_app_operation:
          json['hc_video_tutorials_on_app_operation'] as String? ?? "",
      hc_Vitality_special_site:
          json['hc_Vitality_special_site'] as String? ?? "",
      hc_users_guide_to_benefits:
          json['hc_users_guide_to_benefits'] as String? ?? "",
      hc_terms_and_conditions: json['hc_terms_and_conditions'] as String? ?? "",
      hc_apply_for_steps_points:
          json['hc_apply_for_steps_points'] as String? ?? "",
      hc_send_inquiry_form: json['hc_send_inquiry_form'] as String? ?? "",
      txtwerehavingtroubleconnectingyou_001:
          json['txtwerehavingtroubleconnectingyou_001'] as String? ?? "",
      txtunabletoconnecttopartnerapp_001:
          json['txtunabletoconnecttopartnerapp_001'] as String? ?? "",
      txtissueconnectingtopartnerapp_001:
          json['txtissueconnectingtopartnerapp_001'] as String? ?? "",
      txtwhatsapp: json['txtwhatsapp'] as String? ?? "",
      oldwheel_txt15creditshavebeendepositedinyourcygmall_001:
          json['oldwheel_txt15creditshavebeendepositedinyourcygmall_001']
                  as String? ??
              "",
      txttime005: json['txttime005'] as String? ?? "",
      book_now: json['book_now'] as String? ?? "",
      watch_videos: json['watch_videos'] as String? ?? "",
      txtmailapplication: json['txtmailapplication'] as String? ?? "",
      txtalertcouldntprocessyourgiftcardselection_001:
          json['txtalertcouldntprocessyourgiftcardselection_001'] as String? ??
              "",
      txt_invalidcodeerror_001:
          json['txt_invalidcodeerror_001'] as String? ?? "",
      vj_update_stride_length: json['vj_update_stride_length'] as String? ?? "",
      vj_you_done_it: json['vj_you_done_it'] as String? ?? "",
      vj_well_done: json['vj_well_done'] as String? ?? "",
      vj_just_collected: json['vj_just_collected'] as String? ?? "",
      vj_next_destination: json['vj_next_destination'] as String? ?? "",
      vj_km: json['vj_km'] as String? ?? "",
      txt_nopointsearningactivity_002:
          json['txt_nopointsearningactivity_002'] as String? ?? "",
      txt_points_002: json['txt_points_002'] as String? ?? "",
      txt_day_001: json['txt_day_001'] as String? ?? "",
      txt_days_001: json['txt_days_001'] as String? ?? "",
      txt_uploaderror_001: json['txt_uploaderror_001'] as String? ?? "",
      txt_mobilenumber_001: json['txt_mobilenumber_001'] as String? ?? "",
      txt_message_001: json['txt_message_001'] as String? ?? "",
      txt_typehere_001: json['txt_typehere_001'] as String? ?? "",
      txt_cannottbeempty_001: json['txt_cannottbeempty_001'] as String? ?? "",
      txt_feedback_submission_failure_001:
          json['txt_feedback_submission_failure_001'] as String? ?? "",
      txt_feedback_001: json['txt_feedback_001'] as String? ?? "",
      txtpoints_009: json['txtpoints_009'] as String? ?? "",
      txt_monthly_hf_cashback_001:
          json['txt_monthly_hf_cashback_001'] as String? ?? "",
      txt_progress_001: json['txt_progress_001'] as String? ?? "",
      txt_earned_sofar_001: json['txt_earned_sofar_001'] as String? ?? "",
      txt_no_cashback_earned_002:
          json['txt_no_cashback_earned_002'] as String? ?? "",
      txt_your_insurance_reward_001:
          json['txt_your_insurance_reward_001'] as String? ?? "",
      txt_unlock_cashback_001: json['txt_unlock_cashback_001'] as String? ?? "",
      txtinvalidquickguidedata:
          json['txtinvalidquickguidedata'] as String? ?? "",
      txthealthpartners: json['txthealthpartners'] as String? ?? "",
      txttroubleshootingreportsent_001:
          json['txttroubleshootingreportsent_001'] as String? ?? "",
      txttroubleshootingreportsent_002:
          json['txttroubleshootingreportsent_002'] as String? ?? "",
      txt_label_off: json['txt_label_off'] as String? ?? "",
      txt_label_on: json['txt_label_on'] as String? ?? "",
      ha_tracking_date_format: json['ha_tracking_date_format'] as String? ?? "",
      pag_history_list_day_date_format:
          json['pag_history_list_day_date_format'] as String? ?? "",
      pag_tracking_header_date_format:
          json['pag_tracking_header_date_format'] as String? ?? "",
      pag_activation_modal_started_date_format:
          json['pag_activation_modal_started_date_format'] as String? ?? "",
      txt_biometric_note_001: json['txt_biometric_note_001'] as String? ?? "",
      mha_tracking_next_available_date:
          json['mha_tracking_next_available_date'] as String? ?? "",
      appdevices_strava_subtitle:
          json['appdevices_strava_subtitle'] as String? ?? "",
      appdevices_withings_subtitle:
          json['appdevices_withings_subtitle'] as String? ?? "",
      appdevices_fitbit_subtitle:
          json['appdevices_fitbit_subtitle'] as String? ?? "",
      appdevices_garmin_subtitle:
          json['appdevices_garmin_subtitle'] as String? ?? "",
      appdevices_softbank_subtitle:
          json['appdevices_softbank_subtitle'] as String? ?? "",
      appdevices_polar_subtitle:
          json['appdevices_polar_subtitle'] as String? ?? "",
      appdevices_suunto_subtitle:
          json['appdevices_suunto_subtitle'] as String? ?? "",
      appdevices_omron_subtitle:
          json['appdevices_omron_subtitle'] as String? ?? "",
      appdevices_19_subtitle: json['appdevices_19_subtitle'] as String? ?? "",
      txtupdateyourapp_001: json['txtupdateyourapp_001'] as String? ?? "",
      txtsoftupdate_001: json['txtsoftupdate_001'] as String? ?? "",
      txtforcedupdate_001: json['txtforcedupdate_001'] as String? ?? "",
      txtblacklisting_001: json['txtblacklisting_001'] as String? ?? "",
      ctrl_btnlater_001: json['ctrl_btnlater_001'] as String? ?? "",
      ctrl_btnupdatenow_001: json['ctrl_btnupdatenow_001'] as String? ?? "",
      ctrl_btncloseapp_001: json['ctrl_btncloseapp_001'] as String? ?? "",
      vhc_date_format: json['vhc_date_format'] as String? ?? "",
      tc_error_email_max_length:
          json['tc_error_email_max_length'] as String? ?? "",
      txt_we_updated_your_biometric:
          json['txt_we_updated_your_biometric'] as String? ?? "",
      chip_cashbacklite_fd_001:
          json['chip_cashbacklite_fd_001'] as String? ?? "",
      levels_cashbackptslite_range:
          json['levels_cashbackptslite_range'] as String? ?? "",
      levels_cashbackptslite_range_last:
          json['levels_cashbackptslite_range_last'] as String? ?? "",
      genericfd_cashbacklite_1:
          json['genericfd_cashbacklite_1'] as String? ?? "",
      genericfd_cashbacklite_2:
          json['genericfd_cashbacklite_2'] as String? ?? "",
      genericfd_cashbacklite_3:
          json['genericfd_cashbacklite_3'] as String? ?? "",
      genericfd_cashbacklite_4:
          json['genericfd_cashbacklite_4'] as String? ?? "",
      genericfd_circlecontentlite_1:
          json['genericfd_circlecontentlite_1'] as String? ?? "",
      genericfd_circlecontentlite_2:
          json['genericfd_circlecontentlite_2'] as String? ?? "",
      genericfd_circlecontentlite_3:
          json['genericfd_circlecontentlite_3'] as String? ?? "",
      genericfd_circlecontentlite_4:
          json['genericfd_circlecontentlite_4'] as String? ?? "",
      txtuptoXcashbacklite_001:
          json['txtuptoXcashbacklite_001'] as String? ?? "",
      txtuptoXcashback_genp_001:
          json['txtuptoXcashback_genp_001'] as String? ?? "",
      txt_generic_prod_001: json['txt_generic_prod_001'] as String? ?? "",
      tfa_dialog_btn2_leave: json['tfa_dialog_btn2_leave'] as String? ?? "",
      tfa_dialog_btn2_retry: json['tfa_dialog_btn2_retry'] as String? ?? "",
      tfa_dialog_code_attempts_content:
          json['tfa_dialog_code_attempts_content'] as String? ?? "",
      tfa_dialog_code_attempts_title:
          json['tfa_dialog_code_attempts_title'] as String? ?? "",
      tfa_dialog_disable_content:
          json['tfa_dialog_disable_content'] as String? ?? "",
      tfa_dialog_disable_title:
          json['tfa_dialog_disable_title'] as String? ?? "",
      tfa_dialog_incorrect_password_content:
          json['tfa_dialog_incorrect_password_content'] as String? ?? "",
      tfa_dialog_incorrect_password_title:
          json['tfa_dialog_incorrect_password_title'] as String? ?? "",
      tfa_dialog_unsaved_changes_content:
          json['tfa_dialog_unsaved_changes_content'] as String? ?? "",
      tfa_dialog_unsaved_changes_title:
          json['tfa_dialog_unsaved_changes_title'] as String? ?? "",
      txtsummary: json['txtsummary'] as String? ?? "",
      txt_calculating: json['txt_calculating'] as String? ?? "",
      txtquestioncount_001: json['txtquestioncount_001'] as String? ?? "",
      txtpacemanagementheader_myhealth_001:
          json['txtpacemanagementheader_myhealth_001'] as String? ?? "",
      txtpacemanagementsubtext_myhealth_001:
          json['txtpacemanagementsubtext_myhealth_001'] as String? ?? "",
      txtpacemanagementheader_activityes_002:
          json['txtpacemanagementheader_activityes_002'] as String? ?? "",
      txtpacemanagementsubtext_activityes_002:
          json['txtpacemanagementsubtext_activityes_002'] as String? ?? "",
      chip_cashbacklite_amount_001:
          json['chip_cashbacklite_amount_001'] as String? ?? "",
      gab_btncollapsebarcode_001:
          json['gab_btncollapsebarcode_001'] as String? ?? "",
      gab_btnexpandbarcode_001:
          json['gab_btnexpandbarcode_001'] as String? ?? "",
      gab_txtfullname_001: json['gab_txtfullname_001'] as String? ?? "",
      gab_txtgymactivationbarcode_001:
          json['gab_txtgymactivationbarcode_001'] as String? ?? "",
      gab_txtpartyid_001: json['gab_txtpartyid_001'] as String? ?? "",
      txtuptoxperc_cashbacklite_001:
          json['txtuptoxperc_cashbacklite_001'] as String? ?? "",
      Total_cashbacklite_earned_001:
          json['Total_cashbacklite_earned_001'] as String? ?? "",
      total_coinslite_earned_001:
          json['total_coinslite_earned_001'] as String? ?? "",
      gds_txt_stamping_title_001:
          json['gds_txt_stamping_title_001'] as String? ?? "",
      gds_txt_stamping_desc_001:
          json['gds_txt_stamping_desc_001'] as String? ?? "",
      gds_txt_stamping_desc_002:
          json['gds_txt_stamping_desc_002'] as String? ?? "",
      gds_txt_register_title_001:
          json['gds_txt_register_title_001'] as String? ?? "",
      gds_txt_register_title_002:
          json['gds_txt_register_title_002'] as String? ?? "",
      gds_txt_register_desc_001:
          json['gds_txt_register_desc_001'] as String? ?? "",
      gds_txt_register_desc_002:
          json['gds_txt_register_desc_002'] as String? ?? "",
      gds_btn_register_001: json['gds_btn_register_001'] as String? ?? "",
      gds_btn_register_002: json['gds_btn_register_002'] as String? ?? "",
      gds_txt_error_title_001: json['gds_txt_error_title_001'] as String? ?? "",
      gds_txt_error_desc_001: json['gds_txt_error_desc_001'] as String? ?? "",
      appdevices_18_title: json['appdevices_18_title'] as String? ?? "",
      appdevices_19_title: json['appdevices_19_title'] as String? ?? "",
      appdevices_39_title: json['appdevices_39_title'] as String? ?? "",
      appdevices_98_title: json['appdevices_98_title'] as String? ?? "",
      appdevices_98_subtitle: json['appdevices_98_subtitle'] as String? ?? "",
      appdevices_18_subtitle: json['appdevices_18_subtitle'] as String? ?? "",
      appdevices_39_subtitle: json['appdevices_39_subtitle'] as String? ?? "",
      ExternalLinkReference: json['ExternalLinkReference'] as String? ?? "",
      txt_feeds_assessments_key_001:
          json['txt_feeds_assessments_key_001'] as String? ?? "",
      txt_feeds_prevention_key_001:
          json['txt_feeds_prevention_key_001'] as String? ?? "",
      txt_feeds_healthcheck_key_001:
          json['txt_feeds_healthcheck_key_001'] as String? ?? "",
      expedia_apply_code: json['expedia_apply_code'] as String? ?? "",
      expedia_booking_cancelled:
          json['expedia_booking_cancelled'] as String? ?? "",
      expedia_booking_confirmed:
          json['expedia_booking_confirmed'] as String? ?? "",
      expedia_booking_detail: json['expedia_booking_detail'] as String? ?? "",
      expedia_booking_detail_blue_status:
          json['expedia_booking_detail_blue_status'] as String? ?? "",
      expedia_booking_detail_booking_status:
          json['expedia_booking_detail_booking_status'] as String? ?? "",
      expedia_booking_detail_bronze_status:
          json['expedia_booking_detail_bronze_status'] as String? ?? "",
      expedia_booking_detail_date_booked:
          json['expedia_booking_detail_date_booked'] as String? ?? "",
      expedia_booking_detail_discount_applied:
          json['expedia_booking_detail_discount_applied'] as String? ?? "",
      expedia_booking_detail_discount_value:
          json['expedia_booking_detail_discount_value'] as String? ?? "",
      expedia_booking_detail_gold_status:
          json['expedia_booking_detail_gold_status'] as String? ?? "",
      expedia_booking_detail_hotel_stay:
          json['expedia_booking_detail_hotel_stay'] as String? ?? "",
      expedia_booking_detail_itinerary_number:
          json['expedia_booking_detail_itinerary_number'] as String? ?? "",
      expedia_booking_detail_no_status:
          json['expedia_booking_detail_no_status'] as String? ?? "",
      expedia_booking_detail_platinum_status:
          json['expedia_booking_detail_platinum_status'] as String? ?? "",
      expedia_booking_detail_silver_status:
          json['expedia_booking_detail_silver_status'] as String? ?? "",
      expedia_booking_history: json['expedia_booking_history'] as String? ?? "",
      expedia_checkout: json['expedia_checkout'] as String? ?? "",
      expedia_code_copy: json['expedia_code_copy'] as String? ?? "",
      expedia_code_discount_code:
          json['expedia_code_discount_code'] as String? ?? "",
      expedia_code_error_txt: json['expedia_code_error_txt'] as String? ?? "",
      expedia_code_expires: json['expedia_code_expires'] as String? ?? "",
      expedia_code_footnote_to_remember:
          json['expedia_code_footnote_to_remember'] as String? ?? "",
      expedia_code_percent_discount:
          json['expedia_code_percent_discount'] as String? ?? "",
      expedia_code_title: json['expedia_code_title'] as String? ?? "",
      expedia_code_to_redeem_reward:
          json['expedia_code_to_redeem_reward'] as String? ?? "",
      expedia_code_visit_website:
          json['expedia_code_visit_website'] as String? ?? "",
      expedia_data_privacy_disagree_dialog_message:
          json['expedia_data_privacy_disagree_dialog_message'] as String? ?? "",
      expedia_landing_available_until:
          json['expedia_landing_available_until'] as String? ?? "",
      expedia_landing_book_button:
          json['expedia_landing_book_button'] as String? ?? "",
      expedia_landing_bookings_per_year:
          json['expedia_landing_bookings_per_year'] as String? ?? "",
      expedia_landing_discount_codes_used:
          json['expedia_landing_discount_codes_used'] as String? ?? "",
      expedia_landing_landing_tap_to_book:
          json['expedia_landing_landing_tap_to_book'] as String? ?? "",
      expedia_landing_percent_discount:
          json['expedia_landing_percent_discount'] as String? ?? "",
      expedia_landing_title: json['expedia_landing_title'] as String? ?? "",
      expedia_reserve_hotel: json['expedia_reserve_hotel'] as String? ?? "",
      expedia_rewards_expedia_discount:
          json['expedia_rewards_expedia_discount'] as String? ?? "",
      expedia_rewards_expedia_nights_stay:
          json['expedia_rewards_expedia_nights_stay'] as String? ?? "",
      expedia_you_saved_on_hotel:
          json['expedia_you_saved_on_hotel'] as String? ?? "",
      expedia_bookings_used: json['expedia_bookings_used'] as String? ?? "",
      expedia_code_copied: json['expedia_code_copied'] as String? ?? "",
      txtexpedia_001: json['txtexpedia_001'] as String? ?? "",
      txtdiscount_001: json['txtdiscount_001'] as String? ?? "",
      txtusethiscodetoredeem_001:
          json['txtusethiscodetoredeem_001'] as String? ?? "",
      txtremembertotaponusecoupon_001:
          json['txtremembertotaponusecoupon_001'] as String? ?? "",
      txtdiscountcodewillnotworkfoxexpedia_001:
          json['txtdiscountcodewillnotworkfoxexpedia_001'] as String? ?? "",
      txtbookonexpedia_001: json['txtbookonexpedia_001'] as String? ?? "",
      txtcoins_002: json['txtcoins_002'] as String? ?? "",
      txtgiftcard_002: json['txtgiftcard_002'] as String? ?? "",
      txt_hf_AppExpFeedback_001:
          json['txt_hf_AppExpFeedback_001'] as String? ?? "",
      txt_hf_GeneralFeedback_001:
          json['txt_hf_GeneralFeedback_001'] as String? ?? "",
      txt_hf_TechnicalFeedback_001:
          json['txt_hf_TechnicalFeedback_001'] as String? ?? "",
      ctrl_btnviewdiscountcode_001:
          json['ctrl_btnviewdiscountcode_001'] as String? ?? "",
      txtreservehotel_001: json['txtreservehotel_001'] as String? ?? "",
      txtgotocheckout_001: json['txtgotocheckout_001'] as String? ?? "",
      txtapplycode_001: json['txtapplycode_001'] as String? ?? "",
      txtthisweeksprogress_001:
          json['txtthisweeksprogress_001'] as String? ?? "",
      txtnopointsearnedthisweek_001:
          json['txtnopointsearnedthisweek_001'] as String? ?? "",
      txtouchid: json['txtouchid'] as String? ?? "",
      txfingerprint: json['txfingerprint'] as String? ?? "",
      txtface: json['txtface'] as String? ?? "",
      txtfaceid: json['txtfaceid'] as String? ?? "",
      txtunknown: json['txtunknown'] as String? ?? "",
      gab_btnviewgymactivationbarcode_001:
          json['gab_btnviewgymactivationbarcode_001'] as String? ?? "",
      txtstreaksandmilestones_001:
          json['txtstreaksandmilestones_001'] as String? ?? "",
      txtnew_001: json['txtnew_001'] as String? ?? "",
      txtgoalstreaks_001: json['txtgoalstreaks_001'] as String? ?? "",
      txtnomilestones_001: json['txtnomilestones_001'] as String? ?? "",
      txtweeks_001: json['txtweeks_001'] as String? ?? "",
      txtrecentmilestone_001: json['txtrecentmilestone_001'] as String? ?? "",
      txtcurrentstreak_001: json['txtcurrentstreak_001'] as String? ?? "",
      txt_participating_gyms: json['txt_participating_gyms'] as String? ?? "",
      txtpointsmaynotreflect_immediately_snv_001:
          json['txtpointsmaynotreflect_immediately_snv_001'] as String? ?? "",
      txtpointsmaynotreflect_immediately_vhc_001:
          json['txtpointsmaynotreflect_immediately_vhc_001'] as String? ?? "",
      txtphysicalactivitygoalstreaks_001:
          json['txtphysicalactivitygoalstreaks_001'] as String? ?? "",
      txtmilestone_001: json['txtmilestone_001'] as String? ?? "",
      txtmilestones_001: json['txtmilestones_001'] as String? ?? "",
      txtachieved_001: json['txtachieved_001'] as String? ?? "",
      txthowgoalstreakswork_001:
          json['txthowgoalstreakswork_001'] as String? ?? "",
      txtnextmilestones_001: json['txtnextmilestones_001'] as String? ?? "",
      txtallmilestones_001: json['txtallmilestones_001'] as String? ?? "",
      txthitmilestones_001: json['txthitmilestones_001'] as String? ?? "",
      txtreachedmilestones_001:
          json['txtreachedmilestones_001'] as String? ?? "",
      txtachievinggoalstreak_001:
          json['txtachievinggoalstreak_001'] as String? ?? "",
      txtmilestones_002: json['txtmilestones_002'] as String? ?? "",
      txtsharemilestones_001: json['txtsharemilestones_001'] as String? ?? "",
      txtmilestonesachieved_001:
          json['txtmilestonesachieved_001'] as String? ?? "",
      txtmilestoneheader_001: json['txtmilestoneheader_001'] as String? ?? "",
      txtlongeststreak_001: json['txtlongeststreak_001'] as String? ?? "",
      txtachievedstreakmilestones_001:
          json['txtachievedstreakmilestones_001'] as String? ?? "",
      txtcompletemilestones_001:
          json['txtcompletemilestones_001'] as String? ?? "",
      txtbottomsheetcontent_001:
          json['txtbottomsheetcontent_001'] as String? ?? "",
      txtrewards2045: json['txtrewards2045'] as String? ?? "",
      txtrewards2046: json['txtrewards2046'] as String? ?? "",
      iconrewards2046: json['iconrewards2046'] as String? ?? "",
      iconrewards2045: json['iconrewards2045'] as String? ?? "",
      txtcouldntupdatepulldown_001:
          json['txtcouldntupdatepulldown_001'] as String? ?? "",
      txt_archive_gift_cards: json['txt_archive_gift_cards'] as String? ?? "",
      txt_archive_popup_description:
          json['txt_archive_popup_description'] as String? ?? "",
      vcrm_app_bar_title_001: json['vcrm_app_bar_title_001'] as String? ?? "",
      txt_showmore_001: json['txt_showmore_001'] as String? ?? "",
      txt_showless_001: json['txt_showless_001'] as String? ?? "",
      txthealthycholesterol_intro_002:
          json['txthealthycholesterol_intro_002'] as String? ?? "",
      txthealthguidetipstext_005:
          json['txthealthguidetipstext_005'] as String? ?? "",
      txthealthycholesterol_intro_003:
          json['txthealthycholesterol_intro_003'] as String? ?? "",
      snv_ctrl_btnsubmitanotheractivity_001:
          json['snv_ctrl_btnsubmitanotheractivity_001'] as String? ?? "",
      snv_txtdelete_001: json['snv_txtdelete_001'] as String? ?? "",
      txt_heart_rate_condition_002:
          json['txt_heart_rate_condition_002'] as String? ?? "",
      txtShortcutLinksHeading: json['txtShortcutLinksHeading'] as String? ?? "",
      txtifyoudonthavetheapp_001:
          json['txtifyoudonthavetheapp_001'] as String? ?? "",
      ctrl_btnlogintotataaia_001:
          json['ctrl_btnlogintotataaia_001'] as String? ?? "",
      txtenablesharingpermissions_001:
          json['txtenablesharingpermissions_001'] as String? ?? "",
      txtdialogbodysharepermisionsinsettings_001:
          json['txtdialogbodysharepermisionsinsettings_001'] as String? ?? "",
      ctrl_txtsgotosettings_001:
          json['ctrl_txtsgotosettings_001'] as String? ?? "",
      txtallowcameraaccess_001:
          json['txtallowcameraaccess_001'] as String? ?? "",
      txtdialogbodycamearapermisionsinsettings_001:
          json['txtdialogbodycamearapermisionsinsettings_001'] as String? ?? "",
      txt_activity_date_for_membership_year:
          json['txt_activity_date_for_membership_year'] as String? ?? "",
      txt_google_account_data_permission_001:
          json['txt_google_account_data_permission_001'] as String? ?? "",
      txtlanguage_001: json['txtlanguage_001'] as String? ?? "",
      old_wheel_ctrl_btnspinlater_001:
          json['old_wheel_ctrl_btnspinlater_001'] as String? ?? "",
      old_wheel_txt_doitlater_header_001:
          json['old_wheel_txt_doitlater_header_001'] as String? ?? "",
      old_wheel_txt_doitlater_header_002:
          json['old_wheel_txt_doitlater_header_002'] as String? ?? "",
      old_wheel_txt_doitlater_title_001:
          json['old_wheel_txt_doitlater_title_001'] as String? ?? "",
      old_wheel_txt_doitlater_title_002:
          json['old_wheel_txt_doitlater_title_002'] as String? ?? "",
      old_wheel_txt_doitlater_title_003:
          json['old_wheel_txt_doitlater_title_003'] as String? ?? "",
      old_wheel_txt_doitlater_desc_001:
          json['old_wheel_txt_doitlater_desc_001'] as String? ?? "",
      old_wheel_txt_doitlater_desc_002:
          json['old_wheel_txt_doitlater_desc_002'] as String? ?? "",
      old_wheel_txt_doitlater_desc_003:
          json['old_wheel_txt_doitlater_desc_003'] as String? ?? "",
      vg_dc_challenge_not_started:
          json['vg_dc_challenge_not_started'] as String? ?? "",
      vg_dc_commitment_period: json['vg_dc_commitment_period'] as String? ?? "",
      vg_dc_garmin_startchallenge_note:
          json['vg_dc_garmin_startchallenge_note'] as String? ?? "",
      vg_dc_polar_startchallenge_note:
          json['vg_dc_polar_startchallenge_note'] as String? ?? "",
      vg_dc_apple_startchallenge_note:
          json['vg_dc_apple_startchallenge_note'] as String? ?? "",
      vg_dc_link_device_text: json['vg_dc_link_device_text'] as String? ?? "",
      vg_dc_startchallenge_idle_btn:
          json['vg_dc_startchallenge_idle_btn'] as String? ?? "",
      vg_dc_startchallenge_progress_btn:
          json['vg_dc_startchallenge_progress_btn'] as String? ?? "",
      vg_dc_pending_page_header:
          json['vg_dc_pending_page_header'] as String? ?? "",
      vg_dc_purchase_date_text:
          json['vg_dc_purchase_date_text'] as String? ?? "",
      vg_dc_coins_text: json['vg_dc_coins_text'] as String? ?? "",
      ctrl_btnsave_001: json['ctrl_btnsave_001'] as String? ?? "",
      profile_language_txtheader_001:
          json['profile_language_txtheader_001'] as String? ?? "",
      profile_language_paragraph_001:
          json['profile_language_paragraph_001'] as String? ?? "",
      txt_languageSelection_01:
          json['txt_languageSelection_01'] as String? ?? "",
      txt_languageSelection_02:
          json['txt_languageSelection_02'] as String? ?? "",
      digital_stamp_txt_vitality_points_description:
          json['digital_stamp_txt_vitality_points_description'] as String? ??
              "",
      digital_stamp_txt_privacy_policy_description:
          json['digital_stamp_txt_privacy_policy_description'] as String? ?? "",
      ctrl_txt_credential_breakout:
          json['ctrl_txt_credential_breakout'] as String? ?? "",
      txt_contract_details_001:
          json['txt_contract_details_001'] as String? ?? "",
      androidStoreUrl: json['androidStoreUrl'] as String? ?? "",
      iosStoreUrl: json['iosStoreUrl'] as String? ?? "",
      txt_participating_gym_automatic_gym_note:
          json['txt_participating_gym_automatic_gym_note'] as String? ?? "",
      old_wheel_txt_earn: json['old_wheel_txt_earn'] as String? ?? "",
      old_wheel_txt_super_wheel_spin:
          json['old_wheel_txt_super_wheel_spin'] as String? ?? "",
      old_wheel_txt_this_week: json['old_wheel_txt_this_week'] as String? ?? "",
      old_wheel_txt_getactive_title:
          json['old_wheel_txt_getactive_title'] as String? ?? "",
      old_wheel_txt_getactive_desc:
          json['old_wheel_txt_getactive_desc'] as String? ?? "",
      old_wheel_txt_reach_your_target_title:
          json['old_wheel_txt_reach_your_target_title'] as String? ?? "",
      old_wheel_txt_reach_your_target_desc:
          json['old_wheel_txt_reach_your_target_desc'] as String? ?? "",
      old_wheel_txt_rarn_super_wheelspin_title:
          json['old_wheel_txt_rarn_super_wheelspin_title'] as String? ?? "",
      old_wheel_txt_rarn_super_wheelspin_desc:
          json['old_wheel_txt_rarn_super_wheelspin_desc'] as String? ?? "",
      txtgetyourinsurancediscount_001:
          json['txtgetyourinsurancediscount_001'] as String? ?? "",
      txtgetyourinsurancediscountcopy_001:
          json['txtgetyourinsurancediscountcopy_001'] as String? ?? "",
      old_wheel_txtsuperwheelspinsubtext_001:
          json['old_wheel_txtsuperwheelspinsubtext_001'] as String? ?? "",
      inactive_membership_date_format:
          json['inactive_membership_date_format'] as String? ?? "",
      txtstartchallenge_001: json['txtstartchallenge_001'] as String? ?? "",
      txt_linkdevice_001: json['txt_linkdevice_001'] as String? ?? "",
      vg_registration_pref_email_toggle_title:
          json['vg_registration_pref_email_toggle_title'] as String? ?? "",
      vg_registration_pref_email_toggle_description:
          json['vg_registration_pref_email_toggle_description'] as String? ??
              "",
      device_cashback_effective_from_date_format:
          json['device_cashback_effective_from_date_format'] as String? ?? "",
      device_cashback_effective_to_date_format:
          json['device_cashback_effective_to_date_format'] as String? ?? "",
      partner_description_223: json['partner_description_223'] as String? ?? "",
      partner_description_2029:
          json['partner_description_2029'] as String? ?? "",
      partner_description_2147:
          json['partner_description_2147'] as String? ?? "",
      partner_description_2141:
          json['partner_description_2141'] as String? ?? "",
      partner_description_2069:
          json['partner_description_2069'] as String? ?? "",
      partner_description_2143:
          json['partner_description_2143'] as String? ?? "",
      partner_description_2142:
          json['partner_description_2142'] as String? ?? "",
      unable_to_link_user: json['unable_to_link_user'] as String? ?? "",
      pg_txtallocatedsurpriseitemtitle_044:
          json['pg_txtallocatedsurpriseitemtitle_044'] as String? ?? "",
      old_wheel_ctrl_btnconfirm_001:
          json['old_wheel_ctrl_btnconfirm_001'] as String? ?? "",
      old_wheel_ctrl_btnvisitwebsite_001:
          json['old_wheel_ctrl_btnvisitwebsite_001'] as String? ?? "",
      vg_dc_aw_landing_previous_cycle_text:
          json['vg_dc_aw_landing_previous_cycle_text'] as String? ?? "",
      vg_dc_aw_landing_previous_cycle_body:
          json['vg_dc_aw_landing_previous_cycle_body'] as String? ?? "",
      ctrl_btnuploadfile_002: json['ctrl_btnuploadfile_002'] as String? ?? "",
      txt_login_biometric: json['txt_login_biometric'] as String? ?? "",
      txtgympartners_001: json['txtgympartners_001'] as String? ?? "",
      txtexerciseathome_001: json['txtexerciseathome_001'] as String? ?? "",
      txthealthscreening_001: json['txthealthscreening_001'] as String? ?? "",
      txthealthyfood_001: json['txthealthyfood_001'] as String? ?? "",
      txthealthygear_001: json['txthealthygear_001'] as String? ?? "",
      txtfitnessdevices_001: json['txtfitnessdevices_001'] as String? ?? "",
      txtyourdeviceishasbeendelivered_001:
          json['txtyourdeviceishasbeendelivered_001'] as String? ?? "",
      txtyourfirstgoalstartson_001:
          json['txtyourfirstgoalstartson_001'] as String? ?? "",
      tc_maximumteams_error_001:
          json['tc_maximumteams_error_001'] as String? ?? "",
      tc_sticker_history: json['tc_sticker_history'] as String? ?? "",
      tc_txtnodata_001: json['tc_txtnodata_001'] as String? ?? "",
      tc_btngotit: json['tc_btngotit'] as String? ?? "",
      pag_fitbit_reminder_dialog_title:
          json['pag_fitbit_reminder_dialog_title'] as String? ?? "",
      pag_fitbit_reminder_dialog_body:
          json['pag_fitbit_reminder_dialog_body'] as String? ?? "",
      pag_fitbit_reminder_dialog_button_do_no_show_again:
          json['pag_fitbit_reminder_dialog_button_do_no_show_again']
                  as String? ??
              "",
      txtupdateyouros_001: json['txtupdateyouros_001'] as String? ?? "",
      txtupdateyouros_002: json['txtupdateyouros_002'] as String? ?? "",
      txtsoftosupdate_001: json['txtsoftosupdate_001'] as String? ?? "",
      txtforcedosupdate_001: json['txtforcedosupdate_001'] as String? ?? "",
      ctrl_btnupdatelater_001: json['ctrl_btnupdatelater_001'] as String? ?? "",
      tc_team_members: json['tc_team_members'] as String? ?? "",
      tc_extend: json['tc_extend'] as String? ?? "",
      vj_format_date_compact: json['vj_format_date_compact'] as String? ?? "",
      ha_metric_20_result_value_1:
          json['ha_metric_20_result_value_1'] as String? ?? "",
      ha_metric_20_result_value_3:
          json['ha_metric_20_result_value_3'] as String? ?? "",
      urlReinstatedMem: json['urlReinstatedMem'] as String? ?? "",
      chip_sleep: json['chip_sleep'] as String? ?? "",
      chip_meditation: json['chip_meditation'] as String? ?? "",
      txtsupportedapporwearableapp:
          json['txtsupportedapporwearableapp'] as String? ?? "",
      txthealthconnect: json['txthealthconnect'] as String? ?? "",
      txt_app_iphone_001: json['txt_app_iphone_001'] as String? ?? "",
      txt_app_android_001: json['txt_app_android_001'] as String? ?? "",
      txt_device_001: json['txt_device_001'] as String? ?? "",
      txt_help_center_forms: json['txt_help_center_forms'] as String? ?? "",
      onboarding_modalactivated_txtthebestway_001:
          json['onboarding_modalactivated_txtthebestway_001'] as String? ?? "",
      onboarding_modalactivated_txtthebestway_002:
          json['onboarding_modalactivated_txtthebestway_002'] as String? ?? "",
      pag_fitbit_not_install_dialog_title:
          json['pag_fitbit_not_install_dialog_title'] as String? ?? "",
      pag_fitbit_not_install_dialog_body:
          json['pag_fitbit_not_install_dialog_body'] as String? ?? "",
      pag_fitbit_not_install_dialog_button_skip:
          json['pag_fitbit_not_install_dialog_button_skip'] as String? ?? "",
      pag_fitbit_not_install_dialog_button_download:
          json['pag_fitbit_not_install_dialog_button_download'] as String? ??
              "",
      onboarding_modalactivated_txtthebestway_001_ios:
          json['onboarding_modalactivated_txtthebestway_001_ios'] as String? ??
              "",
      vj_txt_bonus_001: json['vj_txt_bonus_001'] as String? ?? "",
      partner_description_2345:
          json['partner_description_2345'] as String? ?? "",
      txtgotohealthconnect: json['txtgotohealthconnect'] as String? ?? "",
      file_extension_manipulated_error:
          json['file_extension_manipulated_error'] as String? ?? "",
      file_format_recognise_error:
          json['file_format_recognise_error'] as String? ?? "",
      file_upload_error: json['file_upload_error'] as String? ?? "",
      file_unable_upload_error:
          json['file_unable_upload_error'] as String? ?? "",
      txtconnectyourmobilephonetofitbit:
          json['txtconnectyourmobilephonetofitbit'] as String? ?? "",
      txttrackstepsfromyourmobilephone:
          json['txttrackstepsfromyourmobilephone'] as String? ?? "",
      txtmaybelater: json['txtmaybelater'] as String? ?? "",
      txtgotofitbit: json['txtgotofitbit'] as String? ?? "",
      txtinsurername: json['txtinsurername'] as String? ?? "",
      expedia_off_001: json['expedia_off_001'] as String? ?? "",
      tc_chip_steps: json['tc_chip_steps'] as String? ?? "",
      status_detail_modal_date_format:
          json['status_detail_modal_date_format'] as String? ?? "",
      vj_connection_error: json['vj_connection_error'] as String? ?? "",
      vj_cancel: json['vj_cancel'] as String? ?? "",
      vj_try_again: json['vj_try_again'] as String? ?? "",
      vj_error_message: json['vj_error_message'] as String? ?? "",
      teamchallenges_team_left_success:
          json['teamchallenges_team_left_success'] as String? ?? "",
      teamchallenges_team_deleted_success:
          json['teamchallenges_team_deleted_success'] as String? ?? "",
      txt_username_001: json['txt_username_001'] as String? ?? "",
      txt_forgot_username_001: json['txt_forgot_username_001'] as String? ?? "",
      txt_contact_us_001: json['txt_contact_us_001'] as String? ?? "",
      txt_privacy_policy_001: json['txt_privacy_policy_001'] as String? ?? "",
      txt_privacy_policy_cannot_continue_001:
          json['txt_privacy_policy_cannot_continue_001'] as String? ?? "",
      txt_terms_and_conditions_required_001:
          json['txt_terms_and_conditions_required_001'] as String? ?? "",
      txtsupportedapp_001: json['txtsupportedapp_001'] as String? ?? "",
      txtsleeptrackingdevice_001:
          json['txtsleeptrackingdevice_001'] as String? ?? "",
      txtinsurer_001: json['txtinsurer_001'] as String? ?? "",
      txtfirstdaynextdaycheckingoal_002:
          json['txtfirstdaynextdaycheckingoal_002'] as String? ?? "",
      ha_section_Iconkey_121: json['ha_section_Iconkey_121'] as String? ?? "",
      txt_youearnedyourfirstpoints_001:
          json['txt_youearnedyourfirstpoints_001'] as String? ?? "",
      txt_morewaystoearn_001: json['txt_morewaystoearn_001'] as String? ?? "",
      txt_heart_rate_condition_003:
          json['txt_heart_rate_condition_003'] as String? ?? "",
      dialog_hd_asg: json['dialog_hd_asg'] as String? ?? "",
      dialog_body_asg: json['dialog_body_asg'] as String? ?? "",
      txtlinkafitnessdevice_001:
          json['txtlinkafitnessdevice_001'] as String? ?? "",
      txtbodydeviceapplink_002:
          json['txtbodydeviceapplink_002'] as String? ?? "",
      milestone_list_config_001:
          json['milestone_list_config_001'] as String? ?? "",
      txtautotrackeddevice_001:
          json['txtautotrackeddevice_001'] as String? ?? "",
      txtunlockthisreward_001: json['txtunlockthisreward_001'] as String? ?? "",
      txtunlocktvitalityhealth_001:
          json['txtunlocktvitalityhealth_001'] as String? ?? "",
      txtvitalityhealthreview_001:
          json['txtvitalityhealthreview_001'] as String? ?? "",
      txtcodesrevealed_001: json['txtcodesrevealed_001'] as String? ?? "",
      txtincreaserewardstatus_001:
          json['txtincreaserewardstatus_001'] as String? ?? "",
      txtincreasecodevalue_001:
          json['txtincreasecodevalue_001'] as String? ?? "",
      txtgetamonthlycode_001: json['txtgetamonthlycode_001'] as String? ?? "",
      txtdiscount15_001: json['txtdiscount15_001'] as String? ?? "",
      txtdiscount20_001: json['txtdiscount20_001'] as String? ?? "",
      txtdiscount25_001: json['txtdiscount25_001'] as String? ?? "",
      txtdiscount30_001: json['txtdiscount30_001'] as String? ?? "",
      txtsmartscale_001: json['txtsmartscale_001'] as String? ?? "",
      txt_type_username_password_001:
          json['txt_type_username_password_001'] as String? ?? "",
      txt_select_login_pref_001:
          json['txt_select_login_pref_001'] as String? ?? "",
      txtbminote_001: json['txtbminote_001'] as String? ?? "",
      ctrl_update_001: json['ctrl_update_001'] as String? ?? "",
      txt_bmi_condition_001: json['txt_bmi_condition_001'] as String? ?? "",
      txt_bmi_condition_002: json['txt_bmi_condition_002'] as String? ?? "",
      txt_contactus_description_security:
          json['txt_contactus_description_security'] as String? ?? "",
      txt_ourdetails_001: json['txt_ourdetails_001'] as String? ?? "",
      txt_telephone_001: json['txt_telephone_001'] as String? ?? "",
      txt_footer_contactus_001:
          json['txt_footer_contactus_001'] as String? ?? "",
      txt_contactus_availability_001:
          json['txt_contactus_availability_001'] as String? ?? "",
      txtadidas_001: json['txtadidas_001'] as String? ?? "",
      txtrevealcodeby_001: json['txtrevealcodeby_001'] as String? ?? "",
      txtcodereveal_001: json['txtcodereveal_001'] as String? ?? "",
      txtcouponcode_001: json['txtcouponcode_001'] as String? ?? "",
      txtarchive_001: json['txtarchive_001'] as String? ?? "",
      ctrl_btngotoadidas_001: json['ctrl_btngotoadidas_001'] as String? ?? "",
      txtcopied_001: json['txtcopied_001'] as String? ?? "",
      txtyourcantbedisplayederror_001:
          json['txtyourcantbedisplayederror_001'] as String? ?? "",
      ctrl_btngoback_001: json['ctrl_btngoback_001'] as String? ?? "",
      txtadidasstatuscoupon_5: json['txtadidasstatuscoupon_5'] as String? ?? "",
      txtadidasstatuscoupon_1: json['txtadidasstatuscoupon_1'] as String? ?? "",
      txtadidasstatuscoupon_2: json['txtadidasstatuscoupon_2'] as String? ?? "",
      txtadidasstatuscoupon_3: json['txtadidasstatuscoupon_3'] as String? ?? "",
      txtadidasstatuscoupon_4: json['txtadidasstatuscoupon_4'] as String? ?? "",
      txtAdidasStatusRewardDisclaimerstatus_5:
          json['txtAdidasStatusRewardDisclaimerstatus_5'] as String? ?? "",
      txtAdidasStatusRewardDisclaimerstatus_4:
          json['txtAdidasStatusRewardDisclaimerstatus_4'] as String? ?? "",
      txtAdidasStatusRewardDisclaimerstatus_3:
          json['txtAdidasStatusRewardDisclaimerstatus_3'] as String? ?? "",
      txtAdidasStatusRewardDisclaimerstatus_2:
          json['txtAdidasStatusRewardDisclaimerstatus_2'] as String? ?? "",
      txtAdidasStatusRewardDisclaimerstatus_1:
          json['txtAdidasStatusRewardDisclaimerstatus_1'] as String? ?? "",
      txtweek_001: json['txtweek_001'] as String? ?? "",
      txt_vitalityid_001: json['txt_vitalityid_001'] as String? ?? "",
      txtunsafedevice_detected_title_001:
          json['txtunsafedevice_detected_title_001'] as String? ?? "",
      txtunsafedevice_detected_description_001:
          json['txtunsafedevice_detected_description_001'] as String? ?? "",
      txtunsafedevice_detected_ok_001:
          json['txtunsafedevice_detected_ok_001'] as String? ?? "",
      txt_btn_segment_picker_active_001:
          json['txt_btn_segment_picker_active_001'] as String? ?? "",
      txt_btn_segment_picker_archived_001:
          json['txt_btn_segment_picker_archived_001'] as String? ?? "",
      txtyourrevealedcodesswillappearhere_001:
          json['txtyourrevealedcodesswillappearhere_001'] as String? ?? "",
      txtyoucurrentlyhavenoarchivedcodes_001:
          json['txtyoucurrentlyhavenoarchivedcodes_001'] as String? ?? "",
      ctrl_redeem_benefit: json['ctrl_redeem_benefit'] as String? ?? "",
      txt_prenuvolanding_footernote:
          json['txt_prenuvolanding_footernote'] as String? ?? "",
      txt_completevhrtounlock_001:
          json['txt_completevhrtounlock_001'] as String? ?? "",
      txt_prenuvolanding_enabler_001:
          json['txt_prenuvolanding_enabler_001'] as String? ?? "",
      txt_prenuvo_001: json['txt_prenuvo_001'] as String? ?? "",
      txt_prenuvodiscount_001: json['txt_prenuvodiscount_001'] as String? ?? "",
      txt_usevitalityidtoredeem_001:
          json['txt_usevitalityidtoredeem_001'] as String? ?? "",
      txt_gotoprenuvo_001: json['txt_gotoprenuvo_001'] as String? ?? "",
      txt_btn_segment_picker_available_001:
          json['txt_btn_segment_picker_available_001'] as String? ?? "",
      your_codes_001: json['your_codes_001'] as String? ?? "",
      sharestreaks_details_001:
          json['sharestreaks_details_001'] as String? ?? "",
      btn_sharenow_001: json['btn_sharenow_001'] as String? ?? "",
      txt_confirmbyuploadingsubmission_001:
          json['txt_confirmbyuploadingsubmission_001'] as String? ?? "",
      txt_diabetes_support: json['txt_diabetes_support'] as String? ?? "",
      ActivityCategoryLabeltxt_85:
          json['ActivityCategoryLabeltxt_85'] as String? ?? "",
      adidas_reward_landing_enabler_1:
          json['adidas_reward_landing_enabler_1'] as String? ?? "",
      adidas_reward_landing_enabler_2:
          json['adidas_reward_landing_enabler_2'] as String? ?? "",
      adidas_reward_landing_enabler_3:
          json['adidas_reward_landing_enabler_3'] as String? ?? "",
      sr_revealcode_partnername_1337:
          json['sr_revealcode_partnername_1337'] as String? ?? "",
      sr_revealcode_by_1337: json['sr_revealcode_by_1337'] as String? ?? "",
      sr_code_revealed_on_1337:
          json['sr_code_revealed_on_1337'] as String? ?? "",
      sr_revealcode_cta_subtext_1337:
          json['sr_revealcode_cta_subtext_1337'] as String? ?? "",
      sr_revealcode_cta_text_1337:
          json['sr_revealcode_cta_text_1337'] as String? ?? "",
      sr_revealcode_breakout_cta_text_1337:
          json['sr_revealcode_breakout_cta_text_1337'] as String? ?? "",
      sr_code_display_1337: json['sr_code_display_1337'] as String? ?? "",
      sr_code_copy_1337: json['sr_code_copy_1337'] as String? ?? "",
      sr_code_copied_1337: json['sr_code_copied_1337'] as String? ?? "",
      sr_code_archive_1337: json['sr_code_archive_1337'] as String? ?? "",
      sr_code_archived_1337: json['sr_code_archived_1337'] as String? ?? "",
      sr_revealcode_discountdetail_5_1337:
          json['sr_revealcode_discountdetail_5_1337'] as String? ?? "",
      sr_revealcode_discountdetail_1_1337:
          json['sr_revealcode_discountdetail_1_1337'] as String? ?? "",
      sr_revealcode_discountdetail_2_1337:
          json['sr_revealcode_discountdetail_2_1337'] as String? ?? "",
      sr_revealcode_discountdetail_3_1337:
          json['sr_revealcode_discountdetail_3_1337'] as String? ?? "",
      sr_revealcode_discountdetail_4_1337:
          json['sr_revealcode_discountdetail_4_1337'] as String? ?? "",
      sr_revealcode_reward_disclaimer_5_1337:
          json['sr_revealcode_reward_disclaimer_5_1337'] as String? ?? "",
      sr_revealcode_reward_disclaimer_4_1337:
          json['sr_revealcode_reward_disclaimer_4_1337'] as String? ?? "",
      sr_revealcode_reward_disclaimer_3_1337:
          json['sr_revealcode_reward_disclaimer_3_1337'] as String? ?? "",
      sr_revealcode_reward_disclaimer_2_1337:
          json['sr_revealcode_reward_disclaimer_2_1337'] as String? ?? "",
      sr_revealcode_reward_disclaimer_1_1337:
          json['sr_revealcode_reward_disclaimer_1_1337'] as String? ?? "",
      ActivityCategoryIconKey_85:
          json['ActivityCategoryIconKey_85'] as String? ?? "",
      txt_learn_more_about_the_rules_001:
          json['txt_learn_more_about_the_rules_001'] as String? ?? "",
      txt_about_this_reward_001:
          json['txt_about_this_reward_001'] as String? ?? "",
      ha_section_Iconkey_129: json['ha_section_Iconkey_129'] as String? ?? "",
      txt_types_001: json['txt_types_001'] as String? ?? "",
      txt_prenuvodiscount_instructions_001:
          json['txt_prenuvodiscount_instructions_001'] as String? ?? "",
      rewardpartner_prenuvo_url:
          json['rewardpartner_prenuvo_url'] as String? ?? "",
      txt_functionhealth_discount_001:
          json['txt_functionhealth_discount_001'] as String? ?? "",
      txt_functionhealth_001: json['txt_functionhealth_001'] as String? ?? "",
      txt_functionhealthdiscount_instructions_001:
          json['txt_functionhealthdiscount_instructions_001'] as String? ?? "",
      txt_gotofunctionhealth_001:
          json['txt_gotofunctionhealth_001'] as String? ?? "",
      rewardpartner_functionhealth_url:
          json['rewardpartner_functionhealth_url'] as String? ?? "",
      txt_completed_appare_1337:
          json['txt_completed_appare_1337'] as String? ?? "",
      txtheader_YouAndVitality_001:
          json['txtheader_YouAndVitality_001'] as String? ?? "",
      txtsubtext_YouAndVitality_001:
          json['txtsubtext_YouAndVitality_001'] as String? ?? "",
      status_rewards_status_1_subtitle_1337:
          json['status_rewards_status_1_subtitle_1337'] as String? ?? "",
      status_rewards_status_1_title_1337:
          json['status_rewards_status_1_title_1337'] as String? ?? "",
      status_rewards_status_2_subtitle_1337:
          json['status_rewards_status_2_subtitle_1337'] as String? ?? "",
      status_rewards_status_2_title_1337:
          json['status_rewards_status_2_title_1337'] as String? ?? "",
      status_rewards_status_3_subtitle_1337:
          json['status_rewards_status_3_subtitle_1337'] as String? ?? "",
      status_rewards_status_3_title_1337:
          json['status_rewards_status_3_title_1337'] as String? ?? "",
      status_rewards_status_4_subtitle_1337:
          json['status_rewards_status_4_subtitle_1337'] as String? ?? "",
      status_rewards_status_4_title_1337:
          json['status_rewards_status_4_title_1337'] as String? ?? "",
      status_rewards_status_5_subtitle_1337:
          json['status_rewards_status_5_subtitle_1337'] as String? ?? "",
      status_rewards_status_5_title_1337:
          json['status_rewards_status_5_title_1337'] as String? ?? "",
      txt_nutrisenselanding_enabler_001:
          json['txt_nutrisenselanding_enabler_001'] as String? ?? "",
      txt_nutrisenselanding_footernote:
          json['txt_nutrisenselanding_footernote'] as String? ?? "",
      txt_functionhealth_enabler_001:
          json['txt_functionhealth_enabler_001'] as String? ?? "",
      txt_functionhealth_footernote:
          json['txt_functionhealth_footernote'] as String? ?? "",
      txt_nutrisense_redeem_weblink_001:
          json['txt_nutrisense_redeem_weblink_001'] as String? ?? "",
      txt_maintain_your_healthy_lifestyle_001:
          json['txt_maintain_your_healthy_lifestyle_001'] as String? ?? "",
      txt_health_priority_grey_block_001:
          json['txt_health_priority_grey_block_001'] as String? ?? "",
      txt_health_priority_intro_004:
          json['txt_health_priority_intro_004'] as String? ?? "",
      txt_tip_body_001: json['txt_tip_body_001'] as String? ?? "",
      txt_tip_body_002: json['txt_tip_body_002'] as String? ?? "",
      txt_tip_body_003: json['txt_tip_body_003'] as String? ?? "",
      partner_description_113600:
          json['partner_description_113600'] as String? ?? "",
      archived_codes_001: json['archived_codes_001'] as String? ?? "",
      txt_no_archived_codes_for_year_001:
          json['txt_no_archived_codes_for_year_001'] as String? ?? "",
      txt_no_archived_codes_for_year_copy_001:
          json['txt_no_archived_codes_for_year_copy_001'] as String? ?? "",
      txt_you_currently_have_no_codes_001:
          json['txt_you_currently_have_no_codes_001'] as String? ?? "",
      txt_you_currently_have_no_codes_copy_001:
          json['txt_you_currently_have_no_codes_copy_001'] as String? ?? "",
      txt_no_archived_codes_001:
          json['txt_no_archived_codes_001'] as String? ?? "",
      txt_no_archived_codes_copy_001:
          json['txt_no_archived_codes_copy_001'] as String? ?? "",
      txt_oura_enabler_001: json['txt_oura_enabler_001'] as String? ?? "",
      txt_oura_footernote: json['txt_oura_footernote'] as String? ?? "",
      rewardpartner_oura_discount_url:
          json['rewardpartner_oura_discount_url'] as String? ?? "",
      txt_activefitdirect_001: json['txt_activefitdirect_001'] as String? ?? "",
      txt_afd_enabler_001: json['txt_afd_enabler_001'] as String? ?? "",
      txt_afd_footernote: json['txt_afd_footernote'] as String? ?? "",
      txtwehavealittleproblemcopy_001:
          json['txtwehavealittleproblemcopy_001'] as String? ?? "",
      txtwehavealittleproblem_001:
          json['txtwehavealittleproblem_001'] as String? ?? "",
      ActivityCategoryLabeltxt_87:
          json['ActivityCategoryLabeltxt_87'] as String? ?? "",
      ActivityCategoryIconKey_87:
          json['ActivityCategoryIconKey_87'] as String? ?? "",
      txt_afd_redeem_weblink_001:
          json['txt_afd_redeem_weblink_001'] as String? ?? "",
      sr_archive_item_1337: json['sr_archive_item_1337'] as String? ?? "",
      sr_view_terms_1_1337: json['sr_view_terms_1_1337'] as String? ?? "",
      sr_view_terms_2_1337: json['sr_view_terms_2_1337'] as String? ?? "",
      sr_terms_condition_1337: json['sr_terms_condition_1337'] as String? ?? "",
      txt_reward_code_copied: json['txt_reward_code_copied'] as String? ?? "",
      txtcodesuccessfullyarchived_001:
          json['txtcodesuccessfullyarchived_001'] as String? ?? "",
      txtcodessuccessfullyarchived_001:
          json['txtcodessuccessfullyarchived_001'] as String? ?? "",
      txtSafeDriving_001: json['txtSafeDriving_001'] as String? ?? "",
      txtsubmitproof_002: json['txtsubmitproof_002'] as String? ?? "",
      sd_submitProof_text_001: json['sd_submitProof_text_001'] as String? ?? "",
      txtDateNote_001: json['txtDateNote_001'] as String? ?? "",
      txtPrenatalCare_001: json['txtPrenatalCare_001'] as String? ?? "",
      txtsubmitproof_003: json['txtsubmitproof_003'] as String? ?? "",
      txtdownloadform_001: json['txtdownloadform_001'] as String? ?? "",
      pc_submitProof_text_001: json['pc_submitProof_text_001'] as String? ?? "",
      sr_code_detail_discount_detail_1337:
          json['sr_code_detail_discount_detail_1337'] as String? ?? "",
      sr_code_detail_dynamic_terms_1337:
          json['sr_code_detail_dynamic_terms_1337'] as String? ?? "",
      txt_whoop_footernote: json['txt_whoop_footernote'] as String? ?? "",
      txt_doyouhavewhoopmembership_001:
          json['txt_doyouhavewhoopmembership_001'] as String? ?? "",
      txt_pleaseletusknow_001: json['txt_pleaseletusknow_001'] as String? ?? "",
      txt_iamnewtowhoop_001: json['txt_iamnewtowhoop_001'] as String? ?? "",
      txt_ihaveexistingwhoop_001:
          json['txt_ihaveexistingwhoop_001'] as String? ?? "",
      txt_proceed_001: json['txt_proceed_001'] as String? ?? "",
      txt_usepromojohnhancock_001:
          json['txt_usepromojohnhancock_001'] as String? ?? "",
      txt_usethiscodeatcheckout_001:
          json['txt_usethiscodeatcheckout_001'] as String? ?? "",
      imagepath_dms_whoopdevice:
          json['imagepath_dms_whoopdevice'] as String? ?? "",
      txt_highlightfeatures_001:
          json['txt_highlightfeatures_001'] as String? ?? "",
      txt_learnmoreaboutwhoop_001:
          json['txt_learnmoreaboutwhoop_001'] as String? ?? "",
      txt_redeemwhoopmembership_001:
          json['txt_redeemwhoopmembership_001'] as String? ?? "",
      txt_whoop_feature_001: json['txt_whoop_feature_001'] as String? ?? "",
      txt_whoop_feature_002: json['txt_whoop_feature_002'] as String? ?? "",
      txt_whoop_feature_003: json['txt_whoop_feature_003'] as String? ?? "",
      txt_whoop_feature_004: json['txt_whoop_feature_004'] as String? ?? "",
      txt_whoop_feature_005: json['txt_whoop_feature_005'] as String? ?? "",
      txt_whoopmembership_001: json['txt_whoopmembership_001'] as String? ?? "",
      weblink_learnmore_whoop: json['weblink_learnmore_whoop'] as String? ?? "",
      weblink_redeemmembership_whoop:
          json['weblink_redeemmembership_whoop'] as String? ?? "",
      weblink_renewmembership_whoop:
          json['weblink_renewmembership_whoop'] as String? ?? "",
      txt_burned_calories_details_001:
          json['txt_burned_calories_details_001'] as String? ?? "",
      txt_proofnote_sd_001: json['txt_proofnote_sd_001'] as String? ?? "",
      txt_proofnote_sd_002: json['txt_proofnote_sd_002'] as String? ?? "",
      txt_proofnote_sd_003: json['txt_proofnote_sd_003'] as String? ?? "",
      txt_proofnote_pc_001: json['txt_proofnote_pc_001'] as String? ?? "",
      txt_proofnote_pc_002: json['txt_proofnote_pc_002'] as String? ?? "",
      txt_proofnote_pc_003: json['txt_proofnote_pc_003'] as String? ?? "",
      txt_proofnote_pc_004: json['txt_proofnote_pc_004'] as String? ?? "",
      txt_proofnote_pc_005: json['txt_proofnote_pc_005'] as String? ?? "",
      txt_diabetes_hba1c_001: json['txt_diabetes_hba1c_001'] as String? ?? "",
      txtproofnote_pc_001: json['txtproofnote_pc_001'] as String? ?? "",
      txt_grail_enabler_001: json['txt_grail_enabler_001'] as String? ?? "",
      txt_grail_footernote: json['txt_grail_footernote'] as String? ?? "",
      txt_grail_age_eligible_001:
          json['txt_grail_age_eligible_001'] as String? ?? "",
      txt_grail_001: json['txt_grail_001'] as String? ?? "",
      txt_graildiscount_001: json['txt_graildiscount_001'] as String? ?? "",
      txt_accessid_001: json['txt_accessid_001'] as String? ?? "",
      txt_useaccessidtoredeem_001:
          json['txt_useaccessidtoredeem_001'] as String? ?? "",
      txt_graildiscount_instructions_001:
          json['txt_graildiscount_instructions_001'] as String? ?? "",
      txt_gotograil_001: json['txt_gotograil_001'] as String? ?? "",
      rewardpartner_grail_url: json['rewardpartner_grail_url'] as String? ?? "",
      txt_grail_freeTest_001: json['txt_grail_freeTest_001'] as String? ?? "",
      ActivityCategoryLabeltxt_93:
          json['ActivityCategoryLabeltxt_93'] as String? ?? "",
      ActivityCategoryIconKey_93:
          json['ActivityCategoryIconKey_93'] as String? ?? "",
      txthealthprioritygreyblock_001:
          json['txthealthprioritygreyblock_001'] as String? ?? "",
      txthealthpriorityintro_004:
          json['txthealthpriorityintro_004'] as String? ?? "",
      txttipbody_001: json['txttipbody_001'] as String? ?? "",
      txttipbody_002: json['txttipbody_002'] as String? ?? "",
      txttipbody_003: json['txttipbody_003'] as String? ?? "",
      hc_text_participating_partners:
          json['hc_text_participating_partners'] as String? ?? "",
      hc_text_schedule_screening:
          json['hc_text_schedule_screening'] as String? ?? "",
      hc_text_reperio: json['hc_text_reperio'] as String? ?? "",
      hc_text_quest_diagnostics:
          json['hc_text_quest_diagnostics'] as String? ?? "",
      earn_points_active_calories_001:
          json['earn_points_active_calories_001'] as String? ?? "",
      earn_points_active_calories_002:
          json['earn_points_active_calories_002'] as String? ?? "",
      earn_points_active_calories_003:
          json['earn_points_active_calories_003'] as String? ?? "",
      earn_points_active_calories_details:
          json['earn_points_active_calories_details'] as String? ?? "",
      sr_revealcode_partner_store_url_1337:
          json['sr_revealcode_partner_store_url_1337'] as String? ?? "",
      dt_format_short_day_month_full_year:
          json['dt_format_short_day_month_full_year'] as String? ?? "",
      vna_expedia_contentnote: json['vna_expedia_contentnote'] as String? ?? "",
      txt_onboarding_reward_imagepath_freemium:
          json['txt_onboarding_reward_imagepath_freemium'] as String? ?? "",
      txt_onboarding3_header_freemium:
          json['txt_onboarding3_header_freemium'] as String? ?? "",
      txt_onboarding3_subtext_freemium:
          json['txt_onboarding3_subtext_freemium'] as String? ?? "",
      txt_onboarding4_header_freemium:
          json['txt_onboarding4_header_freemium'] as String? ?? "",
      txt_onboarding4_subtext_freemium:
          json['txt_onboarding4_subtext_freemium'] as String? ?? "",
      txt_onboarding5_header_freemium:
          json['txt_onboarding5_header_freemium'] as String? ?? "",
      txt_onboarding5_subtext_freemium:
          json['txt_onboarding5_subtext_freemium'] as String? ?? "",
      txtbronze_subtext_001_freemium:
          json['txtbronze_subtext_001_freemium'] as String? ?? "",
      txtsilver_subtext_001_freemium:
          json['txtsilver_subtext_001_freemium'] as String? ?? "",
      txtgold_subtext_001_freemium:
          json['txtgold_subtext_001_freemium'] as String? ?? "",
      txtglucose_intro_004: json['txtglucose_intro_004'] as String? ?? "",
      txt_check_in_at_the_gym: json['txt_check_in_at_the_gym'] as String? ?? "",
      txt_manually_submit_a_workout:
          json['txt_manually_submit_a_workout'] as String? ?? "",
      txt_submit_workout: json['txt_submit_workout'] as String? ?? "",
      txt_gym_check_in: json['txt_gym_check_in'] as String? ?? "",
      txt_steps_description: json['txt_steps_description'] as String? ?? "",
      earn_points_steps_001: json['earn_points_steps_001'] as String? ?? "",
      earn_points_steps_002: json['earn_points_steps_002'] as String? ?? "",
      earn_points_steps_003: json['earn_points_steps_003'] as String? ?? "",
      earn_points_heart_rate_001:
          json['earn_points_heart_rate_001'] as String? ?? "",
      earn_points_heart_rate_002:
          json['earn_points_heart_rate_002'] as String? ?? "",
      earn_points_heart_rate_003:
          json['earn_points_heart_rate_003'] as String? ?? "",
      earn_points_workout_calories_001:
          json['earn_points_workout_calories_001'] as String? ?? "",
      earn_points_workout_calories_002:
          json['earn_points_workout_calories_002'] as String? ?? "",
      earn_points_workout_calories_003:
          json['earn_points_workout_calories_003'] as String? ?? "",
      txtplatinum_subtext_001_freemium:
          json['txtplatinum_subtext_001_freemium'] as String? ?? "",
      txt_headspace_enabler_001:
          json['txt_headspace_enabler_001'] as String? ?? "",
      txt_headspace_footernote:
          json['txt_headspace_footernote'] as String? ?? "",
      txt_bookinglastupdatedon_001:
          json['txt_bookinglastupdatedon_001'] as String? ?? "",
      txt_payoutdate_enabler_001:
          json['txt_payoutdate_enabler_001'] as String? ?? "",
      txt_maintainplatinum_001:
          json['txt_maintainplatinum_001'] as String? ?? "",
      txt_streakprogress_001: json['txt_streakprogress_001'] as String? ?? "",
      txt_achieveplatinumvhr_001:
          json['txt_achieveplatinumvhr_001'] as String? ?? "",
      txt_maintainplatinum_vhr_001:
          json['txt_maintainplatinum_vhr_001'] as String? ?? "",
      txt_yearofstreak_001: json['txt_yearofstreak_001'] as String? ?? "",
      txt_free_oneyearmembership_001:
          json['txt_free_oneyearmembership_001'] as String? ?? "",
      txt_submit_a_photo_of_your_workout:
          json['txt_submit_a_photo_of_your_workout'] as String? ?? "",
      txt_at_home_workout: json['txt_at_home_workout'] as String? ?? "",
      txt_manually_submit_a_workout_title:
          json['txt_manually_submit_a_workout_title'] as String? ?? "",
      txt_manually_submit_a_workout_desc:
          json['txt_manually_submit_a_workout_desc'] as String? ?? "",
      vhc_alt_header_txtpoints_001:
          json['vhc_alt_header_txtpoints_001'] as String? ?? "",
      vhc_alt_submenu_txtpoints_001:
          json['vhc_alt_submenu_txtpoints_001'] as String? ?? "",
      vhc_txt_max_points_for_submission_type_001:
          json['vhc_txt_max_points_for_submission_type_001'] as String? ?? "",
      vhc_txt_max_points_earn_for_activity:
          json['vhc_txt_max_points_earn_for_activity'] as String? ?? "",
      vhc_txt_max_points_earn_for_health_check_submission:
          json['vhc_txt_max_points_earn_for_health_check_submission']
                  as String? ??
              "",
      headspace_breakout_url_01:
          json['headspace_breakout_url_01'] as String? ?? "",
      txtunlockthisreward_002: json['txtunlockthisreward_002'] as String? ?? "",
      txtactivateyourhealthyfood_001:
          json['txtactivateyourhealthyfood_001'] as String? ?? "",
      txt_hf_appexperiencefeedback_001:
          json['txt_hf_appexperiencefeedback_001'] as String? ?? "",
      txt_hf_autoapplied_001: json['txt_hf_autoapplied_001'] as String? ?? "",
      txt_hf_cashbabck_calculated_001:
          json['txt_hf_cashbabck_calculated_001'] as String? ?? "",
      txt_hf_discount_001: json['txt_hf_discount_001'] as String? ?? "",
      txt_hf_enabler_001: json['txt_hf_enabler_001'] as String? ?? "",
      txt_hf_footernote: json['txt_hf_footernote'] as String? ?? "",
      txt_rep_enabler_001: json['txt_rep_enabler_001'] as String? ?? "",
      txt_que_enabler_001: json['txt_que_enabler_001'] as String? ?? "",
      ActivityCategoryLabeltxt_96:
          json['ActivityCategoryLabeltxt_96'] as String? ?? "",
      ActivityCategoryIconKey_96:
          json['ActivityCategoryIconKey_96'] as String? ?? "",
      ctrl_btnactivateprogram_001:
          json['ctrl_btnactivateprogram_001'] as String? ?? "",
      jh_hc_cotinine_earn_points:
          json['jh_hc_cotinine_earn_points'] as String? ?? "",
      txt_healthy_gear_purchase_rei_cta:
          json['txt_healthy_gear_purchase_rei_cta'] as String? ?? "",
      txt_healthy_gear_order_history:
          json['txt_healthy_gear_order_history'] as String? ?? "",
      txt_healthy_gear_gift_card_value_001:
          json['txt_healthy_gear_gift_card_value_001'] as String? ?? "",
      txt_healthy_gear_you_saved_001:
          json['txt_healthy_gear_you_saved_001'] as String? ?? "",
      txt_healthy_gear_landing_enabler_001:
          json['txt_healthy_gear_landing_enabler_001'] as String? ?? "",
      txt_healthy_gear_giftcard_value_001:
          json['txt_healthy_gear_giftcard_value_001'] as String? ?? "",
      txt_healthy_gear_vhr_unlock_001:
          json['txt_healthy_gear_vhr_unlock_001'] as String? ?? "",
      partner_description_113615:
          json['partner_description_113615'] as String? ?? "",
      partner_description_113616:
          json['partner_description_113616'] as String? ?? "",
      txt_fitbit_discount_membership_001:
          json['txt_fitbit_discount_membership_001'] as String? ?? "",
      txt_fitbit_discount_feature_001:
          json['txt_fitbit_discount_feature_001'] as String? ?? "",
      txt_fitbit_discount_feature_002:
          json['txt_fitbit_discount_feature_002'] as String? ?? "",
      txt_fitbit_discount_feature_003:
          json['txt_fitbit_discount_feature_003'] as String? ?? "",
      txt_fitbit_discount_feature_004:
          json['txt_fitbit_discount_feature_004'] as String? ?? "",
      txt_fitbit_discount_feature_005:
          json['txt_fitbit_discount_feature_005'] as String? ?? "",
      txt_fitbit_discount_feature_006:
          json['txt_fitbit_discount_feature_006'] as String? ?? "",
      txt_learnmoreabout_fitbit_discount_001:
          json['txt_learnmoreabout_fitbit_discount_001'] as String? ?? "",
      txt_redeem_fitbit_discount_membership_001:
          json['txt_redeem_fitbit_discount_membership_001'] as String? ?? "",
      txt_fitbit_discount_footernote:
          json['txt_fitbit_discount_footernote'] as String? ?? "",
      txt_claim_your_complimentary_device:
          json['txt_claim_your_complimentary_device'] as String? ?? "",
      txt_garmin_vivosmart: json['txt_garmin_vivosmart'] as String? ?? "",
      txt_highlight_features: json['txt_highlight_features'] as String? ?? "",
      txt_features_1year_battery:
          json['txt_features_1year_battery'] as String? ?? "",
      txt_shows_sleep_and_workout_data_including_intensity:
          json['txt_shows_sleep_and_workout_data_including_intensity']
                  as String? ??
              "",
      txt_automatic_activity_detection:
          json['txt_automatic_activity_detection'] as String? ?? "",
      txt_stay_active_reminders:
          json['txt_stay_active_reminders'] as String? ?? "",
      txt_automatic_sync_to_garmin_connect:
          json['txt_automatic_sync_to_garmin_connect'] as String? ?? "",
      txt_learn_more_about_garmin_vivosmart_5:
          json['txt_learn_more_about_garmin_vivosmart_5'] as String? ?? "",
      txt_claim_garmin_vivosmart_5:
          json['txt_claim_garmin_vivosmart_5'] as String? ?? "",
      txt_footnote_complimantary:
          json['txt_footnote_complimantary'] as String? ?? "",
    );

Map<String, dynamic> _$VGResourceBundleToJson(VGResourceBundle instance) =>
    <String, dynamic>{
      'already_have_an_account_2': instance.already_have_an_account_2,
      'appdevices_applehealth_txtconnect_001':
          instance.appdevices_applehealth_txtconnect_001,
      'appdevices_applehealth_txtmanagepermissions_001':
          instance.appdevices_applehealth_txtmanagepermissions_001,
      'appdevices_applehealth_txtsynctime_001':
          instance.appdevices_applehealth_txtsynctime_001,
      'appdevices_fitbit_txtconnect_001':
          instance.appdevices_fitbit_txtconnect_001,
      'appdevices_fitbit_txtmanagepermissions_001':
          instance.appdevices_fitbit_txtmanagepermissions_001,
      'appdevices_fitbit_txtsynctime_001':
          instance.appdevices_fitbit_txtsynctime_001,
      'appdevices_garmin_txtconnect_001':
          instance.appdevices_garmin_txtconnect_001,
      'appdevices_garmin_txtmanagepermissions_001':
          instance.appdevices_garmin_txtmanagepermissions_001,
      'appdevices_garmin_txtsynctime_001':
          instance.appdevices_garmin_txtsynctime_001,
      'appdevices_googlefit_txtconnect_001':
          instance.appdevices_googlefit_txtconnect_001,
      'appdevices_googlefit_txtmanagepermissions_001':
          instance.appdevices_googlefit_txtmanagepermissions_001,
      'appdevices_googlefit_txtsynctime_001':
          instance.appdevices_googlefit_txtsynctime_001,
      'appdevices_polar_txtconnect_001':
          instance.appdevices_polar_txtconnect_001,
      'appdevices_polar_txtmanagepermissions_001':
          instance.appdevices_polar_txtmanagepermissions_001,
      'appdevices_polar_txtsynctime_001':
          instance.appdevices_polar_txtsynctime_001,
      'appdevices_samsunghealth_txtconnect_001':
          instance.appdevices_samsunghealth_txtconnect_001,
      'appdevices_samsunghealth_txtmanagepermissions_001':
          instance.appdevices_samsunghealth_txtmanagepermissions_001,
      'appdevices_samsunghealth_txtsynctime_001':
          instance.appdevices_samsunghealth_txtsynctime_001,
      'appdevices_strava_txtconnect_001':
          instance.appdevices_strava_txtconnect_001,
      'appdevices_strava_txtmanagepermissions_001':
          instance.appdevices_strava_txtmanagepermissions_001,
      'appdevices_strava_txtsynctime_001':
          instance.appdevices_strava_txtsynctime_001,
      'appdevices_suunto_txtconnect_001':
          instance.appdevices_suunto_txtconnect_001,
      'appdevices_suunto_txtmanagepermissions_001':
          instance.appdevices_suunto_txtmanagepermissions_001,
      'appdevices_suunto_txtsynctime_001':
          instance.appdevices_suunto_txtsynctime_001,
      'appdevices_withings_txtconnect_001':
          instance.appdevices_withings_txtconnect_001,
      'appdevices_withings_txtmanagepermissions_001':
          instance.appdevices_withings_txtmanagepermissions_001,
      'appdevices_withings_txtsynctime_001':
          instance.appdevices_withings_txtsynctime_001,
      'apppsdevices_landing_txtlastsyncdate_001':
          instance.apppsdevices_landing_txtlastsyncdate_001,
      'appsdevices_fitbit_txtsyncaccount_001':
          instance.appsdevices_fitbit_txtsyncaccount_001,
      'appsdevices_samsunghealth_txtallpermissions_001':
          instance.appsdevices_samsunghealth_txtallpermissions_001,
      'appsdevices_samsunghealth_txtbodyallpermissions_001':
          instance.appsdevices_samsunghealth_txtbodyallpermissions_001,
      'appsdevices_samsunghealth_txtbodyconnectionerror_001':
          instance.appsdevices_samsunghealth_txtbodyconnectionerror_001,
      'appsdevices_samsunghealth_txtbodydisconnect_001':
          instance.appsdevices_samsunghealth_txtbodydisconnect_001,
      'appsdevices_samsunghealth_txtconnected_001':
          instance.appsdevices_samsunghealth_txtconnected_001,
      'appsdevices_samsunghealth_txtdisconnectalert_001':
          instance.appsdevices_samsunghealth_txtdisconnectalert_001,
      'appsdevices_samsunghealth_txthddisconnect_001':
          instance.appsdevices_samsunghealth_txthddisconnect_001,
      'appsdevices_samsunghealth_txtsyncaccount_001':
          instance.appsdevices_samsunghealth_txtsyncaccount_001,
      'appsdevices_samsungsupport_txtbodyhowitworks_001':
          instance.appsdevices_samsungsupport_txtbodyhowitworks_001,
      'appsdevices_samsungsupport_txthdhowitworks_001':
          instance.appsdevices_samsungsupport_txthdhowitworks_001,
      'body_AppCrashSettins': instance.body_AppCrashSettins,
      'body_cholestrolAbout': instance.body_cholestrolAbout,
      'body_EmailSettings': instance.body_EmailSettings,
      'body_emptyState_': instance.body_emptyState_,
      'body_emptyState_noPromotion': instance.body_emptyState_noPromotion,
      'body_MembershipCancelled': instance.body_MembershipCancelled,
      'body_NeedHelp': instance.body_NeedHelp,
      'body_Needhelp': instance.body_Needhelp,
      'body_PrivacyPolicy': instance.body_PrivacyPolicy,
      'body_SaveEmailLogin': instance.body_SaveEmailLogin,
      'body_ScreenAnalyticsSettings': instance.body_ScreenAnalyticsSettings,
      'body_submitproofparagraph': instance.body_submitproofparagraph,
      'body_TsCs': instance.body_TsCs,
      'btn_Agree': instance.btn_Agree,
      'btn_Disagree': instance.btn_Disagree,
      'btn_LogOut': instance.btn_LogOut,
      'btn_next': instance.btn_next,
      'btn_ResetPw': instance.btn_ResetPw,
      'btn_SavePreferences': instance.btn_SavePreferences,
      'btn_showAll': instance.btn_showAll,
      'chip_activeEnergy': instance.chip_activeEnergy,
      'chip_heartRate': instance.chip_heartRate,
      'chip_steps': instance.chip_steps,
      'chip_weight': instance.chip_weight,
      'crd_title_supportQ1': instance.crd_title_supportQ1,
      'crd_title_supportQ2': instance.crd_title_supportQ2,
      'crd_title_supportQ3': instance.crd_title_supportQ3,
      'crd_title_supportQ4': instance.crd_title_supportQ4,
      'ctrl_appdevices_applehealth_btnConnect_001':
          instance.ctrl_appdevices_applehealth_btnConnect_001,
      'ctrl_appdevices_applehealth_txtsupport_001':
          instance.ctrl_appdevices_applehealth_txtsupport_001,
      'ctrl_appdevices_fitbit_btnConnect_001':
          instance.ctrl_appdevices_fitbit_btnConnect_001,
      'ctrl_appdevices_fitbit_txtsupport_001':
          instance.ctrl_appdevices_fitbit_txtsupport_001,
      'ctrl_appdevices_garmin_btnConnect_001':
          instance.ctrl_appdevices_garmin_btnConnect_001,
      'ctrl_appdevices_garmin_txtsupport_001':
          instance.ctrl_appdevices_garmin_txtsupport_001,
      'ctrl_appdevices_googlefit_btnConnect_001':
          instance.ctrl_appdevices_googlefit_btnConnect_001,
      'ctrl_appdevices_googlefit_txtsupport_001':
          instance.ctrl_appdevices_googlefit_txtsupport_001,
      'ctrl_appdevices_polar_btnConnect_001':
          instance.ctrl_appdevices_polar_btnConnect_001,
      'ctrl_appdevices_polar_txtsupport_001':
          instance.ctrl_appdevices_polar_txtsupport_001,
      'ctrl_appdevices_samsunghealth_btnConnect_001':
          instance.ctrl_appdevices_samsunghealth_btnConnect_001,
      'ctrl_appdevices_samsunghealth_txtsupport_001':
          instance.ctrl_appdevices_samsunghealth_txtsupport_001,
      'ctrl_appdevices_strava_btnConnect_001':
          instance.ctrl_appdevices_strava_btnConnect_001,
      'ctrl_appdevices_strava_txtsupport_001':
          instance.ctrl_appdevices_strava_txtsupport_001,
      'ctrl_appdevices_suunto_btnConnect_001':
          instance.ctrl_appdevices_suunto_btnConnect_001,
      'ctrl_appdevices_suunto_txtsupport_001':
          instance.ctrl_appdevices_suunto_txtsupport_001,
      'ctrl_appdevices_withings_btnConnect_001':
          instance.ctrl_appdevices_withings_btnConnect_001,
      'ctrl_appdevices_withings_txtsupport_001':
          instance.ctrl_appdevices_withings_txtsupport_001,
      'ctrl_appsdevices_applehealth_btndisconnect_001':
          instance.ctrl_appsdevices_applehealth_btndisconnect_001,
      'ctrl_appsdevices_applehealth_btnmanpermissions_001':
          instance.ctrl_appsdevices_applehealth_btnmanpermissions_001,
      'ctrl_appsdevices_fitbit_btndisconnect_001':
          instance.ctrl_appsdevices_fitbit_btndisconnect_001,
      'ctrl_appsdevices_garmin_btndisconnect_001':
          instance.ctrl_appsdevices_garmin_btndisconnect_001,
      'ctrl_appsdevices_googlefit_btndisconnect_001':
          instance.ctrl_appsdevices_googlefit_btndisconnect_001,
      'ctrl_appsdevices_polar_btndisconnect_001':
          instance.ctrl_appsdevices_polar_btndisconnect_001,
      'ctrl_appsdevices_samsunghealth_btndisconnect_001':
          instance.ctrl_appsdevices_samsunghealth_btndisconnect_001,
      'ctrl_appsdevices_samsunghealth_btnmanpermissions_001':
          instance.ctrl_appsdevices_samsunghealth_btnmanpermissions_001,
      'ctrl_appsdevices_strava_btndisconnect_001':
          instance.ctrl_appsdevices_strava_btndisconnect_001,
      'ctrl_appsdevices_suunto_btndisconnect_001':
          instance.ctrl_appsdevices_suunto_btndisconnect_001,
      'ctrl_appsdevices_withings_btndisconnect_001':
          instance.ctrl_appsdevices_withings_btndisconnect_001,
      'ctrl_btnactivateacc_001': instance.ctrl_btnactivateacc_001,
      'ctrl_btnactivategoal_001': instance.ctrl_btnactivategoal_001,
      'txtcholesterol_guide_001': instance.txtcholesterol_guide_001,
      'txthealthycholesterol_intro_001':
          instance.txthealthycholesterol_intro_001,
      'txttips_cholesterol_001': instance.txttips_cholesterol_001,
      'txttips_cholesterol_content_001':
          instance.txttips_cholesterol_content_001,
      'ctrl_btnagree_002': instance.ctrl_btnagree_002,
      'ctrl_btnallow_001': instance.ctrl_btnallow_001,
      'ctrl_btnancel_001': instance.ctrl_btnancel_001,
      'ctrl_btnangleright_001': instance.ctrl_btnangleright_001,
      'ctrl_btnappsanddevices_001': instance.ctrl_btnappsanddevices_001,
      'ctrl_btncancel_001': instance.ctrl_btncancel_001,
      'ctrl_btncancel_002': instance.ctrl_btncancel_002,
      'ctrl_btnconfirm_001': instance.ctrl_btnconfirm_001,
      'ctrl_btnconfirm_002': instance.ctrl_btnconfirm_002,
      'ctrl_btnconnect_001': instance.ctrl_btnconnect_001,
      'ctrl_btncontinue_001': instance.ctrl_btncontinue_001,
      'ctrl_btnctaconnect_001': instance.ctrl_btnctaconnect_001,
      'ctrl_btnchoose_product_001': instance.ctrl_btnchoose_product_001,
      'ctrl_btndisagree_001': instance.ctrl_btndisagree_001,
      'ctrl_btndisconnect_001': instance.ctrl_btndisconnect_001,
      'ctrl_btndontallow_001': instance.ctrl_btndontallow_001,
      'ctrl_btngotit_001': instance.ctrl_btngotit_001,
      'ctrl_btngotovitalitymall_001': instance.ctrl_btngotovitalitymall_001,
      'ctrl_btnhealth_001': instance.ctrl_btnhealth_001,
      'ctrl_btnhide_001': instance.ctrl_btnhide_001,
      'ctrl_btnhome_001': instance.ctrl_btnhome_001,
      'ctrl_btniunderstand_001': instance.ctrl_btniunderstand_001,
      'ctrl_btnlearnmore_001': instance.ctrl_btnlearnmore_001,
      'ctrl_btnletsgo_001': instance.ctrl_btnletsgo_001,
      'ctrl_btnlinknow_001': instance.ctrl_btnlinknow_001,
      'ctrl_btnLoggingIn_001': instance.ctrl_btnLoggingIn_001,
      'ctrl_btnlogin_001': instance.ctrl_btnlogin_001,
      'ctrl_btnloginfinger_001': instance.ctrl_btnloginfinger_001,
      'ctrl_btnLoginNewPw_001': instance.ctrl_btnLoginNewPw_001,
      'ctrl_btnloginwith_001': instance.ctrl_btnloginwith_001,
      'ctrl_btnLoginWithEmail_001': instance.ctrl_btnLoginWithEmail_001,
      'ctrl_btnok_001': instance.ctrl_btnok_001,
      'ctrl_btnokay_001': instance.ctrl_btnokay_001,
      'ctrl_btnonnect_001': instance.ctrl_btnonnect_001,
      'ctrl_btnprofile_001': instance.ctrl_btnprofile_001,
      'ctrl_btnradiooff_001': instance.ctrl_btnradiooff_001,
      'ctrl_btnrequestcode_001': instance.ctrl_btnrequestcode_001,
      'ctrl_btnrequestingcode_001': instance.ctrl_btnrequestingcode_001,
      'ctrl_btnrequestnow_001': instance.ctrl_btnrequestnow_001,
      'ctrl_btnresetpassword_001': instance.ctrl_btnresetpassword_001,
      'ctrl_btnreturnlogin_001': instance.ctrl_btnreturnlogin_001,
      'ctrl_btnseeyousoon_001': instance.ctrl_btnseeyousoon_001,
      'ctrl_btnrewards_001': instance.ctrl_btnrewards_001,
      'ctrl_btnsavedetails_001': instance.ctrl_btnsavedetails_001,
      'ctrl_btnsavepref_001': instance.ctrl_btnsavepref_001,
      'ctrl_btnsavingdetails_001': instance.ctrl_btnsavingdetails_001,
      'ctrl_btnsavingpref_001': instance.ctrl_btnsavingpref_001,
      'ctrl_btnshow_001': instance.ctrl_btnshow_001,
      'ctrl_btnskip_001': instance.ctrl_btnskip_001,
      'ctrl_btntick_001': instance.ctrl_btntick_001,
      'ctrl_btntryagain_001': instance.ctrl_btntryagain_001,
      'ctrl_btnusepass_001': instance.ctrl_btnusepass_001,
      'ctrl_btnusepasswrd_001': instance.ctrl_btnusepasswrd_001,
      'ctrl_btnvector_001': instance.ctrl_btnvector_001,
      'ctrl_btnverify_001': instance.ctrl_btnverify_001,
      'ctrl_btnverifying_001': instance.ctrl_btnverifying_001,
      'ctrl_linktxtmanuallyrefresh_001':
          instance.ctrl_linktxtmanuallyrefresh_001,
      'ctrl_lnkforgotpass_001': instance.ctrl_lnkforgotpass_001,
      'ctrl_lnkloginemail_001': instance.ctrl_lnkloginemail_001,
      'ctrl_loginwithbiometric_001': instance.ctrl_loginwithbiometric_001,
      'ctrl_lnkprivpolcy_001': instance.ctrl_lnkprivpolcy_001,
      'ctrl_lnkresendactivcode_001': instance.ctrl_lnkresendactivcode_001,
      'ctrl_login_forgotpassfeedback_btnloginnewpass_001':
          instance.ctrl_login_forgotpassfeedback_btnloginnewpass_001,
      'ctrl_physicalactivity_landing_btnactivategoal_001':
          instance.ctrl_physicalactivity_landing_btnactivategoal_001,
      'ctrl_txt_keep_email_001': instance.ctrl_txt_keep_email_001,
      'ctrl_txt_use_facial_001': instance.ctrl_txt_use_facial_001,
      'ctrl_txtaboutphysicalactivitygoal_001':
          instance.ctrl_txtaboutphysicalactivitygoal_001,
      'ctrl_txtappsanddevices_001': instance.ctrl_txtappsanddevices_001,
      'ctrl_txtgoalhistory_001': instance.ctrl_txtgoalhistory_001,
      'ctrl_txthowitworks_001': instance.ctrl_txthowitworks_001,
      'ctrl_txthowtocomplete_001': instance.ctrl_txthowtocomplete_001,
      'ctrl_txthowtoearnpoints_001': instance.ctrl_txthowtoearnpoints_001,
      'ctrl_txtlearnmore_001': instance.ctrl_txtlearnmore_001,
      'ctrl_txtmanageappsdevices_001': instance.ctrl_txtmanageappsdevices_001,
      'ctrl_txtmanagedevicesandapps_001':
          instance.ctrl_txtmanagedevicesandapps_001,
      'ctrl_txtsamsungconnected_001': instance.ctrl_txtsamsungconnected_001,
      'ctrl_txtsupport_001': instance.ctrl_txtsupport_001,
      'ctrl_btnyeslogout_001': instance.ctrl_btnyeslogout_001,
      'ctrl_btnnogoback_001': instance.ctrl_btnnogoback_001,
      'dialog_body_BiometricsDisabled': instance.dialog_body_BiometricsDisabled,
      'dialog_body_NotRecognised': instance.dialog_body_NotRecognised,
      'dialog_body_TsCsRequired': instance.dialog_body_TsCsRequired,
      'dialog_body_UnableToRegister': instance.dialog_body_UnableToRegister,
      'dialog_btn1_': instance.dialog_btn1_,
      'dialog_btn1_Ok': instance.dialog_btn1_Ok,
      'dialog_btn1_Okay': instance.dialog_btn1_Okay,
      'dialog_btn2_Cancel': instance.dialog_btn2_Cancel,
      'dialog_errfinprntdis_hgconfirm_001':
          instance.dialog_errfinprntdis_hgconfirm_001,
      'dialog_hd_BiometricsDisabled': instance.dialog_hd_BiometricsDisabled,
      'dialog_hd_fingerAuthentication': instance.dialog_hd_fingerAuthentication,
      'dialog_hd_fingerFacial': instance.dialog_hd_fingerFacial,
      'dialog_hd_touch': instance.dialog_hd_touch,
      'dialog_hd_TsCsRequired': instance.dialog_hd_TsCsRequired,
      'dialog_hd_UnableToRegister': instance.dialog_hd_UnableToRegister,
      'dialog_hg_Biometrics': instance.dialog_hg_Biometrics,
      'footnote_ManagePermissions': instance.footnote_ManagePermissions,
      'guide_Privacy': instance.guide_Privacy,
      'guide_supportQ2': instance.guide_supportQ2,
      'hd_connectApp': instance.hd_connectApp,
      'hd_doB': instance.hd_doB,
      'login_dobafter_txthdenterdob_001':
          instance.login_dobafter_txthdenterdob_001,
      'hd_emptyState_noPromotion': instance.hd_emptyState_noPromotion,
      'hd_justOneMoreStep': instance.hd_justOneMoreStep,
      'hd_TsCs': instance.hd_TsCs,
      'hg_PersonalisePreferences': instance.hg_PersonalisePreferences,
      'hg_PrivacyPolicy': instance.hg_PrivacyPolicy,
      'input_': instance.input_,
      'label_App': instance.label_App,
      'label_insurerVitalty': instance.label_insurerVitalty,
      'label_mobilePhone': instance.label_mobilePhone,
      'label_or': instance.label_or,
      'label_PWReq_01': instance.label_PWReq_01,
      'label_PWReq_02': instance.label_PWReq_02,
      'label_PWReq_03': instance.label_PWReq_03,
      'label_PWReq_04': instance.label_PWReq_04,
      'label_PWReq_05': instance.label_PWReq_05,
      'label_Step': instance.label_Step,
      'label_wearableDevice': instance.label_wearableDevice,
      'labelRight': instance.labelRight,
      'login_dialog_bdface_001': instance.login_dialog_bdface_001,
      'login_dialog_bdfingerprnt_001': instance.login_dialog_bdfingerprnt_001,
      'login_dialog_bdincorrectpwd_001':
          instance.login_dialog_bdincorrectpwd_001,
      'login_dialog_bdtouchfingerprnt_001':
          instance.login_dialog_bdtouchfingerprnt_001,
      'login_dialog_hdface_001': instance.login_dialog_hdface_001,
      'login_dialog_hdfingerprnt_001': instance.login_dialog_hdfingerprnt_001,
      'login_dialog_hdincorrectpwd_001':
          instance.login_dialog_hdincorrectpwd_001,
      'login_dialog_hdtouchfingerprnt_001':
          instance.login_dialog_hdtouchfingerprnt_001,
      'login_forgotpassfeedback_txtbodygotmail_001':
          instance.login_forgotpassfeedback_txtbodygotmail_001,
      'login_memnotactive_txthdmemnotactive_001':
          instance.login_memnotactive_txthdmemnotactive_001,
      'login_txtand_001': instance.login_txtand_001,
      'login_txtbdyaccativ_002': instance.login_txtbdyaccativ_002,
      'login_txtbdyacccancelled_001': instance.login_txtbdyacccancelled_001,
      'login_txtbylogginin_001': instance.login_txtbylogginin_001,
      'login_txtdob_error_001': instance.login_txtdob_error_001,
      'login_txthdwelcomebk_001': instance.login_txthdwelcomebk_001,
      'login_txtpassword_error_001': instance.login_txtpassword_error_001,
      'login_txtrememberme_001': instance.login_txtrememberme_001,
      'login_txtturnoffrememberme_001': instance.login_txtturnoffrememberme_001,
      'login_txtyouvegotmail_001': instance.login_txtyouvegotmail_001,
      'loginScreenTitle': instance.loginScreenTitle,
      'phd_MembershipCancelled': instance.phd_MembershipCancelled,
      'phd_supportSection': instance.phd_supportSection,
      'physicalactivity_alert_txtgoalmet_001':
          instance.physicalactivity_alert_txtgoalmet_001,
      'physicalactivity_dialogalert_txtbodydeviceapplink_001':
          instance.physicalactivity_dialogalert_txtbodydeviceapplink_001,
      'physicalactivity_dialogalert_txthddeviceapplink_001':
          instance.physicalactivity_dialogalert_txthddeviceapplink_001,
      'physicalactivity_goalhistory_txtgoalhistory_001':
          instance.physicalactivity_goalhistory_txtgoalhistory_001,
      'physicalactivity_landing_lvautotracked_001':
          instance.physicalactivity_landing_lvautotracked_001,
      'physicalactivity_landing_lvnewgoal_001':
          instance.physicalactivity_landing_lvnewgoal_001,
      'physicalactivity_landing_lvreward_001':
          instance.physicalactivity_landing_lvreward_001,
      'physicalactivity_landing_txtbodyhowtocomplete_001':
          instance.physicalactivity_landing_txtbodyhowtocomplete_001,
      'physicalactivity_landing_txtbodytip_001':
          instance.physicalactivity_landing_txtbodytip_001,
      'physicalactivity_landing_txtbodytip_002':
          instance.physicalactivity_landing_txtbodytip_002,
      'physicalactivity_landing_txtbodytip_003':
          instance.physicalactivity_landing_txtbodytip_003,
      'physicalactivity_landing_txtbodywhyimportant_001':
          instance.physicalactivity_landing_txtbodywhyimportant_001,
      'physicalactivity_landing_txtbodywhyimportant_002':
          instance.physicalactivity_landing_txtbodywhyimportant_002,
      'physicalactivity_landing_txtbodywhyimportant_003':
          instance.physicalactivity_landing_txtbodywhyimportant_003,
      'physicalactivity_landing_txtgoalname_001':
          instance.physicalactivity_landing_txtgoalname_001,
      'physicalactivity_modalactivated_txtactivatedgoal_001':
          instance.physicalactivity_modalactivated_txtactivatedgoal_001,
      'physicalactivity_modalactivated_txtactivatedgoal_002':
          instance.physicalactivity_modalactivated_txtactivatedgoal_002,
      'physicalactivity_modalactivated_txtyourfirststartgoal_001':
          instance.physicalactivity_modalactivated_txtyourfirststartgoal_001,
      'physicalactivity_modalactivated_txtyourfirststartgoal_002':
          instance.physicalactivity_modalactivated_txtyourfirststartgoal_002,
      'physicalactivity_weeklygoals_txtearnreward_001':
          instance.physicalactivity_weeklygoals_txtearnreward_001,
      'physicalactivity_weeklygoals_txtweeklygoals_001':
          instance.physicalactivity_weeklygoals_txtweeklygoals_001,
      'reg_accactivtd_txtbdyaccativ_001':
          instance.reg_accactivtd_txtbdyaccativ_001,
      'reg_accactivtd_txtbdyaccativ_002':
          instance.reg_accactivtd_txtbdyaccativ_002,
      'reg_accactivtd_txthgaccactiv_001':
          instance.reg_accactivtd_txthgaccactiv_001,
      'reg_activation_confirmpass_001': instance.reg_activation_confirmpass_001,
      'reg_activation_input_001': instance.reg_activation_input_001,
      'reg_activation_step3_txtsetlogin_001':
          instance.reg_activation_step3_txtsetlogin_001,
      'reg_activation_txtconfirmpass_001':
          instance.reg_activation_txtconfirmpass_001,
      'reg_activation_txtcreatepass_001':
          instance.reg_activation_txtcreatepass_001,
      'reg_codesentmsg_001': instance.reg_codesentmsg_001,
      'reg_notif_bdpushnotif_001': instance.reg_notif_bdpushnotif_001,
      'reg_notif_hdpushnotif_001': instance.reg_notif_hdpushnotif_001,
      'reg_privacypolicy_txtbdprivpol_001':
          instance.reg_privacypolicy_txtbdprivpol_001,
      'reg_privacypolicy_txthgprivpol_001':
          instance.reg_privacypolicy_txthgprivpol_001,
      'reg_step1_002': instance.reg_step1_002,
      'reg_step1_input_001': instance.reg_step1_input_001,
      'reg_step1_txtemailbody_001': instance.reg_step1_txtemailbody_001,
      'reg_step1_txtgetcode_001': instance.reg_step1_txtgetcode_001,
      'reg_step1_txtwelcomvit_001': instance.reg_step1_txtwelcomvit_001,
      'reg_step2_002': instance.reg_step2_002,
      'reg_step2_enteractiv_001': instance.reg_step2_enteractiv_001,
      'reg_step2_input_001': instance.reg_step2_input_001,
      'reg_step2_txtenteractiv_001': instance.reg_step2_txtenteractiv_001,
      'reg_step2_txtsecacc_001': instance.reg_step2_txtsecacc_001,
      'reg_step2_txtverifyid_001': instance.reg_step2_txtverifyid_001,
      'reg_step2_verifyid_001': instance.reg_step2_verifyid_001,
      'reg_step2txtcodesentmsg_001': instance.reg_step2txtcodesentmsg_001,
      'reg_step3_003': instance.reg_step3_003,
      'reg_step3_confirmpass_001': instance.reg_step3_confirmpass_001,
      'reg_step3_createpass_001': instance.reg_step3_createpass_001,
      'reg_step3_input_001': instance.reg_step3_input_001,
      'reg_step3_secacc_001': instance.reg_step3_secacc_001,
      'reg_step3_setlogin_001': instance.reg_step3_setlogin_001,
      'reg_step3_txtcreatepass_001': instance.reg_step3_txtcreatepass_001,
      'reg_step3_txtinput_001': instance.reg_step3_txtinput_001,
      'reg_step3_txtsecacc_001': instance.reg_step3_txtsecacc_001,
      'reg_step3_txtsetlogin_001': instance.reg_step3_txtsetlogin_001,
      'reg_step4_txtguideprivacy_001': instance.reg_step4_txtguideprivacy_001,
      'reg_step4_txtjustonestep_001': instance.reg_step4_txtjustonestep_001,
      'reg_step4_txtpersonalise_001': instance.reg_step4_txtpersonalise_001,
      'reg_termsconditions_txtbdtcs_001':
          instance.reg_termsconditions_txtbdtcs_001,
      'reg_termsconditions_txthdtcs_001':
          instance.reg_termsconditions_txthdtcs_001,
      'reg_txtstep1_001': instance.reg_txtstep1_001,
      'reg_welcome_findoutmore_001': instance.reg_welcome_findoutmore_001,
      'reg_welcome_insupolicy_001': instance.reg_welcome_insupolicy_001,
      'reg_welcome_needqualify_001': instance.reg_welcome_needqualify_001,
      'reg_welcome_policyto_001': instance.reg_welcome_policyto_001,
      'reg_welcome_txthealthliv_001': instance.reg_welcome_txthealthliv_001,
      'reg_welcome_txtinsupolicy_001': instance.reg_welcome_txtinsupolicy_001,
      'sample123': instance.sample123,
      'sectionHd_lastmembershipyear': instance.sectionHd_lastmembershipyear,
      'sectionHd_promotion': instance.sectionHd_promotion,
      'txt_footnoteVitalityprogramme_001':
          instance.txt_footnoteVitalityprogramme_001,
      'txt1spin_001': instance.txt1spin_001,
      'txtaccount_001': instance.txtaccount_001,
      'txtactivateaccountlogin_001': instance.txtactivateaccountlogin_001,
      'txtactivatecode_001': instance.txtactivatecode_001,
      'txtallowedtoread_001': instance.txtallowedtoread_001,
      'txtalreadyacc_001': instance.txtalreadyacc_001,
      'txtalreadyacc_002': instance.txtalreadyacc_002,
      'txtapp_001': instance.txtapp_001,
      'txtappcrashsettings_001': instance.txtappcrashsettings_001,
      'txtappdevices_001': instance.txtappdevices_001,
      'txtapplehealth_001': instance.txtapplehealth_001,
      'txtapplehealthh_001': instance.txtapplehealthh_001,
      'txtappsanddevicesfooter': instance.txtappsanddevicesfooter,
      'txtappver_001': instance.txtappver_001,
      'txtareyousureyouwanttodisconnect_001':
          instance.txtareyousureyouwanttodisconnect_001,
      'txtauthcodesent_001': instance.txtauthcodesent_001,
      'txtautosync_001': instance.txtautosync_001,
      'txtautosyncautomaticallythroughouttheda_001':
          instance.txtautosyncautomaticallythroughouttheda_001,
      'txtautotracked_001': instance.txtautotracked_001,
      'txtavailableagaindate_001': instance.txtavailableagaindate_001,
      'txtbdconncterr_001': instance.txtbdconncterr_001,
      'txtbdincrrdetail_001': instance.txtbdincrrdetail_001,
      'txtbdonestepaway_001': instance.txtbdonestepaway_001,
      'txtbdonestepaway_002': instance.txtbdonestepaway_002,
      'txtbdprobnerr_001': instance.txtbdprobnerr_001,
      'txtbdsomewrngerr_001': instance.txtbdsomewrngerr_001,
      'txtbdtcrequired_001': instance.txtbdtcrequired_001,
      'txtbdunknwnerr_001': instance.txtbdunknwnerr_001,
      'txtbdyacccancelled_001': instance.txtbdyacccancelled_001,
      'txtbloodglucose_001': instance.txtbloodglucose_001,
      'txtbloodpressure_001': instance.txtbloodpressure_001,
      'txtbodyapperror_001': instance.txtbodyapperror_001,
      'txtbodyconnectcompatibleapps_001':
          instance.txtbodyconnectcompatibleapps_001,
      'txtbodyconnectedmsg_001': instance.txtbodyconnectedmsg_001,
      'txtbodyforgotpass_001': instance.txtbodyforgotpass_001,
      'txtbodyforgotpass_002': instance.txtbodyforgotpass_002,
      'txtbodyhowtocomplete_001': instance.txtbodyhowtocomplete_001,
      'txtbodymassindex_001': instance.txtbodymassindex_001,
      'txtbodynohistoryavailable_001': instance.txtbodynohistoryavailable_001,
      'txtbodypromofitnesscard_001': instance.txtbodypromofitnesscard_001,
      'txtbodyresetpass_001': instance.txtbodyresetpass_001,
      'txtbodysharedata_001': instance.txtbodysharedata_001,
      'txtbodysharedata_garmin_001': instance.txtbodysharedata_garmin_001,
      'txtbtnletsgo_001': instance.txtbtnletsgo_001,
      'txttips_drink_001': instance.txttips_drink_001,
      'txttips_drink_content_001': instance.txttips_drink_content_001,
      'txttips_smoking_001': instance.txttips_smoking_001,
      'txttips_smoking_content_001': instance.txttips_smoking_content_001,
      'txttips_smoking_content_002': instance.txttips_smoking_content_002,
      'txttipstomanagecholesterol_001': instance.txttipstomanagecholesterol_001,
      'txttips_cardio_001': instance.txttips_cardio_001,
      'txttips_cardio_content_001': instance.txttips_cardio_content_001,
      'txtcardio_guide_001': instance.txtcardio_guide_001,
      'txtcardio_intro_001': instance.txtcardio_intro_001,
      'txtcardio_intro_002': instance.txtcardio_intro_002,
      'txtcardio_intro_003': instance.txtcardio_intro_003,
      'txtcardio_intro_004': instance.txtcardio_intro_004,
      'txtconsulthealthcareprovider_001':
          instance.txtconsulthealthcareprovider_001,
      'txtconsulthealthcareprovider_content_001':
          instance.txtconsulthealthcareprovider_content_001,
      'txtconsulthealthcareprovider_content_002':
          instance.txtconsulthealthcareprovider_content_002,
      'txtsmoking_guide_001': instance.txtsmoking_guide_001,
      'txtsmoking_intro_001': instance.txtsmoking_intro_001,
      'txtsmoking_intro_002': instance.txtsmoking_intro_002,
      'txtsmoking_intro_003': instance.txtsmoking_intro_003,
      'txtdrink_guide_001': instance.txtdrink_guide_001,
      'txtdrink_intro_001': instance.txtdrink_intro_001,
      'txtdrink_intro_002': instance.txtdrink_intro_002,
      'txtdrink_intro_003': instance.txtdrink_intro_003,
      'txtdrinkslowly_001': instance.txtdrinkslowly_001,
      'txtdrinkslowly_content_001': instance.txtdrinkslowly_content_001,
      'txtintensity_001': instance.txtintensity_001,
      'txtintensity_content_001': instance.txtintensity_content_001,
      'txtlimitdrinking_001': instance.txtlimitdrinking_001,
      'txtlimitdrinking_content_001': instance.txtlimitdrinking_content_001,
      'txtlimittoomuchatonce_001': instance.txtlimittoomuchatonce_001,
      'txtlimittoomuchatonce_content_001':
          instance.txtlimittoomuchatonce_content_001,
      'txtquitedate_001': instance.txtquitedate_001,
      'txtquitedate_content_001': instance.txtquitedate_content_001,
      'txt8020rule_001': instance.txt8020rule_001,
      'txt8020rule_content_001': instance.txt8020rule_content_001,
      'txt8020rule_content_002': instance.txt8020rule_content_002,
      'txtplanahead_001': instance.txtplanahead_001,
      'txtplanahead_content_001': instance.txtplanahead_content_001,
      'txtlistreasons_001': instance.txtlistreasons_001,
      'txtlistreasons_content_001': instance.txtlistreasons_content_001,
      'txtaddtime_001': instance.txtaddtime_001,
      'txtaddtime_content_001': instance.txtaddtime_content_001,
      'txtaddtime_content_002': instance.txtaddtime_content_002,
      'txtdontdrinkanddrive_001': instance.txtdontdrinkanddrive_001,
      'txtdontdrinkanddrive_content_001':
          instance.txtdontdrinkanddrive_content_001,
      'txtgathersupport_001': instance.txtgathersupport_001,
      'txtgathersupport_content_001': instance.txtgathersupport_content_001,
      'txtplantodealwithurges_001': instance.txtplantodealwithurges_001,
      'txtplantodealwithurges_content_001':
          instance.txtplantodealwithurges_content_001,
      'txtslowlyadd_001': instance.txtslowlyadd_001,
      'txtslowlyadd_content_001': instance.txtslowlyadd_content_001,
      'txtexerciseregularly_001': instance.txtexerciseregularly_001,
      'txtexmoreoften_001': instance.txtexmoreoften_001,
      'txtexmoreoften_content_001': instance.txtexmoreoften_content_001,
      'txtmedicationsupport_001': instance.txtmedicationsupport_001,
      'txtmedicationsupport_content_001':
          instance.txtmedicationsupport_content_001,
      'txtcardiovascular_001': instance.txtcardiovascular_001,
      'txtchildhood_001': instance.txtchildhood_001,
      'txtcholesterol_001': instance.txtcholesterol_001,
      'txtchoosereward': instance.txtchoosereward,
      'txtchooseyourgiftcard_001': instance.txtchooseyourgiftcard_001,
      'txtcoins': instance.txtcoins,
      'txtcolonoscopy_001': instance.txtcolonoscopy_001,
      'txtconnectaccount_001': instance.txtconnectaccount_001,
      'txtconnectapp_001': instance.txtconnectapp_001,
      'txtconnectionerror_001': instance.txtconnectionerror_001,
      'txtconnectyougarmintoearnvitalitypoint_001':
          instance.txtconnectyougarmintoearnvitalitypoint_001,
      'txtconnectyourappordevice_001': instance.txtconnectyourappordevice_001,
      'txtdentalcheckups_001': instance.txtdentalcheckups_001,
      'txtdisclaimer_001': instance.txtdisclaimer_001,
      'txtdob_001': instance.txtdob_001,
      'txtdod_001': instance.txtdod_001,
      'txtearncoinsactivation': instance.txtearncoinsactivation,
      'txtearncoinsreward_001': instance.txtearncoinsreward_001,
      'txtearngiftcardreward_001': instance.txtearngiftcardreward_001,
      'txtearnspinreward_001': instance.txtearnspinreward_001,
      'txtemailadd_001': instance.txtemailadd_001,
      'txtemailadd_001l_': instance.txtemailadd_001l_,
      'txtemailerrsup_001': instance.txtemailerrsup_001,
      'txtemailsetting_001': instance.txtemailsetting_001,
      'txtenteremail_001': instance.txtenteremail_001,
      'txtewrngmailadd_001': instance.txtewrngmailadd_001,
      'txtexpiresinxdays_001': instance.txtexpiresinxdays_001,
      'txtfastingglucose_hba1c_001': instance.txtfastingglucose_hba1c_001,
      'txtfirsttimelog_001': instance.txtfirsttimelog_001,
      'txtfirsttimelogin_001': instance.txtfirsttimelogin_001,
      'txtfitbit_001': instance.txtfitbit_001,
      'txtflu_001': instance.txtflu_001,
      'txtfobt_001': instance.txtfobt_001,
      'txtfooter_005': instance.txtfooter_005,
      'txtgarmin_001': instance.txtgarmin_001,
      'txtgastriccancer_001': instance.txtgastriccancer_001,
      'txtgetanewgoaleveryday_001': instance.txtgetanewgoaleveryday_001,
      'txtgetreadytostartmoving_001': instance.txtgetreadytostartmoving_001,
      'txtglaucoma_001': instance.txtglaucoma_001,
      'txtgoaldates_001': instance.txtgoaldates_001,
      'txtgooglefit_001': instance.txtgooglefit_001,
      'txtguideprivacy_001': instance.txtguideprivacy_001,
      'txthbA1c_001': instance.txthbA1c_001,
      'txthdapperror_001': instance.txthdapperror_001,
      'txthdconncterr_001': instance.txthdconncterr_001,
      'txthdconnectcompatibleapps_001': instance.txthdconnectcompatibleapps_001,
      'txthdforgotpass_001': instance.txthdforgotpass_001,
      'txthdincrrdetail_001': instance.txthdincrrdetail_001,
      'txthdonestepaway_001': instance.txthdonestepaway_001,
      'txthdprobnerr_001': instance.txthdprobnerr_001,
      'txthdpromofitnesscard_001': instance.txthdpromofitnesscard_001,
      'txthdsharedata_001': instance.txthdsharedata_001,
      'txthdsomewrngerr_001': instance.txthdsomewrngerr_001,
      'txthdtcrequired_001': instance.txthdtcrequired_001,
      'txthdunknwnerr_001': instance.txthdunknwnerr_001,
      'txthdwelcomebk_001': instance.txthdwelcomebk_001,
      'txthealthresults_001': instance.txthealthresults_001,
      'txthello_001': instance.txthello_001,
      'txthepatitisb_001': instance.txthepatitisb_001,
      'txthiv_001': instance.txthiv_001,
      'txthowtocomplete_001': instance.txthowtocomplete_001,
      'txthpv_001': instance.txthpv_001,
      'txtincludefooter_001': instance.txtincludefooter_001,
      'txtinsurervitality_001': instance.txtinsurervitality_001,
      'txtjustonestep_001': instance.txtjustonestep_001,
      'txtkeepemailpop_001': instance.txtkeepemailpop_001,
      'txtlastsyncdate_001': instance.txtlastsyncdate_001,
      'txtlblemailaddress_001': instance.txtlblemailaddress_001,
      'txtlblpassword_001': instance.txtlblpassword_001,
      'txtlivercancer_001': instance.txtlivercancer_001,
      'txtlungcancer_001': instance.txtlungcancer_001,
      'txtmaintenanceerror_001': instance.txtmaintenanceerror_001,
      'txtmammogram_001': instance.txtmammogram_001,
      'txtmanperms_001': instance.txtmanperms_001,
      'txtmeningococcal_001': instance.txtmeningococcal_001,
      'txtmobilephone_001': instance.txtmobilephone_001,
      'txtneedhelp_001': instance.txtneedhelp_001,
      'txtnewgoaldates_001': instance.txtnewgoaldates_001,
      'txtnotconnected_001': instance.txtnotconnected_001,
      'txtonlyqualifyingphysicalactivitygoal_001':
          instance.txtonlyqualifyingphysicalactivitygoal_001,
      'txtovariancancer_001': instance.txtovariancancer_001,
      'txtpapsmear_001': instance.txtpapsmear_001,
      'txtpassreqnotmet_001': instance.txtpassreqnotmet_001,
      'txtpassword_001': instance.txtpassword_001,
      'txtpermissions_001': instance.txtpermissions_001,
      'txtpersonalise_001': instance.txtpersonalise_001,
      'txtphysicalactivitygoal_001': instance.txtphysicalactivitygoal_001,
      'txtpleasere_enteravaliddateofbirth_001':
          instance.txtpleasere_enteravaliddateofbirth_001,
      'txtpneumococcal_001': instance.txtpneumococcal_001,
      'txtpoints_001': instance.txtpoints_001,
      'txtpointsareavailableeverymembershipyear_001':
          instance.txtpointsareavailableeverymembershipyear_001,
      'txtpointsreflectnote_001': instance.txtpointsreflectnote_001,
      'txtpointsreflecttime_001': instance.txtpointsreflecttime_001,
      'txtpointstwiceamembershipyearmonthsapart_001':
          instance.txtpointstwiceamembershipyearmonthsapart_001,
      'txtpolar_001': instance.txtpolar_001,
      'txtPTS_001': instance.txtPTS_001,
      'txtqualifyingqeventsandpoints_001':
          instance.txtqualifyingqeventsandpoints_001,
      'txtrecomcalories_001': instance.txtrecomcalories_001,
      'txtrecomheartrate_001': instance.txtrecomheartrate_001,
      'txtrecomsteps_001': instance.txtrecomsteps_001,
      'txtsamesignupdetails_001': instance.txtsamesignupdetails_001,
      'txtsamsunghealth_001': instance.txtsamsunghealth_001,
      'txtsamsungsupport_001': instance.txtsamsungsupport_001,
      'txtsaveemail_001': instance.txtsaveemail_001,
      'txtscreenings_001': instance.txtscreenings_001,
      'txtscreeningsandvaccinations_001':
          instance.txtscreeningsandvaccinations_001,
      'txtscreeningspagebodycopy_001': instance.txtscreeningspagebodycopy_001,
      'txtsharescreeninteract_001': instance.txtsharescreeninteract_001,
      'txtshareyourdatawithus_001': instance.txtshareyourdatawithus_001,
      'txtshow_001': instance.txtshow_001,
      'txtskincancer_001': instance.txtskincancer_001,
      'txtspamguide_001': instance.txtspamguide_001,
      'txtspinrewardearned_001': instance.txtspinrewardearned_001,
      'txtstrava_001': instance.txtstrava_001,
      'txtsubmitproof_001': instance.txtsubmitproof_001,
      'txtsuccesfullyconnected_001': instance.txtsuccesfullyconnected_001,
      'txtsuunto_001': instance.txtsuunto_001,
      'txtsyncyourdevicetoyourgarminaccount_001':
          instance.txtsyncyourdevicetoyourgarminaccount_001,
      'txttcprivacy_001': instance.txttcprivacy_001,
      'txttermcnd_001': instance.txttermcnd_001,
      'txttncprivacy_001': instance.txttncprivacy_001,
      'txttypesofactivities_001': instance.txttypesofactivities_001,
      'txtunabletoconnectyoursamsunghealthapp_001':
          instance.txtunabletoconnectyoursamsunghealthapp_001,
      'txturinaryprotein_001': instance.txturinaryprotein_001,
      'txtusefinger_001': instance.txtusefinger_001,
      'txtvaccinations_001': instance.txtvaccinations_001,
      'txtwearabledevice_001': instance.txtwearabledevice_001,
      'txtweight_001': instance.txtweight_001,
      'txtwhyimportant_001': instance.txtwhyimportant_001,
      'txtwhythisisimportant_001': instance.txtwhythisisimportant_001,
      'txtwithings_001': instance.txtwithings_001,
      'txtyoudiditnowspinit_001': instance.txtyoudiditnowspinit_001,
      'txtyourenowconnected_001': instance.txtyourenowconnected_001,
      'txtyourfirstgoalstartsonmonday_001':
          instance.txtyourfirstgoalstartsonmonday_001,
      'txtyourspinexpiresinxdays_001': instance.txtyourspinexpiresinxdays_001,
      'txtyourweeklypointstargetstartsondate_001':
          instance.txtyourweeklypointstargetstartsondate_001,
      'txtzoster_001': instance.txtzoster_001,
      'view_reg_txtemailbody_001': instance.view_reg_txtemailbody_001,
      'welcome_screen_1': instance.welcome_screen_1,
      'txtjustonestep_closer_001': instance.txtjustonestep_closer_001,
      'txttncprivacy_agree_001': instance.txttncprivacy_agree_001,
      'reg_step4_txtprivacyidentified_001':
          instance.reg_step4_txtprivacyidentified_001,
      'txtmakesurepassmatch_001': instance.txtmakesurepassmatch_001,
      'txtagreetnc_001': instance.txtagreetnc_001,
      'txtdisagreelogout_001': instance.txtdisagreelogout_001,
      'txtnopointsearningactivity_001': instance.txtnopointsearningactivity_001,
      'txtniceyouhaveearnedfidcoins_001':
          instance.txtniceyouhaveearnedfidcoins_001,
      'physicalactivity_modalactivated_txtyourfirststartgoal_003':
          instance.physicalactivity_modalactivated_txtyourfirststartgoal_003,
      'txt1spin_002': instance.txt1spin_002,
      'txtactivatedyouhaveveearnedfidcoins_001':
          instance.txtactivatedyouhaveveearnedfidcoins_001,
      'txtactivatedyouhaveveearnedfidcoins_002':
          instance.txtactivatedyouhaveveearnedfidcoins_002,
      'txtpointsreflectnote_002': instance.txtpointsreflectnote_002,
      'txtswiptetospin_001': instance.txtswiptetospin_001,
      'ctrl_txtwhatsonthewheel_001': instance.ctrl_txtwhatsonthewheel_001,
      'txtxcoins_001': instance.txtxcoins_001,
      'txtxgiftcards_001': instance.txtxgiftcards_001,
      'txtxgiftcards_002': instance.txtxgiftcards_002,
      'txtxgiftcards_003': instance.txtxgiftcards_003,
      'ctrl_txtgotit_001': instance.ctrl_txtgotit_001,
      'txtsubtextonreward_001': instance.txtsubtextonreward_001,
      'txtsubtextonreward_002': instance.txtsubtextonreward_002,
      'txtsubtextonreward_004': instance.txtsubtextonreward_004,
      'txtsubtextonreward_003': instance.txtsubtextonreward_003,
      'txtwonreward_001': instance.txtwonreward_001,
      'txtwonreward_002': instance.txtwonreward_002,
      'txtwonreward_003': instance.txtwonreward_003,
      'txtwonreward_004': instance.txtwonreward_004,
      'body_EmailSettings_offers': instance.body_EmailSettings_offers,
      'ctrl_btnsavingpref_your_001': instance.ctrl_btnsavingpref_your_001,
      'txtyourspinexpiresindays_001': instance.txtyourspinexpiresindays_001,
      'txt_earn_points_001': instance.txt_earn_points_001,
      'txtrewards1081': instance.txtrewards1081,
      'txtrewards1082': instance.txtrewards1082,
      'txtrewards1083': instance.txtrewards1083,
      'txtrewards1084': instance.txtrewards1084,
      'txtrewards1085': instance.txtrewards1085,
      'txtrewards1086': instance.txtrewards1086,
      'txtrewards2270': instance.txtrewards2270,
      'txtmonday': instance.txtmonday,
      'txtpts': instance.txtpts,
      'txtpending': instance.txtpending,
      'txtgoalspending': instance.txtgoalspending,
      'txtpointspending': instance.txtpointspending,
      'txtqualifyingtext': instance.txtqualifyingtext,
      'txtactivategoal': instance.txtactivategoal,
      'txtopensettings': instance.txtopensettings,
      'txtapplehealth': instance.txtapplehealth,
      'txtvitalitymallstore': instance.txtvitalitymallstore,
      'txtpermissions': instance.txtpermissions,
      'iconrewards1081': instance.iconrewards1081,
      'iconrewards1082': instance.iconrewards1082,
      'iconrewards1083': instance.iconrewards1083,
      'iconrewards1084': instance.iconrewards1084,
      'iconrewards1085': instance.iconrewards1085,
      'iconrewards1086': instance.iconrewards1086,
      'txtandor_001': instance.txtandor_001,
      'txtor_001': instance.txtor_001,
      'txtfooter_003': instance.txtfooter_003,
      'useToLogin': instance.useToLogin,
      'biometricSettings': instance.biometricSettings,
      'stepOf4': instance.stepOf4,
      'sentActivationCodeError': instance.sentActivationCodeError,
      'clearLabel': instance.clearLabel,
      'codeVerified': instance.codeVerified,
      'resendInsurerSuccess': instance.resendInsurerSuccess,
      'networkError': instance.networkError,
      'exceptionUserRegistered': instance.exceptionUserRegistered,
      'exceptionValidateInsurer': instance.exceptionValidateInsurer,
      'exceptionTimeout': instance.exceptionTimeout,
      'dobValidationFailed': instance.dobValidationFailed,
      'ctrl_navbar_tabhome': instance.ctrl_navbar_tabhome,
      'ctrl_navbar_tabrewards': instance.ctrl_navbar_tabrewards,
      'ctrl_navbar_tabhealth': instance.ctrl_navbar_tabhealth,
      'ctrl_navbar_tabprofile': instance.ctrl_navbar_tabprofile,
      'txtentercodeanddob_001': instance.txtentercodeanddob_001,
      'ctrl_btnnext_001': instance.ctrl_btnnext_001,
      'ctrl_btnrelatedfaqs_001': instance.ctrl_btnrelatedfaqs_001,
      'ctrl_btnhelpcentre_001': instance.ctrl_btnhelpcentre_001,
      'txt_footer_001': instance.txt_footer_001,
      'home_landing_txtheaderplatinum_001':
          instance.home_landing_txtheaderplatinum_001,
      'txt_homeintro_001': instance.txt_homeintro_001,
      'txtbronzestatus_001': instance.txtbronzestatus_001,
      'txtbronzestatusaia_002': instance.txtbronzestatusaia_002,
      'txtsilverstatusaia_001': instance.txtsilverstatusaia_001,
      'txtgoldstatusaia_001': instance.txtgoldstatusaia_001,
      'txtvitalitystatus_001': instance.txtvitalitystatus_001,
      'spend': instance.spend,
      'txtcoins_001': instance.txtcoins_001,
      'txt_goodmorningwithname_001': instance.txt_goodmorningwithname_001,
      'txtgoodmorning_001': instance.txtgoodmorning_001,
      'txt_goodafternoonwithname_001': instance.txt_goodafternoonwithname_001,
      'txtgoodafternoon_001': instance.txtgoodafternoon_001,
      'txt_goodeveningwithname_001': instance.txt_goodeveningwithname_001,
      'txtgoodevening_001': instance.txtgoodevening_001,
      'txtnopromotion_001': instance.txtnopromotion_001,
      'txtnopromotionbody_001': instance.txtnopromotionbody_001,
      'chip_workoutDuration': instance.chip_workoutDuration,
      'txtactivationwithrewardsuccess1':
          instance.txtactivationwithrewardsuccess1,
      'txtactivationsuccess': instance.txtactivationsuccess,
      'txtconnectionerrorbody': instance.txtconnectionerrorbody,
      'txtachieved': instance.txtachieved,
      'txt_points_001': instance.txt_points_001,
      'txtpointsreflectnoteandvitlaitystatus_002':
          instance.txtpointsreflectnoteandvitlaitystatus_002,
      'ctrl_btnchoosegiftcard_001': instance.ctrl_btnchoosegiftcard_001,
      'txtexpires_001': instance.txtexpires_001,
      'txtexpired_001_date': instance.txtexpired_001_date,
      'txtusethecodetoredeem_001': instance.txtusethecodetoredeem_001,
      'txtusethecodetoredeem_002': instance.txtusethecodetoredeem_002,
      'txtdiscountcode_001': instance.txtdiscountcode_001,
      'ctrl_btnrevealcode_001': instance.ctrl_btnrevealcode_001,
      'txtpincode_001': instance.txtpincode_001,
      'ctrl_btnviewallgiftcards_001': instance.ctrl_btnviewallgiftcards_001,
      'ctrl_btncopy_001': instance.ctrl_btncopy_001,
      'ctrl_btnvisitwebsite_001': instance.ctrl_btnvisitwebsite_001,
      'txtthisbarcodecanbeusedforinstorepurchases_001':
          instance.txtthisbarcodecanbeusedforinstorepurchases_001,
      'txtvisitthewebsitetoredeemyourgiftcard_001':
          instance.txtvisitthewebsitetoredeemyourgiftcard_001,
      'txtlinkafitnessdeviceorapp_001': instance.txtlinkafitnessdeviceorapp_001,
      'txtbodydeviceapplink_001': instance.txtbodydeviceapplink_001,
      'ctrl_txtmaybelater_001': instance.ctrl_txtmaybelater_001,
      'ctrl_txtconnectnow_001': instance.ctrl_txtconnectnow_001,
      'txtselectyourgiftcard_001': instance.txtselectyourgiftcard_001,
      'ctrl_btnselectlater_001': instance.ctrl_btnselectlater_001,
      'login_errfaceiddchange_txthgfaceidchange_001':
          instance.login_errfaceiddchange_txthgfaceidchange_001,
      'login_errfaceidchange_txtbdfaceidchange_001':
          instance.login_errfaceidchange_txtbdfaceidchange_001,
      'ctrl_btnyescontinueusingfaceid_001':
          instance.ctrl_btnyescontinueusingfaceid_001,
      'ctrl_btnnodisableusingfaceid_001':
          instance.ctrl_btnnodisableusingfaceid_001,
      'txtarchivedgiftcards_001': instance.txtarchivedgiftcards_001,
      'txtnogiftcardsarchived_001': instance.txtnogiftcardsarchived_001,
      'txtnogiftcardsarchivedcopy_001': instance.txtnogiftcardsarchivedcopy_001,
      'txtgiftcard_001': instance.txtgiftcard_001,
      'txtyourgiftcardisbeingprepared_001':
          instance.txtyourgiftcardisbeingprepared_001,
      'txtyourgiftcardisbeingpreparedcopy_001':
          instance.txtyourgiftcardisbeingpreparedcopy_001,
      'txtyourgiftcards_001': instance.txtyourgiftcards_001,
      'txtgiftcards_001': instance.txtgiftcards_001,
      'txthowtoearncoins_001': instance.txthowtoearncoins_001,
      'txtexpiressoon_001': instance.txtexpiressoon_001,
      'ctrl_btnarchive_001': instance.ctrl_btnarchive_001,
      'txtnogiftcards_001': instance.txtnogiftcards_001,
      'txtyourgiftcardswillappearhere_001':
          instance.txtyourgiftcardswillappearhere_001,
      'txtnoexpirydate_001': instance.txtnoexpirydate_001,
      'txtyouhaveXgiftcards_001': instance.txtyouhaveXgiftcards_001,
      'txtyouhaveXgiftcard_001': instance.txtyouhaveXgiftcard_001,
      'ctrl_btnsubmitresultsandproof_001':
          instance.ctrl_btnsubmitresultsandproof_001,
      'txtnsubmitproof_001': instance.txtnsubmitproof_001,
      'txtpoints_002': instance.txtpoints_002,
      'txt_fitnessevents': instance.txt_fitnessevents,
      'txtpersonalisegoalsbody_001': instance.txtpersonalisegoalsbody_001,
      'txtpersonalisegoals_001': instance.txtpersonalisegoals_001,
      'txtgetweeklygoals_001': instance.txtgetweeklygoals_001,
      'txteachmondaywellrecommendthreegoalsforyou_001':
          instance.txteachmondaywellrecommendthreegoalsforyou_001,
      'txtenjoyrewards_001': instance.txtenjoyrewards_001,
      'txtwhenyoucompleteyourgoal_001': instance.txtwhenyoucompleteyourgoal_001,
      'txttellusmoreaboutyou_001': instance.txttellusmoreaboutyou_001,
      'txtthemoreweknow_001': instance.txtthemoreweknow_001,
      'ctrl_btngetstarted_001': instance.ctrl_btngetstarted_001,
      'txt_iunderstand_001': instance.txt_iunderstand_001,
      'txtcsubmitproofofparticipation_001':
          instance.txtcsubmitproofofparticipation_001,
      'txtsupportexplanation_001': instance.txtsupportexplanation_001,
      'txteventdetails_001': instance.txteventdetails_001,
      'txtproof_001': instance.txtproof_001,
      'ctrl_btnsubmit_001': instance.ctrl_btnsubmit_001,
      'txtFitnesseventsubmitted_001': instance.txtFitnesseventsubmitted_001,
      'txtpointsmaynotreflect_immediately_001':
          instance.txtpointsmaynotreflect_immediately_001,
      'txtfooter_006': instance.txtfooter_006,
      'txthowtoearnpoints_001': instance.txthowtoearnpoints_001,
      'txtsubmitted_001': instance.txtsubmitted_001,
      'txteventscompletedwithinthelastsixmonthsqualify_001':
          instance.txteventscompletedwithinthelastsixmonthsqualify_001,
      'txtuptopointspersubmission_001': instance.txtuptopointspersubmission_001,
      'txteventtype_001': instance.txteventtype_001,
      'txt_label_resultURL_001': instance.txt_label_resultURL_001,
      'txtpleaseenteravalidurl_001': instance.txtpleaseenteravalidurl_001,
      'ctrl_btnupload_001': instance.ctrl_btnupload_001,
      'txtformatnotrecognised_pleasetryagain_001':
          instance.txtformatnotrecognised_pleasetryagain_001,
      'txtforyou_001': instance.txtforyou_001,
      'txtwhyitsimportantcopy_001': instance.txtwhyitsimportantcopy_001,
      'txtwlgonbaordingerror_001': instance.txtwlgonbaordingerror_001,
      'txtwlgonboaringerror_body_001': instance.txtwlgonboaringerror_body_001,
      'txtwlgonboaringerror_body_002': instance.txtwlgonboaringerror_body_002,
      'txtpoints_003': instance.txtpoints_003,
      'txtgetproof_001': instance.txtgetproof_001,
      'ctrl_btnsubmitproof_001': instance.ctrl_btnsubmitproof_001,
      'ctrl_btnsubmitproof_002': instance.ctrl_btnsubmitproof_002,
      'txtnote_001': instance.txtnote_001,
      'txtpointsforsubmitting_001': instance.txtpointsforsubmitting_001,
      'txtabout_001': instance.txtabout_001,
      'txtmammogramaboutcopy_001': instance.txtmammogramaboutcopy_001,
      'ctrl_btnseeless_001': instance.ctrl_btnseeless_001,
      'body_submitproofparagraph_001': instance.body_submitproofparagraph_001,
      'txttype_001': instance.txttype_001,
      'txtselecttype_001': instance.txtselecttype_001,
      'txtdateofactivity_001': instance.txtdateofactivity_001,
      'txtdateofactivitynotedate_001': instance.txtdateofactivitynotedate_001,
      'txtbody_cholestrolAbout_001': instance.txtbody_cholestrolAbout_001,
      'txtbody_cholestrolAbout_002': instance.txtbody_cholestrolAbout_002,
      'txtbody_cholestrolAbout_003': instance.txtbody_cholestrolAbout_003,
      'txtbody_cholestrolAbout_004': instance.txtbody_cholestrolAbout_004,
      'txtbody_cholestrolAbout_005': instance.txtbody_cholestrolAbout_005,
      'txtbuttondone_001': instance.txtbuttondone_001,
      'txtbuttondone_002': instance.txtbuttondone_002,
      'txtpoints_004': instance.txtpoints_004,
      'txtcomplete_001': instance.txtcomplete_001,
      'txtcompleteddate_001': instance.txtcompleteddate_001,
      'iconrewardsclip': instance.iconrewardsclip,
      'iconrewardsarrowdown': instance.iconrewardsarrowdown,
      'txthealth_001': instance.txthealth_001,
      'txtlearnmoreaboutyourhealth_001':
          instance.txtlearnmoreaboutyourhealth_001,
      'txtcompleteha_001': instance.txtcompleteha_001,
      'txtforaccurateresults_001': instance.txtforaccurateresults_001,
      'txtrelatedactivities_001': instance.txtrelatedactivities_001,
      'txthealthassessment_001': instance.txthealthassessment_001,
      'txthealthcheck_001': instance.txthealthcheck_001,
      'ctrl_textlearnmoreaboutthescience':
          instance.ctrl_textlearnmoreaboutthescience,
      'ctrl_helpcentre_001': instance.ctrl_helpcentre_001,
      'txtstateoftheartscience_001': instance.txtstateoftheartscience_001,
      'txtcompletehctoget_001': instance.txtcompletehctoget_001,
      'txttoimprove_001': instance.txttoimprove_001,
      'txtdoingwell_001': instance.txtdoingwell_001,
      'txtunknown_001': instance.txtunknown_001,
      'txthealthpriority_001': instance.txthealthpriority_001,
      'txtimproveyoursleep_001': instance.txtimproveyoursleep_001,
      'txtfuturehealth_001': instance.txtfuturehealth_001,
      'txtdiscoverthehealthiestversionofyou_001':
          instance.txtdiscoverthehealthiestversionofyou_001,
      'txtcompleted_on_date_001': instance.txtcompleted_on_date_001,
      'ctrl_btnseeallactivities_001': instance.ctrl_btnseeallactivities_001,
      'txtvitalityageandhealthresults_001':
          instance.txtvitalityageandhealthresults_001,
      'txtexploreyourresults_001': instance.txtexploreyourresults_001,
      'txtloweryourbloodpressure_001': instance.txtloweryourbloodpressure_001,
      'txtvitalityage_001': instance.txtvitalityage_001,
      'txtwhyitsimportant_001': instance.txtwhyitsimportant_001,
      'ctrl_btnselectgoal_001': instance.ctrl_btnselectgoal_001,
      'txtselectandcompletealifestylegoalrecommendedforyou_001':
          instance.txtselectandcompletealifestylegoalrecommendedforyou_001,
      'txtcheckbackonmonday_001': instance.txtcheckbackonmonday_001,
      'ctrl_btnseemore_001': instance.ctrl_btnseemore_001,
      'txtyourlifestylegoalismet_001': instance.txtyourlifestylegoalismet_001,
      'txtyourlifestylegoalismetcopy_001':
          instance.txtyourlifestylegoalismetcopy_001,
      'ctrl_btnviewsummary_001': instance.ctrl_btnviewsummary_001,
      'txtscreeningorvaccination_001': instance.txtscreeningorvaccination_001,
      'ctrl_btnsubmitanotheractivity_001':
          instance.ctrl_btnsubmitanotheractivity_001,
      'txteventdate_001': instance.txteventdate_001,
      'txtonrewardsubtext_001': instance.txtonrewardsubtext_001,
      'txtrewards_001': instance.txtrewards_001,
      'ctrl_btngotothefidelidadestore_001':
          instance.ctrl_btngotothefidelidadestore_001,
      'txtweeklyrewards_001': instance.txtweeklyrewards_001,
      'txtfooter_007': instance.txtfooter_007,
      'txtonreward_001': instance.txtonreward_001,
      'txtonrewardfidcoins_001': instance.txtonrewardfidcoins_001,
      'txtaboutthispartner_001': instance.txtaboutthispartner_001,
      'txtgiftcardpartners_001': instance.txtgiftcardpartners_001,
      'txtonrewardsubtext_002': instance.txtonrewardsubtext_002,
      'txtenjoytherewards_001': instance.txtenjoytherewards_001,
      'ctrl_btnilldothislater_001': instance.ctrl_btnilldothislater_001,
      'txthowtoearncoinsintrotext_001': instance.txthowtoearncoinsintrotext_001,
      'txthowtoearncoinsintrotext_002': instance.txthowtoearncoinsintrotext_002,
      'txthowtoearncoinsintrotext_003': instance.txthowtoearncoinsintrotext_003,
      'textcoins_002': instance.textcoins_002,
      'txtselectimage_001': instance.txtselectimage_001,
      'txt_takeaphoto_001': instance.txt_takeaphoto_001,
      'ctrl_txtselectfromgallery_001': instance.ctrl_txtselectfromgallery_001,
      'txtexercise_guide_001': instance.txtexercise_guide_001,
      'txtexercise_intro_001': instance.txtexercise_intro_001,
      'txtexercise_intro_002': instance.txtexercise_intro_002,
      'txtexercise_intro_003': instance.txtexercise_intro_003,
      'txtexercise_intro_004': instance.txtexercise_intro_004,
      'txtexercise_intro_005': instance.txtexercise_intro_005,
      'txtexercise_intro_006': instance.txtexercise_intro_006,
      'txttips_excercise_001': instance.txttips_excercise_001,
      'txttips_excercise_content_001': instance.txttips_excercise_content_001,
      'txtrecommendations_001': instance.txtrecommendations_001,
      'txtrecommendations_content_001': instance.txtrecommendations_content_001,
      'txtwhatcounts_001': instance.txtwhatcounts_001,
      'txtwhatcounts_content_001': instance.txtwhatcounts_content_001,
      'txtfitmorein_001': instance.txtfitmorein_001,
      'txtfitmorein_content_001': instance.txtfitmorein_content_001,
      'txtavoidboredom_001': instance.txtavoidboredom_001,
      'txtavoidboredom_content_001': instance.txtavoidboredom_content_001,
      'txtexcsafely_001': instance.txtexcsafely_001,
      'txtexcsafely_content_001': instance.txtexcsafely_content_001,
      'txtexcsafely_content_002': instance.txtexcsafely_content_002,
      'txtconsultwithyourdoctor_001': instance.txtconsultwithyourdoctor_001,
      'txtconsultwithyourdoctor_content_001':
          instance.txtconsultwithyourdoctor_content_001,
      'txtconsultwithyourdoctor_content_002':
          instance.txtconsultwithyourdoctor_content_002,
      'txtmentalwellbeing_guide_001': instance.txtmentalwellbeing_guide_001,
      'txtmentalwellbeing_intro_001': instance.txtmentalwellbeing_intro_001,
      'txtmentalwellbeing_intro_002': instance.txtmentalwellbeing_intro_002,
      'txttips_mentalwellbeing_001': instance.txttips_mentalwellbeing_001,
      'txttips_mentalwellbeing_content_001':
          instance.txttips_mentalwellbeing_content_001,
      'txtprioritizesleep_001': instance.txtprioritizesleep_001,
      'txtprioritizesleep_content_001': instance.txtprioritizesleep_content_001,
      'txtprioritizesleep_content_002': instance.txtprioritizesleep_content_002,
      'txtprioritizesleep_content_003': instance.txtprioritizesleep_content_003,
      'txtconnectwithothers_001': instance.txtconnectwithothers_001,
      'txtconnectwithothers_content_001':
          instance.txtconnectwithothers_content_001,
      'txthealthymindset_001': instance.txthealthymindset_001,
      'txthealthymindset_content_001': instance.txthealthymindset_content_001,
      'txtcalmingtechniques_001': instance.txtcalmingtechniques_001,
      'txtcalmingtechniques_content_001':
          instance.txtcalmingtechniques_content_001,
      'txtresiliencetechniques_001': instance.txtresiliencetechniques_001,
      'txtresiliencetechniques_content_001':
          instance.txtresiliencetechniques_content_001,
      'txtdiabetes_guide_001': instance.txtdiabetes_guide_001,
      'txtdiabetes_intro_001': instance.txtdiabetes_intro_001,
      'txtdiabetes_intro_002': instance.txtdiabetes_intro_002,
      'txtdiabetes_intro_003': instance.txtdiabetes_intro_003,
      'txttips_diabetes_001': instance.txttips_diabetes_001,
      'txttips_diabetes_content_001': instance.txttips_diabetes_content_001,
      'txtcheckbloodsugarreg_001': instance.txtcheckbloodsugarreg_001,
      'txtcheckbloodsugarreg_content_001':
          instance.txtcheckbloodsugarreg_content_001,
      'txteatwell_001': instance.txteatwell_001,
      'txteatwell_content_001': instance.txteatwell_content_001,
      'txteatwell_content_002': instance.txteatwell_content_002,
      'txteatwell_content_003': instance.txteatwell_content_003,
      'txteatwell_content_004': instance.txteatwell_content_004,
      'txteatwell_content_005': instance.txteatwell_content_005,
      'txteatwell_content_006': instance.txteatwell_content_006,
      'txteatwell_content_007': instance.txteatwell_content_007,
      'txttakemedasdirected_001': instance.txttakemedasdirected_001,
      'txttakemedasdirected_content_001':
          instance.txttakemedasdirected_content_001,
      'txtbeactive_001': instance.txtbeactive_001,
      'txtbeactive_content001': instance.txtbeactive_content001,
      'txtbeactive_content002': instance.txtbeactive_content002,
      'txtwatchyourweight_001': instance.txtwatchyourweight_001,
      'txtwatchyourweight_content_001': instance.txtwatchyourweight_content_001,
      'txtwatchyourweight_content_002': instance.txtwatchyourweight_content_002,
      'txtwatchyourweight_content_003': instance.txtwatchyourweight_content_003,
      'txtwatchyourweight_content_004': instance.txtwatchyourweight_content_004,
      'txtwatchyourweight_content_005': instance.txtwatchyourweight_content_005,
      'txtwatchyourweight_content_006': instance.txtwatchyourweight_content_006,
      'txtwatchyourweight_content_007': instance.txtwatchyourweight_content_007,
      'txtwatchyourweight_content_008': instance.txtwatchyourweight_content_008,
      'txtwatchyourweight_content_009': instance.txtwatchyourweight_content_009,
      'txtwatchyourweight_content_010': instance.txtwatchyourweight_content_010,
      'txtwatchyourweight_content_011': instance.txtwatchyourweight_content_011,
      'txtreceivecare_001': instance.txtreceivecare_001,
      'txtreceivecare_content_001': instance.txtreceivecare_content_001,
      'txtplantbased_guide_001': instance.txtplantbased_guide_001,
      'txtplantbased_intro_001': instance.txtplantbased_intro_001,
      'txtplantbased_intro_002': instance.txtplantbased_intro_002,
      'txttips_plantbased_001': instance.txttips_plantbased_001,
      'txttips_plantbased_content_001': instance.txttips_plantbased_content_001,
      'txthowmanysohuldIeat_001': instance.txthowmanysohuldIeat_001,
      'txthowmanysohuldIeat_content_001':
          instance.txthowmanysohuldIeat_content_001,
      'txthowmanysohuldIeat_content_002':
          instance.txthowmanysohuldIeat_content_002,
      'txthowmanysohuldIeat_content_003':
          instance.txthowmanysohuldIeat_content_003,
      'txtchooserawandwhole_001': instance.txtchooserawandwhole_001,
      'txtchooserawandwhole_content_001':
          instance.txtchooserawandwhole_content_001,
      'txtchooserawandwhole_content_002':
          instance.txtchooserawandwhole_content_002,
      'txteatmorecolour_001': instance.txteatmorecolour_001,
      'txteatmorecolour_content_001': instance.txteatmorecolour_content_001,
      'txteatmorecolour_content_002': instance.txteatmorecolour_content_002,
      'txtsneakbeansandpeas_001': instance.txtsneakbeansandpeas_001,
      'txtsneakbeansandpeas_content_001':
          instance.txtsneakbeansandpeas_content_001,
      'txtsneakbeansandpeas_content_002':
          instance.txtsneakbeansandpeas_content_002,
      'txtsneakbeansandpeas_content_003':
          instance.txtsneakbeansandpeas_content_003,
      'txtoptforwholegrains_001': instance.txtoptforwholegrains_001,
      'txtoptforwholegrains_content_001':
          instance.txtoptforwholegrains_content_001,
      'txtoptforwholegrains_content_002':
          instance.txtoptforwholegrains_content_002,
      'txtoptforwholegrains_content_003':
          instance.txtoptforwholegrains_content_003,
      'txthealthyeating_guide_001': instance.txthealthyeating_guide_001,
      'txthealthyeating_intro_001': instance.txthealthyeating_intro_001,
      'txthealthyeating_intro_002': instance.txthealthyeating_intro_002,
      'txttips_eatinghealthy_001': instance.txttips_eatinghealthy_001,
      'txttips_eatinghealthy_content_001':
          instance.txttips_eatinghealthy_content_001,
      'txtsweetcravings_001': instance.txtsweetcravings_001,
      'txtsweetcravings_content_001': instance.txtsweetcravings_content_001,
      'txtsweetcravings_content_002': instance.txtsweetcravings_content_002,
      'txtsugarswaps_001': instance.txtsugarswaps_001,
      'txtsugarswaps_content_001': instance.txtsugarswaps_content_001,
      'txtherbsandspices_001': instance.txtherbsandspices_001,
      'txtherbsandspices_content_001': instance.txtherbsandspices_content_001,
      'txtherbsandspices_content_002': instance.txtherbsandspices_content_002,
      'txtflavourcombos_001': instance.txtflavourcombos_001,
      'txtflavourcombos_content_001': instance.txtflavourcombos_content_001,
      'txtprotein_guide_001': instance.txtprotein_guide_001,
      'txtprotein_intro_001': instance.txtprotein_intro_001,
      'txtprotein_intro_002': instance.txtprotein_intro_002,
      'txtfreshorfrozeninsteadofprocessed_001':
          instance.txtfreshorfrozeninsteadofprocessed_001,
      'txtfreshorfrozeninsteadofprocessed_content_001':
          instance.txtfreshorfrozeninsteadofprocessed_content_001,
      'txtfreshorfrozeninsteadofprocessed_content_002':
          instance.txtfreshorfrozeninsteadofprocessed_content_002,
      'txtchooseredleancuts_001': instance.txtchooseredleancuts_001,
      'txtchooseredleancuts_content_001':
          instance.txtchooseredleancuts_content_001,
      'txtchooseredleancuts_content_002':
          instance.txtchooseredleancuts_content_002,
      'txtchooseredleancuts_content_003':
          instance.txtchooseredleancuts_content_003,
      'txthealthierprep_001': instance.txthealthierprep_001,
      'txthealthierprep_content_001': instance.txthealthierprep_content_001,
      'txtvarietyproteinsources_001': instance.txtvarietyproteinsources_001,
      'txtvarietyproteinsources_content_001':
          instance.txtvarietyproteinsources_content_001,
      'txtvarietyproteinsources_content_002':
          instance.txtvarietyproteinsources_content_002,
      'txtvarietyproteinsources_content_003':
          instance.txtvarietyproteinsources_content_003,
      'txtmediterraneanstyle_001': instance.txtmediterraneanstyle_001,
      'txtmediterraneanstyle_content_001':
          instance.txtmediterraneanstyle_content_001,
      'txtbloodpressure_guide_001': instance.txtbloodpressure_guide_001,
      'txtbloodpressure_intro_001': instance.txtbloodpressure_intro_001,
      'txtbloodpressure_intro_002': instance.txtbloodpressure_intro_002,
      'txtbloodpressure_intro_003': instance.txtbloodpressure_intro_003,
      'txtbloodpressure_intro_004': instance.txtbloodpressure_intro_004,
      'txtbloodpressure_intro_005': instance.txtbloodpressure_intro_005,
      'txtbloodpressure_intro_006': instance.txtbloodpressure_intro_006,
      'txtbloodpressure_intro_007': instance.txtbloodpressure_intro_007,
      'txttips_bloodpressure_001': instance.txttips_bloodpressure_001,
      'txttips_bloodpressure_content_001':
          instance.txttips_bloodpressure_content_001,
      'txttips_bloodpressure_content_002':
          instance.txttips_bloodpressure_content_002,
      'txttakeyourmedications_001': instance.txttakeyourmedications_001,
      'txttakeyourmedications_content_001':
          instance.txttakeyourmedications_content_001,
      'txttakeyourmedications_content_002':
          instance.txttakeyourmedications_content_002,
      'txttakeyourmedications_content_003':
          instance.txttakeyourmedications_content_003,
      'txtmindfulofacl_001': instance.txtmindfulofacl_001,
      'txtmindfulofacl_content_001': instance.txtmindfulofacl_content_001,
      'txtmindfulofacl_content_002': instance.txtmindfulofacl_content_002,
      'txtmindfulofacl_content_003': instance.txtmindfulofacl_content_003,
      'txtmindfulofacl_content_004': instance.txtmindfulofacl_content_004,
      'txtmindfulofacl_content_005': instance.txtmindfulofacl_content_005,
      'txtmindfulofacl_content_006': instance.txtmindfulofacl_content_006,
      'txtmindfulofacl_content_007': instance.txtmindfulofacl_content_007,
      'txtmindfulofacl_content_008': instance.txtmindfulofacl_content_008,
      'txtmindfulofacl_content_009': instance.txtmindfulofacl_content_009,
      'txtmindfulofacl_content_010': instance.txtmindfulofacl_content_010,
      'txtmindfulofacl_content_011': instance.txtmindfulofacl_content_011,
      'txtquitsmoking_001': instance.txtquitsmoking_001,
      'txtquitsmoking_content_001': instance.txtquitsmoking_content_001,
      'txtquitsmoking_content_002': instance.txtquitsmoking_content_002,
      'txtquitsmoking_content_003': instance.txtquitsmoking_content_003,
      'txtquitsmoking_content_004': instance.txtquitsmoking_content_004,
      'txtquitsmoking_content_005': instance.txtquitsmoking_content_005,
      'txtquitsmoking_content_006': instance.txtquitsmoking_content_006,
      'txttakemedicationswhenprescribed_001':
          instance.txttakemedicationswhenprescribed_001,
      'txttakemedicationswhenprescribed_content_001':
          instance.txttakemedicationswhenprescribed_content_001,
      'txttakemedicationswhenprescribed_content_002':
          instance.txttakemedicationswhenprescribed_content_002,
      'txteathearthealthy_001': instance.txteathearthealthy_001,
      'txteathearthealthy_content_001': instance.txteathearthealthy_content_001,
      'txteathearthealthy_content_002': instance.txteathearthealthy_content_002,
      'txtspendyourvitalitycoins_001': instance.txtspendyourvitalitycoins_001,
      'txtspendyourmedals_001': instance.txtspendyourmedals_001,
      'txtspendyourcoins_001': instance.txtspendyourcoins_001,
      'ctrl_btnfinish_001': instance.ctrl_btnfinish_001,
      'ctrl_btndone_001': instance.ctrl_btndone_001,
      'txtcheckinruleguide_001': instance.txtcheckinruleguide_001,
      'txtyesterday_001': instance.txtyesterday_001,
      'txttoday_001': instance.txttoday_001,
      'txtcheckin_001': instance.txtcheckin_001,
      'txtcheckingin_001': instance.txtcheckingin_001,
      'txtweekday_001': instance.txtweekday_001,
      'txtweekday_002': instance.txtweekday_002,
      'txtweekday_003': instance.txtweekday_003,
      'txtweekday_004': instance.txtweekday_004,
      'txtweekday_005': instance.txtweekday_005,
      'txtweekday_006': instance.txtweekday_006,
      'txtweekday_007': instance.txtweekday_007,
      'txtwheretogo': instance.txtwheretogo,
      'txtactivitycontentnote': instance.txtactivitycontentnote,
      'txtcurrentperiod_001': instance.txtcurrentperiod_001,
      'txttotalpointsearned_001': instance.txttotalpointsearned_001,
      'txtdaysleft_001': instance.txtdaysleft_001,
      'txtstatusdetails_001': instance.txtstatusdetails_001,
      'txtpointshistory_001': instance.txtpointshistory_001,
      'ctrl_btnseeall_001': instance.ctrl_btnseeall_001,
      'txttbronze_001': instance.txttbronze_001,
      'txtsilver_001': instance.txtsilver_001,
      'txtgold_001': instance.txtgold_001,
      'txttplatinum_001': instance.txttplatinum_001,
      'txttblue_001': instance.txttblue_001,
      'txtsettings_001': instance.txtsettings_001,
      'section_manage_001': instance.section_manage_001,
      'txtappsanddevices_001': instance.txtappsanddevices_001,
      'txtloginpreferences_001': instance.txtloginpreferences_001,
      'txtemailcommunication_001': instance.txtemailcommunication_001,
      'txtnotifications_001': instance.txtnotifications_001,
      'txtdatasharing_001': instance.txtdatasharing_001,
      'section_general_001': instance.section_general_001,
      'txtaccountdetails_001': instance.txtaccountdetails_001,
      'txthelpcentre_001': instance.txthelpcentre_001,
      'txtprivacypolicy_001': instance.txtprivacypolicy_001,
      'txttermsandconditions_001': instance.txttermsandconditions_001,
      'txtlegal1_001': instance.txtlegal1_001,
      'txtlegal2_001': instance.txtlegal2_001,
      'ctrl_logout_001': instance.ctrl_logout_001,
      'dialog_body_areyouwanttologout_001':
          instance.dialog_body_areyouwanttologout_001,
      'dialog_btn1_yeslogout_001': instance.dialog_btn1_yeslogout_001,
      'txtname_001': instance.txtname_001,
      'txtmobile_001': instance.txtmobile_001,
      'txtgender_001': instance.txtgender_001,
      'txtdateofbirth_001': instance.txtdateofbirth_001,
      'txtmembershipnumber_001': instance.txtmembershipnumber_001,
      'txtcustomernumber_001': instance.txtcustomernumber_001,
      'txtmembershipstartdate_001': instance.txtmembershipstartdate_001,
      'txtmembershiptype_001': instance.txtmembershiptype_001,
      'txtmembershipcancellation_001': instance.txtmembershipcancellation_001,
      'txtthisinformationfrominsurer_001':
          instance.txtthisinformationfrominsurer_001,
      'txtpointsandstatus_001': instance.txtpointsandstatus_001,
      'ctrl_receiveemailsabout_001': instance.ctrl_receiveemailsabout_001,
      'label_commsemaiaddress_001': instance.label_commsemaiaddress_001,
      'ctrl_btnsavechanges_001': instance.ctrl_btnsavechanges_001,
      'alert_updatedemailaddress_001': instance.alert_updatedemailaddress_001,
      'dialog_hd_disablecommunications_001':
          instance.dialog_hd_disablecommunications_001,
      'dialog_body_disable_email_communication_001':
          instance.dialog_body_disable_email_communication_001,
      'dialog_btn2_cancel_001': instance.dialog_btn2_cancel_001,
      'dialog_btn1_continue_001': instance.dialog_btn1_continue_001,
      'supportText_changeyourcurrentemail_001':
          instance.supportText_changeyourcurrentemail_001,
      'dialog_hd_changeemail_001': instance.dialog_hd_changeemail_001,
      'ctrl_txt_sharescreeninteractions_001':
          instance.ctrl_txt_sharescreeninteractions_001,
      'ctrl_txt_shareappcrashes_001': instance.ctrl_txt_shareappcrashes_001,
      'txt_pushnotification_contentnote_001':
          instance.txt_pushnotification_contentnote_001,
      'txt_opensettings_001': instance.txt_opensettings_001,
      'txtyoucancompletethisprofilenow_001':
          instance.txtyoucancompletethisprofilenow_001,
      'txttherewillbeafewquestionsaboutyou_001':
          instance.txttherewillbeafewquestionsaboutyou_001,
      'ctrl_btncompletelater_001': instance.ctrl_btncompletelater_001,
      'ctrl_btncompletenow_001': instance.ctrl_btncompletenow_001,
      'txtwelldoneyouscored_001': instance.txtwelldoneyouscored_001,
      'txtwelldoneyouscoredcopy_001': instance.txtwelldoneyouscoredcopy_001,
      'ctrl_btnviewresults_001': instance.ctrl_btnviewresults_001,
      'txtbetterlucknexttime_001': instance.txtbetterlucknexttime_001,
      'txtbetterlucknexttimecopy_001': instance.txtbetterlucknexttimecopy_001,
      'Visit_BiW_mall_001': instance.Visit_BiW_mall_001,
      'ctrl_btngotocygmall_001': instance.ctrl_btngotocygmall_001,
      'txt15creditshavebeendepositedinyourcygmall_001':
          instance.txt15creditshavebeendepositedinyourcygmall_001,
      'txtcreditcopy_001': instance.txtcreditcopy_001,
      'ctrl_btnyourgoalhistory_001': instance.ctrl_btnyourgoalhistory_001,
      'txtmanualcheck_in_001': instance.txtmanualcheck_in_001,
      'txtyourprofilesummary_001': instance.txtyourprofilesummary_001,
      'txtyouranswer_001': instance.txtyouranswer_001,
      'txtguidance_001': instance.txtguidance_001,
      'txtnewgoaleverymonday_001': instance.txtnewgoaleverymonday_001,
      'txtscorehigherthan80_001': instance.txtscorehigherthan80_001,
      'txtwhenwouldyouliketocompletethisgoal_001':
          instance.txtwhenwouldyouliketocompletethisgoal_001,
      'txtifyouchooselateryouwillfinditingoalsonhome_001':
          instance.txtifyouchooselateryouwillfinditingoalsonhome_001,
      'txtspendyourcredit_001': instance.txtspendyourcredit_001,
      'txt_reward_2048': instance.txt_reward_2048,
      'txt_reward_2050': instance.txt_reward_2050,
      'txtnsubmitproof_002': instance.txtnsubmitproof_002,
      'txtpoints_006': instance.txtpoints_006,
      'txtpoints_005': instance.txtpoints_005,
      'txtuploading_001': instance.txtuploading_001,
      'txtoptional_001': instance.txtoptional_001,
      'txtselectPDF_001': instance.txtselectPDF_001,
      'txtproofnote_003': instance.txtproofnote_003,
      'txtproofnote_001': instance.txtproofnote_001,
      'txtproofnote_002': instance.txtproofnote_002,
      'how_to_earn_gift_cards_001': instance.how_to_earn_gift_cards_001,
      'how_to_earn_gift_cards_details_001':
          instance.how_to_earn_gift_cards_details_001,
      'txthhmmss_001': instance.txthhmmss_001,
      'txtselect_001': instance.txtselect_001,
      'txt_fitness_disclaimer_001': instance.txt_fitness_disclaimer_001,
      'txtfirstdaynextdaycheckingoal_001':
          instance.txtfirstdaynextdaycheckingoal_001,
      'txtcheckintomorrow_001': instance.txtcheckintomorrow_001,
      'txtcheckintomorrowdialog_001': instance.txtcheckintomorrowdialog_001,
      'txtyourquizsummary_001': instance.txtyourquizsummary_001,
      'login_dialog_txtbdincorrectpwd_002':
          instance.login_dialog_txtbdincorrectpwd_002,
      'txtnonsmokerdeclaration_001': instance.txtnonsmokerdeclaration_001,
      'txtnonsmokerstatus_001': instance.txtnonsmokerstatus_001,
      'txtnonsmokerstatus_002': instance.txtnonsmokerstatus_002,
      'txttime_001': instance.txttime_001,
      'txtguide_participatingpartners_001':
          instance.txtguide_participatingpartners_001,
      'txtpointsareavailableeverymembershipyear_002':
          instance.txtpointsareavailableeverymembershipyear_002,
      'ctrl_btndeclarenonsmokingstatus_001':
          instance.ctrl_btndeclarenonsmokingstatus_001,
      'txtguide_participatingpartners_002':
          instance.txtguide_participatingpartners_002,
      'txtparticpatingpartners_001': instance.txtparticpatingpartners_001,
      'txtnonsmokerstatusdeclarationcomplete_001':
          instance.txtnonsmokerstatusdeclarationcomplete_001,
      'txtstatus_001': instance.txtstatus_001,
      'txtsmokingdeclarationcomplete_001':
          instance.txtsmokingdeclarationcomplete_001,
      'nsd_declaration_txtsmoker_001': instance.nsd_declaration_txtsmoker_001,
      'nsd_declaration_txtnonsmoker_001':
          instance.nsd_declaration_txtnonsmoker_001,
      'nsd_modalcompleted_txtstatusconfirmed_002':
          instance.nsd_modalcompleted_txtstatusconfirmed_002,
      'nsd_confirm_smoker_001': instance.nsd_confirm_smoker_001,
      'nsd_confirm_smoker_002': instance.nsd_confirm_smoker_002,
      'nsd_confirm_non_smoker_001': instance.nsd_confirm_non_smoker_001,
      'nsd_confirm_non_smoker_002': instance.nsd_confirm_non_smoker_002,
      'txtkmtokm_001': instance.txtkmtokm_001,
      'txtkmandmore_001': instance.txtkmandmore_001,
      'txtxpoints_001': instance.txtxpoints_001,
      'txtpointsfordistance_001': instance.txtpointsfordistance_001,
      'txtautomaticallytracked_001': instance.txtautomaticallytracked_001,
      'txt_how_to_earn_Points_body_001':
          instance.txt_how_to_earn_Points_body_001,
      'txtheartratenote_001': instance.txtheartratenote_001,
      'txt_heart_rate_condition_001': instance.txt_heart_rate_condition_001,
      'txt_potential_points_001': instance.txt_potential_points_001,
      'txtgymworkout_001': instance.txtgymworkout_001,
      'txtgymnote_001': instance.txtgymnote_001,
      'txt_steps_range_001': instance.txt_steps_range_001,
      'txt_steps_range_002': instance.txt_steps_range_002,
      'txt_steps_range_003': instance.txt_steps_range_003,
      'txt_register_sport_classes_001': instance.txt_register_sport_classes_001,
      'txt_sport_classes_details_001': instance.txt_sport_classes_details_001,
      'txt_min_at_kCal_001': instance.txt_min_at_kCal_001,
      'txt_min_and_more_at_kCal_001': instance.txt_min_and_more_at_kCal_001,
      'txtmindistanceofxkmatanaveragespeedof_001':
          instance.txtmindistanceofxkmatanaveragespeedof_001,
      'txvalidtimeformat_0001': instance.txvalidtimeformat_0001,
      'txtgetverifiedprooffromhealthcareprovider_001':
          instance.txtgetverifiedprooffromhealthcareprovider_001,
      'txtguide_participatingpartners_003':
          instance.txtguide_participatingpartners_003,
      'txtrelatedfaqs_001': instance.txtrelatedfaqs_001,
      'txtpointsforsubmitting_002': instance.txtpointsforsubmitting_002,
      'txtpointsforresultsinhealthyrange_001':
          instance.txtpointsforresultsinhealthyrange_001,
      'txtsubmittedearnpoints_001': instance.txtsubmittedearnpoints_001,
      'txthealthyrange': instance.txthealthyrange,
      'txtyoucansubmiteverysixmonths_001':
          instance.txtyoucansubmiteverysixmonths_001,
      'txtsubmitresultsandform_001': instance.txtsubmitresultsandform_001,
      'txtsubmitresultsandproofexplanationcopy_001':
          instance.txtsubmitresultsandproofexplanationcopy_001,
      'txthealthcheckdate_001': instance.txthealthcheckdate_001,
      'txtyouractivitydatemusthaveoccurredbetweendate_001':
          instance.txtyouractivitydatemusthaveoccurredbetweendate_001,
      'txtsubmitresultsandproofexplanationcopy_002':
          instance.txtsubmitresultsandproofexplanationcopy_002,
      'guide_': instance.guide_,
      'txtproofnote_004': instance.txtproofnote_004,
      'txtproofsubmittedsuccessfully_001':
          instance.txtproofsubmittedsuccessfully_001,
      'txtpointsmaynotreflectimmediately_001':
          instance.txtpointsmaynotreflectimmediately_001,
      'txtpointsawardedtopreviousmembershipyear_001':
          instance.txtpointsawardedtopreviousmembershipyear_001,
      'ctrl_btnsubmitascreeningorvaccination_001':
          instance.ctrl_btnsubmitascreeningorvaccination_001,
      'txtyoucansubmiteverysixmonths_002':
          instance.txtyoucansubmiteverysixmonths_002,
      'txtlastmembershipyear_001': instance.txtlastmembershipyear_001,
      'ctrl_txtlearnmoreaboutbodymassindex_001':
          instance.ctrl_txtlearnmoreaboutbodymassindex_001,
      'txtrange1250_001': instance.txtrange1250_001,
      'txtresubmitresultsandform_001': instance.txtresubmitresultsandform_001,
      'body_smokesResultSummary': instance.body_smokesResultSummary,
      'crd_statusMarker_outOfRange_001':
          instance.crd_statusMarker_outOfRange_001,
      'crd_title_001': instance.crd_title_001,
      'crd_subtext_001': instance.crd_subtext_001,
      'crd_subtext_002': instance.crd_subtext_002,
      'txtreferences_001': instance.txtreferences_001,
      'crd_title_002': instance.crd_title_002,
      'crd_subtext_003': instance.crd_subtext_003,
      'crd_statusMarker_outOfRange_002':
          instance.crd_statusMarker_outOfRange_002,
      'ctrl_txthistory_001': instance.ctrl_txthistory_001,
      'ctrl_txtquickguide_001': instance.ctrl_txtquickguide_001,
      'ctrl_txtlearnmoreaboutcholesterol_001':
          instance.ctrl_txtlearnmoreaboutcholesterol_001,
      'txtsectionHd_001': instance.txtsectionHd_001,
      'ctrl_txt_001': instance.ctrl_txt_001,
      'txtfooterhealth': instance.txtfooterhealth,
      'ctrl_btnshareyoursmokingstatus_001':
          instance.ctrl_btnshareyoursmokingstatus_001,
      'txtcurrentlysmokes_001': instance.txtcurrentlysmokes_001,
      'txtnonsmoker_001': instance.txtnonsmoker_001,
      'txtproofnote_005': instance.txtproofnote_005,
      'txtpoints_007': instance.txtpoints_007,
      'txtpoints_008': instance.txtpoints_008,
      'sport_class_url': instance.sport_class_url,
      'txt_fingerprint_facial': instance.txt_fingerprint_facial,
      'txt_goals_001': instance.txt_goals_001,
      'txthealthcheck_002': instance.txthealthcheck_002,
      'txtactivities_001': instance.txtactivities_001,
      'txtcheckinruleguide_002': instance.txtcheckinruleguide_002,
      'txtmaximumstatuspointsearnedfromactivities_001':
          instance.txtmaximumstatuspointsearnedfromactivities_001,
      'txtkeepearningpointstomeetyourgoals_001':
          instance.txtkeepearningpointstomeetyourgoals_001,
      'ctrl_txtpointshistory_001': instance.ctrl_txtpointshistory_001,
      'txtfootnoteVitalityprogramme_001':
          instance.txtfootnoteVitalityprogramme_001,
      'txt_sv_disclaimer_001': instance.txt_sv_disclaimer_001,
      'txtgiftcardsuccessfullyarchived_001':
          instance.txtgiftcardsuccessfullyarchived_001,
      'txtgiftcardsuccessfullyarchived_002':
          instance.txtgiftcardsuccessfullyarchived_002,
      'txtnohistoryavailable_001': instance.txtnohistoryavailable_001,
      'txtthepointsyouearnforcompletingactivitieswillposthere_001':
          instance.txtthepointsyouearnforcompletingactivitieswillposthere_001,
      'ctrl_btngotoyouractivities_001': instance.ctrl_btngotoyouractivities_001,
      'txtxpointsmonth_002': instance.txtxpointsmonth_002,
      'txtnohistoryavailableyet_001': instance.txtnohistoryavailableyet_001,
      'txt30million_001': instance.txt30million_001,
      'txtvitalitymembersworldwide_001':
          instance.txtvitalitymembersworldwide_001,
      'txtvitalitymemberslive_001': instance.txtvitalitymemberslive_001,
      'txtlongerhealthierlives_001': instance.txtlongerhealthierlives_001,
      'txttheworldslargestsciencebased_001':
          instance.txttheworldslargestsciencebased_001,
      'txtonboardingearnpoints_001': instance.txtonboardingearnpoints_001,
      'txtonboardingvitalitystatus_001':
          instance.txtonboardingvitalitystatus_001,
      'txtonboardingdiscountonpremium_001':
          instance.txtonboardingdiscountonpremium_001,
      'txtonboardinggoals_001': instance.txtonboardinggoals_001,
      'txtonboardingrewardsheader_002': instance.txtonboardingrewardsheader_002,
      'txtonboardingearnpoints_002': instance.txtonboardingearnpoints_002,
      'txtonboardingvitalitystatus_002':
          instance.txtonboardingvitalitystatus_002,
      'txtonboardingdiscountonpremium_002':
          instance.txtonboardingdiscountonpremium_002,
      'txtonboardinggoals_002': instance.txtonboardinggoals_002,
      'txtonboardingrewardscopy_002': instance.txtonboardingrewardscopy_002,
      'ctrl_btnideclare_001': instance.ctrl_btnideclare_001,
      'title_featured_articles_001': instance.title_featured_articles_001,
      'txtxpoints_003': instance.txtxpoints_003,
      'txtxpoints_002': instance.txtxpoints_002,
      'txtyouremissingoutonpointscopy_001':
          instance.txtyouremissingoutonpointscopy_001,
      'txtyouremissingoutonpoints_001': instance.txtyouremissingoutonpoints_001,
      'txtfuturehealthintro_001': instance.txtfuturehealthintro_001,
      'txtyouareclosetomaximising_001': instance.txtyouareclosetomaximising_001,
      'txtbasedonyourvitalityprofile_002':
          instance.txtbasedonyourvitalityprofile_002,
      'txtfuturehealthsectionheader_001':
          instance.txtfuturehealthsectionheader_001,
      'txtfuturehealthsectiontext_001': instance.txtfuturehealthsectiontext_001,
      'txtfuturehealthsectionheader_002':
          instance.txtfuturehealthsectionheader_002,
      'txtfuturehealthsectiontext_002': instance.txtfuturehealthsectiontext_002,
      'txtbasedonyourvitalityprofile_001':
          instance.txtbasedonyourvitalityprofile_001,
      'txtyoucanadduptohealthyyears_001':
          instance.txtyoucanadduptohealthyyears_001,
      'txtbasedonyourvitalityprofile_003':
          instance.txtbasedonyourvitalityprofile_003,
      'txtfuturehealthsectionheader_003':
          instance.txtfuturehealthsectionheader_003,
      'txtfuturehealthsectiontext_003': instance.txtfuturehealthsectiontext_003,
      'txtfuturehealthsectiontext_004': instance.txtfuturehealthsectiontext_004,
      'txtyoucanadd10ormorehealthyyears_001':
          instance.txtyoucanadd10ormorehealthyyears_001,
      'txtyoucanaddmorehealthyyears_001':
          instance.txtyoucanaddmorehealthyyears_001,
      'txtfuturehealthsectiontext_005': instance.txtfuturehealthsectiontext_005,
      'txtfuturehealthsectiontext_006': instance.txtfuturehealthsectiontext_006,
      'txtbasedonyourvitalityprofile_004':
          instance.txtbasedonyourvitalityprofile_004,
      'ctrl_submitresultsandproof_001': instance.ctrl_submitresultsandproof_001,
      'ctrl_updateassessmentresults_001':
          instance.ctrl_updateassessmentresults_001,
      'ctrl_txtmoreaboutsmoking_001': instance.ctrl_txtmoreaboutsmoking_001,
      'ctrl_txtmoreaboutsleep_001': instance.ctrl_txtmoreaboutsleep_001,
      'ctrl_txtmoreaboutbloodglucose_001':
          instance.ctrl_txtmoreaboutbloodglucose_001,
      'ctrl_txtmoreaboutbodycomposition_001':
          instance.ctrl_txtmoreaboutbodycomposition_001,
      'ctrl_txtmoreaboutcholesterol_001':
          instance.ctrl_txtmoreaboutcholesterol_001,
      'ctrl_txtmoreaboutalcoholconsumption_001':
          instance.ctrl_txtmoreaboutalcoholconsumption_001,
      'ctrl_txtmoreaboutphysicalactivity_001':
          instance.ctrl_txtmoreaboutphysicalactivity_001,
      'ctrl_txtmoreaboutcardiofitness_001':
          instance.ctrl_txtmoreaboutcardiofitness_001,
      'ctrl_txtmoreaboutmentalwellbeing_001':
          instance.ctrl_txtmoreaboutmentalwellbeing_001,
      'ctrl_txtmoreaboutbloodpressure_001':
          instance.ctrl_txtmoreaboutbloodpressure_001,
      'ctrl_txtmoreaboutnutrition_001': instance.ctrl_txtmoreaboutnutrition_001,
      'txthealthpartnersapps_001': instance.txthealthpartnersapps_001,
      'txtdomoreofthis_001': instance.txtdomoreofthis_001,
      'txtkeeparecord_001': instance.txtkeeparecord_001,
      'txtmakeacommitment_001': instance.txtmakeacommitment_001,
      'ctrl_txtlearnmoreaboutthescience_001':
          instance.ctrl_txtlearnmoreaboutthescience_001,
      'txtunabletologin_001': instance.txtunabletologin_001,
      'txtunabletologincopy_001': instance.txtunabletologincopy_001,
      'partner_description_1044': instance.partner_description_1044,
      'partner_description_224': instance.partner_description_224,
      'partner_description_116': instance.partner_description_116,
      'partner_description_1008': instance.partner_description_1008,
      'partner_description_128': instance.partner_description_128,
      'partner_description_129': instance.partner_description_129,
      'partner_description_1040': instance.partner_description_1040,
      'txtcalculatingyourresults_001': instance.txtcalculatingyourresults_001,
      'txtcalculatingresultssubtext_001':
          instance.txtcalculatingresultssubtext_001,
      'txtsourcetext_001': instance.txtsourcetext_001,
      'txtsourcedateonlytext_001': instance.txtsourcedateonlytext_001,
      'txttarget_001': instance.txttarget_001,
      'partner_description_254': instance.partner_description_254,
      'partner_description_252': instance.partner_description_252,
      'partner_description_255': instance.partner_description_255,
      'partner_description_2025': instance.partner_description_2025,
      'partner_description_1167': instance.partner_description_1167,
      'partner_description_1168': instance.partner_description_1168,
      'partner_description_1161': instance.partner_description_1161,
      'partner_description_1162': instance.partner_description_1162,
      'partner_description_1169': instance.partner_description_1169,
      'partner_description_1170': instance.partner_description_1170,
      'partner_description_2081': instance.partner_description_2081,
      'partner_description_2082': instance.partner_description_2082,
      'partner_description_1124': instance.partner_description_1124,
      'partner_description_1123': instance.partner_description_1123,
      'partner_description_654079': instance.partner_description_654079,
      'partner_description_1065': instance.partner_description_1065,
      'partner_description_654081': instance.partner_description_654081,
      'whatsNew_item1On': instance.whatsNew_item1On,
      'whatsNew_item2On': instance.whatsNew_item2On,
      'whatsNew_item3On': instance.whatsNew_item3On,
      'whatsNew_item4On': instance.whatsNew_item4On,
      'txtwevemadesomeupdates_001': instance.txtwevemadesomeupdates_001,
      'txtwevemadesomeupdatescopy_001': instance.txtwevemadesomeupdatescopy_001,
      'txtwevemadesomeupdatescopy_002': instance.txtwevemadesomeupdatescopy_002,
      'txtwevemadesomeupdatescopy_003': instance.txtwevemadesomeupdatescopy_003,
      'txtwevemadesomeupdatescopy_004': instance.txtwevemadesomeupdatescopy_004,
      'txtinch_001': instance.txtinch_001,
      'txtfeet_001': instance.txtfeet_001,
      'txt_processing_begin_shortly': instance.txt_processing_begin_shortly,
      'txt_processing_file': instance.txt_processing_file,
      'txt_taking_longer_than_usual': instance.txt_taking_longer_than_usual,
      'txt_no_internet': instance.txt_no_internet,
      'txt_maximum_file_exceeds': instance.txt_maximum_file_exceeds,
      'txtyourpointsmaynot_001': instance.txtyourpointsmaynot_001,
      'no_coins_account_threshold_001': instance.no_coins_account_threshold_001,
      'no_account_description_001': instance.no_account_description_001,
      'txtnopromotionbody_002': instance.txtnopromotionbody_002,
      'ActivityCategoryLabeltxt_81': instance.ActivityCategoryLabeltxt_81,
      'ActivityCategoryLabeltxt_82': instance.ActivityCategoryLabeltxt_82,
      'ActivityCategoryLabeltxt_83': instance.ActivityCategoryLabeltxt_83,
      'ActivityCategoryLabeltxt_84': instance.ActivityCategoryLabeltxt_84,
      'txttime_002': instance.txttime_002,
      'ctrl_btnstartassessment_001': instance.ctrl_btnstartassessment_001,
      'ctrl_btncontinueassessment_001': instance.ctrl_btncontinueassessment_001,
      'ctrl_btnrestartassessment_001': instance.ctrl_btnrestartassessment_001,
      'txtnextdate_001': instance.txtnextdate_001,
      'ctrl_btneditresponses_001': instance.ctrl_btneditresponses_001,
      'healtassessment_modalsuccess_txthealthassessmentcomplete_001':
          instance.healtassessment_modalsuccess_txthealthassessmentcomplete_001,
      'healtassessment_modalsuccess_txthealthassessmentcomplete_002':
          instance.healtassessment_modalsuccess_txthealthassessmentcomplete_002,
      'ctrl_btncalculateresults_001': instance.ctrl_btncalculateresults_001,
      'txtphysicalactivityandfitness_001':
          instance.txtphysicalactivityandfitness_001,
      'txtphysicalactivityquestion_001':
          instance.txtphysicalactivityquestion_001,
      'txtnutrition_001': instance.txtnutrition_001,
      'txtbiometricsandhealthconditions_001':
          instance.txtbiometricsandhealthconditions_001,
      'txtmentalhealth_001': instance.txtmentalhealth_001,
      'txtalcoholandtobacco_001': instance.txtalcoholandtobacco_001,
      'txtsleep_001': instance.txtsleep_001,
      'txtsleep_guide_001': instance.txtsleep_guide_001,
      'txtsleep_intro_001': instance.txtsleep_intro_001,
      'txtsleep_intro_002': instance.txtsleep_intro_002,
      'txttips_sleep_001': instance.txttips_sleep_001,
      'txttips_sleep_content_001': instance.txttips_sleep_content_001,
      'txtconsistent_001': instance.txtconsistent_001,
      'txtconsistent_content_001': instance.txtconsistent_content_001,
      'txtconsistent_content_002': instance.txtconsistent_content_002,
      'txtsleepenvironment_001': instance.txtsleepenvironment_001,
      'txtsleepenvironment_content_001':
          instance.txtsleepenvironment_content_001,
      'txtexcercisereg_001': instance.txtexcercisereg_001,
      'txtexcercisereg_content_001': instance.txtexcercisereg_content_001,
      'txtexcercisereg_content_002': instance.txtexcercisereg_content_002,
      'txtexcercisereg_content_003': instance.txtexcercisereg_content_003,
      'txtexcercisereg_content_004': instance.txtexcercisereg_content_004,
      'txtexcercisereg_content_005': instance.txtexcercisereg_content_005,
      'txtexcercisereg_content_006': instance.txtexcercisereg_content_006,
      'txtexcercisereg_content_007': instance.txtexcercisereg_content_007,
      'txtexcercisereg_content_008': instance.txtexcercisereg_content_008,
      'txtexcercisereg_content_009': instance.txtexcercisereg_content_009,
      'txtexcercisereg_content_010': instance.txtexcercisereg_content_010,
      'txtexcercisereg_content_011': instance.txtexcercisereg_content_011,
      'txtexcercisereg_content_012': instance.txtexcercisereg_content_012,
      'txtexcercisereg_content_013': instance.txtexcercisereg_content_013,
      'txtexcercisereg_content_014': instance.txtexcercisereg_content_014,
      'txtexcercisereg_content_015': instance.txtexcercisereg_content_015,
      'txtexcercisereg_content_016': instance.txtexcercisereg_content_016,
      'txtexcercisereg_content_017': instance.txtexcercisereg_content_017,
      'txtexcercisereg_content_018': instance.txtexcercisereg_content_018,
      'txtroutine_001': instance.txtroutine_001,
      'txtroutine_content_001': instance.txtroutine_content_001,
      'txtwatchdrink_001': instance.txtwatchdrink_001,
      'txtwatchdrink_content_001': instance.txtwatchdrink_content_001,
      'txtwatchdrink_content_002': instance.txtwatchdrink_content_002,
      'ctrl_btnstart_001': instance.ctrl_btnstart_001,
      'txtactivitiescardsubtext_007': instance.txtactivitiescardsubtext_007,
      'txt_vitality_age_measure_001': instance.txt_vitality_age_measure_001,
      'ctrl_txtsaveandcompletelater_001':
          instance.ctrl_txtsaveandcompletelater_001,
      'ctrl_btnsubmitassessment_001': instance.ctrl_btnsubmitassessment_001,
      'txtdialogsaveandcompletelater_001':
          instance.txtdialogsaveandcompletelater_001,
      'txtdialogbodysaveandcompletelater_001':
          instance.txtdialogbodysaveandcompletelater_001,
      'txtrange_001': instance.txtrange_001,
      'txtpersonalisegoalsbody_002': instance.txtpersonalisegoalsbody_002,
      'txtpersonalisegoals_002': instance.txtpersonalisegoals_002,
      'healthassessment_txtdividersleep_001':
          instance.healthassessment_txtdividersleep_001,
      'healthassessment_txtdivideralcoholandtobacco_001':
          instance.healthassessment_txtdivideralcoholandtobacco_001,
      'healthassessment_txtdividermentalhealth_001':
          instance.healthassessment_txtdividermentalhealth_001,
      'healthassessment_txtdividerbiometrics_001':
          instance.healthassessment_txtdividerbiometrics_001,
      'healthassessment_txtdividernutrition_001':
          instance.healthassessment_txtdividernutrition_001,
      'healthassessment_txtdividerphysicalactivity_002':
          instance.healthassessment_txtdividerphysicalactivity_002,
      'txtsectionof_001': instance.txtsectionof_001,
      'txtactivitiescardsubtext_002': instance.txtactivitiescardsubtext_002,
      'healthassessment_txtdividerphysicalactivity_001':
          instance.healthassessment_txtdividerphysicalactivity_001,
      'onboarding_modalactivated_txtnowletsgetyouconnected_001':
          instance.onboarding_modalactivated_txtnowletsgetyouconnected_001,
      'txtareyousure_001': instance.txtareyousure_001,
      'ctrl_btnyesconnectlater_001': instance.ctrl_btnyesconnectlater_001,
      'txtareyousurecopy_001': instance.txtareyousurecopy_001,
      'txtthesciencebehindyourhealthmeasures_001':
          instance.txtthesciencebehindyourhealthmeasures_001,
      'txtsciencesectiontext_001': instance.txtsciencesectiontext_001,
      'txtsciencesectiontext_002': instance.txtsciencesectiontext_002,
      'txtsciencesectiontext_003': instance.txtsciencesectiontext_003,
      'txtsciencesectiontext_004': instance.txtsciencesectiontext_004,
      'txtsciencereference_001': instance.txtsciencereference_001,
      'txtvitalityhealthpriority_001': instance.txtvitalityhealthpriority_001,
      'txtvitalityfuturehealth_001': instance.txtvitalityfuturehealth_001,
      'txtfooter_002': instance.txtfooter_002,
      'txtweight_guide_001': instance.txtweight_guide_001,
      'txtweight_intro_001': instance.txtweight_intro_001,
      'txtweight_intro_002': instance.txtweight_intro_002,
      'txttips_weight_001': instance.txttips_weight_001,
      'txttips_weight_content_001': instance.txttips_weight_content_001,
      'txtrevieweating_001': instance.txtrevieweating_001,
      'txtrevieweating_content_001': instance.txtrevieweating_content_001,
      'txtrevieweating_content_002': instance.txtrevieweating_content_002,
      'txtimproveyourskills_001': instance.txtimproveyourskills_001,
      'txtimproveyourskills_content_001':
          instance.txtimproveyourskills_content_001,
      'txtfindsocialsupport_001': instance.txtfindsocialsupport_001,
      'txtfindsocialsupport_content_001':
          instance.txtfindsocialsupport_content_001,
      'txtfindsocialsupport_content_002':
          instance.txtfindsocialsupport_content_002,
      'txtpregnancy_guide_001': instance.txtpregnancy_guide_001,
      'txtpregnancy_intro_001': instance.txtpregnancy_intro_001,
      'txttips_pregnancy_001': instance.txttips_pregnancy_001,
      'txttips_pregnancy_content_001': instance.txttips_pregnancy_content_001,
      'txtnutrition_content_001': instance.txtnutrition_content_001,
      'txtnutrition_content_002': instance.txtnutrition_content_002,
      'txtvisitdrearlyandoften_001': instance.txtvisitdrearlyandoften_001,
      'txtvisitdrearlyandoften_content_001':
          instance.txtvisitdrearlyandoften_content_001,
      'txtthinkbabysafety_001': instance.txtthinkbabysafety_001,
      'txtthinkbabysafety_content_001': instance.txtthinkbabysafety_content_001,
      'txtavoidharmful_001': instance.txtavoidharmful_001,
      'txtavoidharmful_content_001': instance.txtavoidharmful_content_001,
      'txtglucose_guide_001': instance.txtglucose_guide_001,
      'txtglucose_intro_001': instance.txtglucose_intro_001,
      'txtglucose_intro_002': instance.txtglucose_intro_002,
      'txtglucose_intro_003': instance.txtglucose_intro_003,
      'txttips_glucose_001': instance.txttips_glucose_001,
      'txttips_glucose_content_001': instance.txttips_glucose_content_001,
      'txtmindfulofaddedsugar_001': instance.txtmindfulofaddedsugar_001,
      'txtmindfulofaddedsugar_content_001':
          instance.txtmindfulofaddedsugar_content_001,
      'txtmindfulofaddedsugar_content_002':
          instance.txtmindfulofaddedsugar_content_002,
      'txtmindfulofaddedsugar_content_003':
          instance.txtmindfulofaddedsugar_content_003,
      'txtfibrercihfoods_001': instance.txtfibrercihfoods_001,
      'txtfibrercihfoods_content_001': instance.txtfibrercihfoods_content_001,
      'txtfibrercihfoods_content_002': instance.txtfibrercihfoods_content_002,
      'txtfibrercihfoods_content_003': instance.txtfibrercihfoods_content_003,
      'btn_filter_by_001': instance.btn_filter_by_001,
      'filter_category_01': instance.filter_category_01,
      'txtmembershipyear_001': instance.txtmembershipyear_001,
      'clear_all_filter_001': instance.clear_all_filter_001,
      'apply_filter_001': instance.apply_filter_001,
      'txtDate_001': instance.txtDate_001,
      'txtactivity_001': instance.txtactivity_001,
      'txtaverageheartrate_001': instance.txtaverageheartrate_001,
      'txtmaximumheartrate_001': instance.txtmaximumheartrate_001,
      'Duration': instance.Duration,
      'txtsource_001': instance.txtsource_001,
      'txtburnedcalories_001': instance.txtburnedcalories_001,
      'txtactivitydetail_001': instance.txtactivitydetail_001,
      'txttotal_001': instance.txttotal_001,
      'txtstepcount_001': instance.txtstepcount_001,
      'heart_rate_bpm_001': instance.heart_rate_bpm_001,
      'categories_filtered_001': instance.categories_filtered_001,
      'txtcurrent_001': instance.txtcurrent_001,
      'txtprevious_001': instance.txtprevious_001,
      'ctrl_btnfilter_001': instance.ctrl_btnfilter_001,
      'txtkeepemailaddresspopulated_001':
          instance.txtkeepemailaddresspopulated_001,
      'txtusefingerprinttologin_001': instance.txtusefingerprinttologin_001,
      'txtloginemailaddress_001': instance.txtloginemailaddress_001,
      'txtcreatenewpassword_001': instance.txtcreatenewpassword_001,
      'txtsavechanges_001': instance.txtsavechanges_001,
      'txtauthenticationrequired_001': instance.txtauthenticationrequired_001,
      'txtvitalityoverallstatus_001': instance.txtvitalityoverallstatus_001,
      'dialog_body_changingemaillogin_001':
          instance.dialog_body_changingemaillogin_001,
      'chip_BMI': instance.chip_BMI,
      'chip_cashback_amount_001': instance.chip_cashback_amount_001,
      'ctrl_btnorder_device_001': instance.ctrl_btnorder_device_001,
      'txtmonthly_Apple_Watch_cashback_001':
          instance.txtmonthly_Apple_Watch_cashback_001,
      'txtmonthly_Generic_GProduct_cashback_001':
          instance.txtmonthly_Generic_GProduct_cashback_001,
      'ctrl_btnchoose_device_001': instance.ctrl_btnchoose_device_001,
      'txtuptoXcashback_001': instance.txtuptoXcashback_001,
      'txt_purchase_device_001': instance.txt_purchase_device_001,
      'chip_commitment_period_001': instance.chip_commitment_period_001,
      'get_active_001': instance.get_active_001,
      'txtearncashback_001': instance.txtearncashback_001,
      'cashback_amount_001': instance.cashback_amount_001,
      'cashback_amount_002': instance.cashback_amount_002,
      'txtactivatedevicechallenge_001': instance.txtactivatedevicechallenge_001,
      'previous_device_rewards_001': instance.previous_device_rewards_001,
      'txtscored_001': instance.txtscored_001,
      'txtcorrectanswer_001': instance.txtcorrectanswer_001,
      'txtchipnotachieved': instance.txtchipnotachieved,
      'txtenjoyrewardsandexclusivebenefitsjustforyou_001':
          instance.txtenjoyrewardsandexclusivebenefitsjustforyou_001,
      'txtmonthlyrewards_001': instance.txtmonthlyrewards_001,
      'txtstatusrewards_001': instance.txtstatusrewards_001,
      'txtexclusivebenefits_001': instance.txtexclusivebenefits_001,
      'txtvitalitymemberslikeyou_001': instance.txtvitalitymemberslikeyou_001,
      'ctrl_btnuploadanotherfile_001': instance.ctrl_btnuploadanotherfile_001,
      'ha_section_Iconkey_94': instance.ha_section_Iconkey_94,
      'ha_section_Iconkey_95': instance.ha_section_Iconkey_95,
      'ha_section_Iconkey_96': instance.ha_section_Iconkey_96,
      'ha_section_Iconkey_97': instance.ha_section_Iconkey_97,
      'ha_section_Iconkey_98': instance.ha_section_Iconkey_98,
      'ha_section_Iconkey_99': instance.ha_section_Iconkey_99,
      'txt_apple_watch_001': instance.txt_apple_watch_001,
      'txtpurchaseapplewatch_001': instance.txtpurchaseapplewatch_001,
      'cashback_amount_003': instance.cashback_amount_003,
      'txtturnoffnotifications_001': instance.txtturnoffnotifications_001,
      'txthaveaquestiongetintouch_001': instance.txthaveaquestiongetintouch_001,
      'txtwebsite_001': instance.txtwebsite_001,
      'txtemail_001': instance.txtemail_001,
      'txtyoureabouttoleaveourapp_001': instance.txtyoureabouttoleaveourapp_001,
      'txtyouarebeingredirectedtowebsite_001':
          instance.txtyouarebeingredirectedtowebsite_001,
      'txtyouarebeingredirectedtomakeacall_001':
          instance.txtyouarebeingredirectedtomakeacall_001,
      'txtyouarebeingredirectedtosendanemail_001':
          instance.txtyouarebeingredirectedtosendanemail_001,
      'dialog_authenticationrequired_001':
          instance.dialog_authenticationrequired_001,
      'dialog_entercurrentpassword_001':
          instance.dialog_entercurrentpassword_001,
      'dialog_existingpassword_001': instance.dialog_existingpassword_001,
      'dialog_weveupdatedyourpassword_001':
          instance.dialog_weveupdatedyourpassword_001,
      'apple_watch_breakout': instance.apple_watch_breakout,
      'aw_cashbackpts_1': instance.aw_cashbackpts_1,
      'aw_cashbackpts_2': instance.aw_cashbackpts_2,
      'aw_cashbackpts_3': instance.aw_cashbackpts_3,
      'aw_cashbackpts_4': instance.aw_cashbackpts_4,
      'genericfd_cashbackpts_1': instance.genericfd_cashbackpts_1,
      'genericfd_cashbackpts_2': instance.genericfd_cashbackpts_2,
      'genericfd_cashbackpts_3': instance.genericfd_cashbackpts_3,
      'genericfd_cashbackpts_4': instance.genericfd_cashbackpts_4,
      'aw_circlecontent_1': instance.aw_circlecontent_1,
      'aw_circlecontent_2': instance.aw_circlecontent_2,
      'aw_circlecontent_3': instance.aw_circlecontent_3,
      'aw_circlecontent_4': instance.aw_circlecontent_4,
      'genericfd_circlecontent_1': instance.genericfd_circlecontent_1,
      'genericfd_circlecontent_2': instance.genericfd_circlecontent_2,
      'genericfd_circlecontent_3': instance.genericfd_circlecontent_3,
      'genericfd_circlecontent_4': instance.genericfd_circlecontent_4,
      'aw_cashback_1': instance.aw_cashback_1,
      'aw_cashback_2': instance.aw_cashback_2,
      'aw_cashback_3': instance.aw_cashback_3,
      'aw_cashback_4': instance.aw_cashback_4,
      'genericfd_cashback_1': instance.genericfd_cashback_1,
      'genericfd_cashback_2': instance.genericfd_cashback_2,
      'genericfd_cashback_3': instance.genericfd_cashback_3,
      'genericfd_cashback_4': instance.genericfd_cashback_4,
      'txtearn_reward_001': instance.txtearn_reward_001,
      'txtearn_reward_002': instance.txtearn_reward_002,
      'chip_cashback_fd_001': instance.chip_cashback_fd_001,
      'chip_commitment_period_002': instance.chip_commitment_period_002,
      'txtpurchasedevice_001': instance.txtpurchasedevice_001,
      'txtyourfirstgoalstartson_date_001':
          instance.txtyourfirstgoalstartson_date_001,
      'txtuptoxperc_cashback_001': instance.txtuptoxperc_cashback_001,
      'txtcashbackstartssoon_001': instance.txtcashbackstartssoon_001,
      'device_invoice_number_001': instance.device_invoice_number_001,
      'device_purchase_date_001': instance.device_purchase_date_001,
      'device_purchase_amount_001': instance.device_purchase_amount_001,
      'txtchooseadevice_001': instance.txtchooseadevice_001,
      'txtonedevicecashback_001': instance.txtonedevicecashback_001,
      'txttotal_002': instance.txttotal_002,
      'cashback_effective_date_001': instance.cashback_effective_date_001,
      'cashback_months_completed_001': instance.cashback_months_completed_001,
      'device_order_information_001': instance.device_order_information_001,
      'cashback_history_001': instance.cashback_history_001,
      'monthly_device_cashback_001': instance.monthly_device_cashback_001,
      'txtwonreward_001_2277': instance.txtwonreward_001_2277,
      'txtachieveyourgoalstoearncoins_001':
          instance.txtachieveyourgoalstoearncoins_001,
      'txtwonreward_002_2277': instance.txtwonreward_002_2277,
      'txtwonreward_004_2277': instance.txtwonreward_004_2277,
      'txtachieveyourgoalstoearngiftcards_001':
          instance.txtachieveyourgoalstoearngiftcards_001,
      'txt_vhc_disclaimer_001': instance.txt_vhc_disclaimer_001,
      'txthowvitalityworks_001': instance.txthowvitalityworks_001,
      'txtpointsandstatus_002': instance.txtpointsandstatus_002,
      'txtcoinsandgiftcards_001': instance.txtcoinsandgiftcards_001,
      'txtmonthlygoalsandrewards_001': instance.txtmonthlygoalsandrewards_001,
      'ctrl_txtcontactus_001': instance.ctrl_txtcontactus_001,
      'txtinformationaboutyourdevice_001':
          instance.txtinformationaboutyourdevice_001,
      'txthowcanwehelpyou_001': instance.txthowcanwehelpyou_001,
      'ctrl_btnupload_002': instance.ctrl_btnupload_002,
      'ctrl_txtabout_001': instance.ctrl_txtabout_001,
      'txtexperiencingtechnicalissue_001':
          instance.txtexperiencingtechnicalissue_001,
      'txtneedhelpwithvitalioty_001': instance.txtneedhelpwithvitalioty_001,
      'txtlabelfeedbacktype_001': instance.txtlabelfeedbacktype_001,
      'txtalertfeedback_001': instance.txtalertfeedback_001,
      'txtdialogheaderfeedback_001': instance.txtdialogheaderfeedback_001,
      'txtdialogbodyfeedback_001': instance.txtdialogbodyfeedback_001,
      'txt_onboarding_reward_imagepath_001':
          instance.txt_onboarding_reward_imagepath_001,
      'txt_onboarding3_header_vitalityactivefull':
          instance.txt_onboarding3_header_vitalityactivefull,
      'txt_onboarding3_subtext_vitalityactivefull':
          instance.txt_onboarding3_subtext_vitalityactivefull,
      'txt_onboarding4_header_vitalityactivefull':
          instance.txt_onboarding4_header_vitalityactivefull,
      'txt_onboarding4_subtext_vitalityactivefull':
          instance.txt_onboarding4_subtext_vitalityactivefull,
      'txt_onboarding5_header_vitalityactivefull':
          instance.txt_onboarding5_header_vitalityactivefull,
      'txt_onboarding5_subtext_vitalityactivefull':
          instance.txt_onboarding5_subtext_vitalityactivefull,
      'txt_onboarding_reward_imagepath_vitalityactivefull':
          instance.txt_onboarding_reward_imagepath_vitalityactivefull,
      'txt_onboarding3_header_vitalityactivelite':
          instance.txt_onboarding3_header_vitalityactivelite,
      'txt_onboarding3_subtext_vitalityactivelite':
          instance.txt_onboarding3_subtext_vitalityactivelite,
      'txt_onboarding4_header_vitalityactivelite':
          instance.txt_onboarding4_header_vitalityactivelite,
      'txt_onboarding4_subtext_vitalityactivelite':
          instance.txt_onboarding4_subtext_vitalityactivelite,
      'txt_onboarding5_header_vitalityactivelite':
          instance.txt_onboarding5_header_vitalityactivelite,
      'txt_onboarding5_subtext_vitalityactivelite':
          instance.txt_onboarding5_subtext_vitalityactivelite,
      'txt_onboarding_reward_imagepath_vitalityactivelite':
          instance.txt_onboarding_reward_imagepath_vitalityactivelite,
      'txt_onboarding3_header_individual':
          instance.txt_onboarding3_header_individual,
      'txt_onboarding3_subtext_individual':
          instance.txt_onboarding3_subtext_individual,
      'txt_onboarding4_header_individual':
          instance.txt_onboarding4_header_individual,
      'txt_onboarding4_subtext_individual':
          instance.txt_onboarding4_subtext_individual,
      'txt_onboarding5_header_individual':
          instance.txt_onboarding5_header_individual,
      'txt_onboarding5_subtext_individual':
          instance.txt_onboarding5_subtext_individual,
      'txt_onboarding_reward_imagepath_individual':
          instance.txt_onboarding_reward_imagepath_individual,
      'txt_onboarding3_header_sme': instance.txt_onboarding3_header_sme,
      'txt_onboarding3_subtext_sme': instance.txt_onboarding3_subtext_sme,
      'txt_onboarding4_header_sme': instance.txt_onboarding4_header_sme,
      'txt_onboarding4_subtext_sme': instance.txt_onboarding4_subtext_sme,
      'txt_onboarding5_header_sme': instance.txt_onboarding5_header_sme,
      'txt_onboarding5_subtext_sme': instance.txt_onboarding5_subtext_sme,
      'txt_onboarding_reward_imagepath_sme':
          instance.txt_onboarding_reward_imagepath_sme,
      'txt_onboarding3_header_ind_sme': instance.txt_onboarding3_header_ind_sme,
      'txt_onboarding3_subtext_ind_sme':
          instance.txt_onboarding3_subtext_ind_sme,
      'txt_onboarding4_header_ind_sme': instance.txt_onboarding4_header_ind_sme,
      'txt_onboarding4_subtext_ind_sme':
          instance.txt_onboarding4_subtext_ind_sme,
      'txt_onboarding5_header_ind_sme': instance.txt_onboarding5_header_ind_sme,
      'txt_onboarding5_subtext_ind_sme':
          instance.txt_onboarding5_subtext_ind_sme,
      'txt_onboarding_reward_imagepath_ind_sme':
          instance.txt_onboarding_reward_imagepath_ind_sme,
      'teamchallenges_extend_already_dialog_description_001':
          instance.teamchallenges_extend_already_dialog_description_001,
      'teamchallenges_extend_already_dialog_title_001':
          instance.teamchallenges_extend_already_dialog_title_001,
      'teamchallenges_extend_button_001':
          instance.teamchallenges_extend_button_001,
      'teamchallenges_extend_dialog_message_001':
          instance.teamchallenges_extend_dialog_message_001,
      'teamchallenges_extend_dialog_title_001':
          instance.teamchallenges_extend_dialog_title_001,
      'teamchallenges_extend_first_week_dialog_description_001':
          instance.teamchallenges_extend_first_week_dialog_description_001,
      'teamchallenges_extend_first_week_dialog_title_001':
          instance.teamchallenges_extend_first_week_dialog_title_001,
      'teamchallenges_extend_success_001':
          instance.teamchallenges_extend_success_001,
      'teamchallenges_manage_data_consent_title_6346':
          instance.teamchallenges_manage_data_consent_title_6346,
      'teamchallenges_manage_leave_team_title_6347':
          instance.teamchallenges_manage_leave_team_title_6347,
      'teamchallenges_manage_delete_team_title_6348':
          instance.teamchallenges_manage_delete_team_title_6348,
      'teamchallenges_manage_createdby_title_6349':
          instance.teamchallenges_manage_createdby_title_6349,
      'teamchallenges_manage_team_invite_6350':
          instance.teamchallenges_manage_team_invite_6350,
      'teamchallenges_manage_leave_messgae_6351':
          instance.teamchallenges_manage_leave_messgae_6351,
      'teamchallenges_manage_rejoin_team_title_6352':
          instance.teamchallenges_manage_rejoin_team_title_6352,
      'teamchallenges_manage_cancel_title_6353':
          instance.teamchallenges_manage_cancel_title_6353,
      'teamchallenges_manage_leave_title_6354':
          instance.teamchallenges_manage_leave_title_6354,
      'teamchallenges_manage_delete_team_title_6355':
          instance.teamchallenges_manage_delete_team_title_6355,
      'teamchallenges_manage_undo_title_6356':
          instance.teamchallenges_manage_undo_title_6356,
      'proof_attachments_delete_button_2349':
          instance.proof_attachments_delete_button_2349,
      'teamchallenges_invite_team_sub_title_6336':
          instance.teamchallenges_invite_team_sub_title_6336,
      'teamchallenges_invite_team_main_title_6337':
          instance.teamchallenges_invite_team_main_title_6337,
      'teamchallenges_invite_team_progress_6343':
          instance.teamchallenges_invite_team_progress_6343,
      'teamchallenges_invite_team_manage_button_6344':
          instance.teamchallenges_invite_team_manage_button_6344,
      'VHR_Onboarding_title_7182': instance.VHR_Onboarding_title_7182,
      'teamchallenges_invite_team_button_6335':
          instance.teamchallenges_invite_team_button_6335,
      'teamchallenges_chip_steps': instance.teamchallenges_chip_steps,
      'teamchallenges_your_team_now_active':
          instance.teamchallenges_your_team_now_active,
      'teamchallenges_btn_got_it': instance.teamchallenges_btn_got_it,
      'txt_team_created_001': instance.txt_team_created_001,
      'chip_applewatch_cashback_amount_001':
          instance.chip_applewatch_cashback_amount_001,
      'chip_applewatch_commitment_period_001':
          instance.chip_applewatch_commitment_period_001,
      'chip_garmin_cashback_fd_001': instance.chip_garmin_cashback_fd_001,
      'chip_garmin_commitment_period_002':
          instance.chip_garmin_commitment_period_002,
      'chip_genp_cashback_amount_001': instance.chip_genp_cashback_amount_001,
      'chip_genpcommitment_period_001': instance.chip_genpcommitment_period_001,
      'chip_fitbit_cashback_fd_001': instance.chip_fitbit_cashback_fd_001,
      'chip_fitbit_commitment_period_002':
          instance.chip_fitbit_commitment_period_002,
      'chip_samsung_cashback_fd_001': instance.chip_samsung_cashback_fd_001,
      'chip_samsung_commitment_period_002':
          instance.chip_samsung_commitment_period_002,
      'chip_polar_cashback_fd_001': instance.chip_polar_cashback_fd_001,
      'chip_polar_commitment_period_002':
          instance.chip_polar_commitment_period_002,
      'txtpurchase_polar_device_001': instance.txtpurchase_polar_device_001,
      'txtpurchase_samsung_device_001': instance.txtpurchase_samsung_device_001,
      'txtpurchase_fitbit_device_001': instance.txtpurchase_fitbit_device_001,
      'txtpurchase_garmin_device_001': instance.txtpurchase_garmin_device_001,
      'txtpurchase_genprod_001': instance.txtpurchase_genprod_001,
      'old_wheel_txtdialogbodyfeedback_001':
          instance.old_wheel_txtdialogbodyfeedback_001,
      'old_wheel_txtswipequicklytospin_001':
          instance.old_wheel_txtswipequicklytospin_001,
      'old_wheel_txttargetachieved_001':
          instance.old_wheel_txttargetachieved_001,
      'old_wheel_ctrl_btnspinnow_001': instance.old_wheel_ctrl_btnspinnow_001,
      'old_wheel_ctrl_btnparticipatingpartners_001':
          instance.old_wheel_ctrl_btnparticipatingpartners_001,
      'old_wheel_ctrl_btnswapforotherreward_001':
          instance.old_wheel_ctrl_btnswapforotherreward_001,
      'old_wheel_txtswapforotherreward_001':
          instance.old_wheel_txtswapforotherreward_001,
      'old_wheel_ctrl_btnconfirmnewreward_001':
          instance.old_wheel_ctrl_btnconfirmnewreward_001,
      'old_wheel_txtswapforotherrewardfooter_001':
          instance.old_wheel_txtswapforotherrewardfooter_001,
      'old_wheel_txtyourcurrentreward_001':
          instance.old_wheel_txtyourcurrentreward_001,
      'old_wheel_txtwhatsonthewheel_001':
          instance.old_wheel_txtwhatsonthewheel_001,
      'old_wheel_txtmakeselection_001': instance.old_wheel_txtmakeselection_001,
      'old_wheel_txtthewheelistakingawhiletoload_001':
          instance.old_wheel_txtthewheelistakingawhiletoload_001,
      'old_wheel_txtwouldyouliketocontinuetowaitortrylater_001':
          instance.old_wheel_txtwouldyouliketocontinuetowaitortrylater_001,
      'old_wheel_ctrl_btnwait_001': instance.old_wheel_ctrl_btnwait_001,
      'old_wheel_ctrl_btntrylater_001': instance.old_wheel_ctrl_btntrylater_001,
      'old_wheel_txtthewheelistemporarilyunavailable_001':
          instance.old_wheel_txtthewheelistemporarilyunavailable_001,
      'old_wheel_dialog_body_': instance.old_wheel_dialog_body_,
      'super_roulette_txtwaytogoyouearneda_superwheelspin_001':
          instance.super_roulette_txtwaytogoyouearneda_superwheelspin_001,
      'super_roulette_txtswipequicklytospin_001':
          instance.super_roulette_txtswipequicklytospin_001,
      'super_roulette_ctrl_txtwhatsonthewheel_001':
          instance.super_roulette_ctrl_txtwhatsonthewheel_001,
      'super_roulette_dialog_hd': instance.super_roulette_dialog_hd,
      'super_roulette_dialog_body': instance.super_roulette_dialog_body,
      'txtyourdeviceisonitsway_apple_001':
          instance.txtyourdeviceisonitsway_apple_001,
      'txtyourdeviceisonitsway_garmin_001':
          instance.txtyourdeviceisonitsway_garmin_001,
      'txtyourdeviceisonitsway_fitbit_001':
          instance.txtyourdeviceisonitsway_fitbit_001,
      'txtyourdeviceisonitsway_samsung_001':
          instance.txtyourdeviceisonitsway_samsung_001,
      'txtyourdeviceisonitsway_polar_001':
          instance.txtyourdeviceisonitsway_polar_001,
      'teamchallenges_team_enddate_title_001':
          instance.teamchallenges_team_enddate_title_001,
      'teamchallenges_team_created_001':
          instance.teamchallenges_team_created_001,
      'teamchallenges_watch_team_progress_001':
          instance.teamchallenges_watch_team_progress_001,
      'teamchallenges_successfully_joined_team_001':
          instance.teamchallenges_successfully_joined_team_001,
      'teamchallenges_reached_the_maximum_capacity_001':
          instance.teamchallenges_reached_the_maximum_capacity_001,
      'txt2factorverificationbody_001': instance.txt2factorverificationbody_001,
      'txt2factorverificationcopy_002': instance.txt2factorverificationcopy_002,
      'txt2factorverificationenabled_001':
          instance.txt2factorverificationenabled_001,
      'txt2factorverificationenabled_002':
          instance.txt2factorverificationenabled_002,
      'txt2factorverificationheader_001':
          instance.txt2factorverificationheader_001,
      'ctrL_btnenable_001': instance.ctrL_btnenable_001,
      'ctrL_btnnothanksmaybelater_001': instance.ctrL_btnnothanksmaybelater_001,
      'txtpleaseselectyourmethodofverification_001':
          instance.txtpleaseselectyourmethodofverification_001,
      'txtverifywithemail_001': instance.txtverifywithemail_001,
      'txtverifywithemail_002': instance.txtverifywithemail_002,
      'txtverifywithemailbody_002': instance.txtverifywithemailbody_002,
      'txtverifywithtext_001': instance.txtverifywithtext_001,
      'txtverifywithtextbody_001': instance.txtverifywithtextbody_001,
      'ctrL_btnselect_001': instance.ctrL_btnselect_001,
      'txtphonenumber_001': instance.txtphonenumber_001,
      'ctr__btnsendverificationcode_001':
          instance.ctr__btnsendverificationcode_001,
      'ctr_btnemail_001': instance.ctr_btnemail_001,
      'ctrL_btnresendverificationcode_001':
          instance.ctrL_btnresendverificationcode_001,
      'txtenterverificationcode_001': instance.txtenterverificationcode_001,
      'txtenterverificationcode_002': instance.txtenterverificationcode_002,
      'ctrL_btnsubmit_001': instance.ctrL_btnsubmit_001,
      'ctrL_btndone_001': instance.ctrL_btndone_001,
      'txtyouhaveenteredthewrongcodepleasetryagain_001':
          instance.txtyouhaveenteredthewrongcodepleasetryagain_001,
      'txtyouhaveenteredthewrongcodepleasetryagain_002':
          instance.txtyouhaveenteredthewrongcodepleasetryagain_002,
      'ctrL_btncancel_001': instance.ctrL_btncancel_001,
      'ctrL_btntryagain_001': instance.ctrL_btntryagain_001,
      'ctrL_btnlogout_001': instance.ctrL_btnlogout_001,
      'ctrL_btnresetpassword_001': instance.ctrL_btnresetpassword_001,
      'txtsomethinghasgonewrong_001': instance.txtsomethinghasgonewrong_001,
      'txtsomethinghasgonewrong_002': instance.txtsomethinghasgonewrong_002,
      'txtincorrectcode_001': instance.txtincorrectcode_001,
      'txtincorrectcode_002': instance.txtincorrectcode_002,
      'ctrL_btnok_001': instance.ctrL_btnok_001,
      'txtcodeexpired_001': instance.txtcodeexpired_001,
      'txtcodeexpired_002': instance.txtcodeexpired_002,
      'ctrL_btnsendnewcode_001': instance.ctrL_btnsendnewcode_001,
      'txtuse2faverification_001': instance.txtuse2faverification_001,
      'txtupdate2faverification_001': instance.txtupdate2faverification_001,
      'txtcurrentmethod_001': instance.txtcurrentmethod_001,
      'txtchoosemethod_001': instance.txtchoosemethod_001,
      'tc_txtphysicalactivitygoal_001': instance.tc_txtphysicalactivitygoal_001,
      'tc_txtgetproof_001': instance.tc_txtgetproof_001,
      'tc_txtautotracked_001': instance.tc_txtautotracked_001,
      'tc_ctrl_btnselectgoal_001': instance.tc_ctrl_btnselectgoal_001,
      'tc_ctrl_btn_001': instance.tc_ctrl_btn_001,
      'tc_txthowtocomplete_001': instance.tc_txthowtocomplete_001,
      'tc_txttakeatwominutemovementbreakeverywakinghour_001':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_001,
      'tc_txttakeatwominutemovementbreakeverywakinghour_002':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_002,
      'tc_txttakeatwominutemovementbreakeverywakinghour_002_1':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_002_1,
      'tc_txttakeatwominutemovementbreakeverywakinghour_002_2':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_002_2,
      'tc_txttakeatwominutemovementbreakeverywakinghour_002_3':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_002_3,
      'tc_txttakeatwominutemovementbreakeverywakinghour_002_4':
          instance.tc_txttakeatwominutemovementbreakeverywakinghour_002_4,
      'tc_txtcheckintoyourgoalthesameday_002':
          instance.tc_txtcheckintoyourgoalthesameday_002,
      'tc_txtcheckintoyourgoalthesameday_001':
          instance.tc_txtcheckintoyourgoalthesameday_001,
      'tc_leaving_a_team_001': instance.tc_leaving_a_team_001,
      'tc_leaving_a_team_002': instance.tc_leaving_a_team_002,
      'tc_txtwhyitsimportant_001': instance.tc_txtwhyitsimportant_001,
      'tc_wlg_healthcheck_txbodytip_001':
          instance.tc_wlg_healthcheck_txbodytip_001,
      'tc_txthowtocomplete_002': instance.tc_txthowtocomplete_002,
      'tc_txtsubmitresultsandproofexplanationcopy_001':
          instance.tc_txtsubmitresultsandproofexplanationcopy_001,
      'tc_label_001': instance.tc_label_001,
      'tc_ctrl_btnnext_001': instance.tc_ctrl_btnnext_001,
      'tc_dialog_hd_001': instance.tc_dialog_hd_001,
      'tc_dialog_body_001': instance.tc_dialog_body_001,
      'tc_dialog_hd_002': instance.tc_dialog_hd_002,
      'tc_dialog_body_002': instance.tc_dialog_body_002,
      'tc_btn_Agree': instance.tc_btn_Agree,
      'tc_btn_Disagree': instance.tc_btn_Disagree,
      'tc_txthdunknwnerr_001': instance.tc_txthdunknwnerr_001,
      'tc_txtbdunknwnerr_002': instance.tc_txtbdunknwnerr_002,
      'tc_ctrl_btncancel_001': instance.tc_ctrl_btncancel_001,
      'tc_ctrl_btnyeslogout_001': instance.tc_ctrl_btnyeslogout_001,
      'tc_txt_001': instance.tc_txt_001,
      'tc_txtsubmitresultsandproofexplanationcopy_002':
          instance.tc_txtsubmitresultsandproofexplanationcopy_002,
      'tc_label_002': instance.tc_label_002,
      'tc_ctrl_btnnext_002': instance.tc_ctrl_btnnext_002,
      'tc_txtbdunknwnerr_003': instance.tc_txtbdunknwnerr_003,
      'txtaccountlocked_001': instance.txtaccountlocked_001,
      'txtaccountlockedcopy_001': instance.txtaccountlockedcopy_001,
      'ctrl_btnactivatereward_001': instance.ctrl_btnactivatereward_001,
      'txtmonthlyhealthyfoodcashback_001':
          instance.txtmonthlyhealthyfoodcashback_001,
      'txt_requiredactivity_001': instance.txt_requiredactivity_001,
      'txtpurchasehealthy_food_001': instance.txtpurchasehealthy_food_001,
      'txtearn_hf_reward_001': instance.txtearn_hf_reward_001,
      'txtearn_hf_reward_002': instance.txtearn_hf_reward_002,
      'txtexercise_001': instance.txtexercise_001,
      'txtpointsperday_001': instance.txtpointsperday_001,
      'txtmanual_gym_checkin': instance.txtmanual_gym_checkin,
      'ctrl_btngymcheckin_001': instance.ctrl_btngymcheckin_001,
      'ctrl_btnconnectanappordevice_001':
          instance.ctrl_btnconnectanappordevice_001,
      'hf_footer_email_001': instance.hf_footer_email_001,
      'txt_hf_footer_001': instance.txt_hf_footer_001,
      'chip_cashback_HF_001': instance.chip_cashback_HF_001,
      'highest_cashback_achieved': instance.highest_cashback_achieved,
      'cashback_date_range_001': instance.cashback_date_range_001,
      'txtxpointsmonth_001': instance.txtxpointsmonth_001,
      'txtcashbackmonth_001': instance.txtcashbackmonth_001,
      'cashback_calc_day_001': instance.cashback_calc_day_001,
      'total_cashback_002': instance.total_cashback_002,
      'Total_cashback_earned_001': instance.Total_cashback_earned_001,
      'txtyourprogress_001': instance.txtyourprogress_001,
      'levels_cashbackpts_range': instance.levels_cashbackpts_range,
      'levels_cashbackpts_range_last': instance.levels_cashbackpts_range_last,
      'healthy_food_complete_hc_001': instance.healthy_food_complete_hc_001,
      'Supermaxi_membership_001': instance.Supermaxi_membership_001,
      'hf_onboarding_level_points_004': instance.hf_onboarding_level_points_004,
      'hf_onboarding_level_points_003': instance.hf_onboarding_level_points_003,
      'hf_onboarding_level_points_002': instance.hf_onboarding_level_points_002,
      'hf_onboarding_level_points_001': instance.hf_onboarding_level_points_001,
      'hf_onboarding_cycle_cashback_004':
          instance.hf_onboarding_cycle_cashback_004,
      'hf_onboarding_cycle_cashback_003':
          instance.hf_onboarding_cycle_cashback_003,
      'hf_onboarding_cycle_cashback_002':
          instance.hf_onboarding_cycle_cashback_002,
      'hf_onboarding_cycle_cashback_001':
          instance.hf_onboarding_cycle_cashback_001,
      'hf_onboarding_cashback_004': instance.hf_onboarding_cashback_004,
      'hf_onboarding_cashback_003': instance.hf_onboarding_cashback_003,
      'hf_onboarding_cashback_002': instance.hf_onboarding_cashback_002,
      'hf_onboarding_cashback_001': instance.hf_onboarding_cashback_001,
      'hf_visit_website': instance.hf_visit_website,
      'question_input_hint_102503': instance.question_input_hint_102503,
      'question_input_hint_102452': instance.question_input_hint_102452,
      'question_input_hint_100111': instance.question_input_hint_100111,
      'question_input_hint_100105': instance.question_input_hint_100105,
      'question_input_hint_100102': instance.question_input_hint_100102,
      'question_input_hint_100109': instance.question_input_hint_100109,
      'question_input_hint_102476': instance.question_input_hint_102476,
      'question_input_hint_100113': instance.question_input_hint_100113,
      'question_input_hint_100118': instance.question_input_hint_100118,
      'question_input_hint_100123': instance.question_input_hint_100123,
      'question_input_hint_100112': instance.question_input_hint_100112,
      'question_input_hint_102501': instance.question_input_hint_102501,
      'question_input_hint_100114': instance.question_input_hint_100114,
      'question_input_hint_101627': instance.question_input_hint_101627,
      'question_input_hint_101626': instance.question_input_hint_101626,
      'question_input_hint_100120': instance.question_input_hint_100120,
      'question_input_hint_101505': instance.question_input_hint_101505,
      'question_input_hint_100127': instance.question_input_hint_100127,
      'question_input_hint_100128': instance.question_input_hint_100128,
      'question_input_hint_height_ft': instance.question_input_hint_height_ft,
      'question_input_hint_height_in': instance.question_input_hint_height_in,
      'question_input_hint_height_cm': instance.question_input_hint_height_cm,
      'question_input_hint_420000001': instance.question_input_hint_420000001,
      'question_input_hint_310000075': instance.question_input_hint_310000075,
      'question_input_hint_410000001': instance.question_input_hint_410000001,
      'healtassessment_modalsuccess_txthealthassessmentcomplete_003':
          instance.healtassessment_modalsuccess_txthealthassessmentcomplete_003,
      'txtidontknow_001': instance.txtidontknow_001,
      'txtsection': instance.txtsection,
      'txtrange': instance.txtrange,
      'txttimeforyourfirstspin_001': instance.txttimeforyourfirstspin_001,
      'txtswiptetospin_002': instance.txtswiptetospin_002,
      'tracking_screen_header_001': instance.tracking_screen_header_001,
      'txtxcoinsmonth_001': instance.txtxcoinsmonth_001,
      'txtcasback_001': instance.txtcasback_001,
      'total_coins_earned_001': instance.total_coins_earned_001,
      'txtapplewatch_001': instance.txtapplewatch_001,
      'txt_reward_activation_time_001': instance.txt_reward_activation_time_001,
      'txt_reward_hf_starts_soon_001': instance.txt_reward_hf_starts_soon_001,
      'tc_how_it_works_one': instance.tc_how_it_works_one,
      'tc_how_it_works_two': instance.tc_how_it_works_two,
      'tc_how_it_works_three': instance.tc_how_it_works_three,
      'tc_how_it_works_four': instance.tc_how_it_works_four,
      'txtmentalwellbeingassessment_001':
          instance.txtmentalwellbeingassessment_001,
      'txtlowrisk_001': instance.txtlowrisk_001,
      'txtstayconnectedtofriendsandfamily_001':
          instance.txtstayconnectedtofriendsandfamily_001,
      'mentalwellbeing_assessment_txtblowrisk_001':
          instance.mentalwellbeing_assessment_txtblowrisk_001,
      'txtmoderaterisk_001': instance.txtmoderaterisk_001,
      'txtgetsupportnow_001': instance.txtgetsupportnow_001,
      'mentalwellbeing_assessment_txtbmoderaterisk_001':
          instance.mentalwellbeing_assessment_txtbmoderaterisk_001,
      'txthighrisk_001': instance.txthighrisk_001,
      'txtconsultyourhealthcareprovider_001':
          instance.txtconsultyourhealthcareprovider_001,
      'mentalwellbeing_assessment_txtbhighrisk_001':
          instance.mentalwellbeing_assessment_txtbhighrisk_001,
      'mha_section_Iconkey_118': instance.mha_section_Iconkey_118,
      'mha_section_Iconkey_119': instance.mha_section_Iconkey_119,
      'mha_section_Iconkey_120': instance.mha_section_Iconkey_120,
      'txtearnpoints_001': instance.txtearnpoints_001,
      'txtfeedsheadergoals_001': instance.txtfeedsheadergoals_001,
      'txtassessments_001': instance.txtassessments_001,
      'txtprevention_001': instance.txtprevention_001,
      'ctrl_txt_onlysendreport_001': instance.ctrl_txt_onlysendreport_001,
      'ctrl_txt_haveyoubeenaskedtosend_001':
          instance.ctrl_txt_haveyoubeenaskedtosend_001,
      'ctrl_txt_sendreport_001': instance.ctrl_txt_sendreport_001,
      'ctrl_txt_sendatroubleshootingreport_001':
          instance.ctrl_txt_sendatroubleshootingreport_001,
      'ctrl_txt_onlysendareport_001': instance.ctrl_txt_onlysendareport_001,
      'txtareyousureyouwanttoclosethesurvey_001':
          instance.txtareyousureyouwanttoclosethesurvey_001,
      'txt_closesurvey_001': instance.txt_closesurvey_001,
      'ctrl_txtcancel_001': instance.ctrl_txtcancel_001,
      'ctrl_txtyes_001': instance.ctrl_txtyes_001,
      'ctrl_btncontinuetofeedback_001': instance.ctrl_btncontinuetofeedback_001,
      'vhc_submission_screen_date_of_activity_date_format':
          instance.vhc_submission_screen_date_of_activity_date_format,
      'dt_format_date_short_month_full_year':
          instance.dt_format_date_short_month_full_year,
      'txtbronze_001': instance.txtbronze_001,
      'txtbronze_subtext_001': instance.txtbronze_subtext_001,
      'txtsilver_subtext_001': instance.txtsilver_subtext_001,
      'txtgold_subtext_001': instance.txtgold_subtext_001,
      'txtplatinum_subtext_001': instance.txtplatinum_subtext_001,
      'pag_history_header_date_format': instance.pag_history_header_date_format,
      'pag_history_list_week_date_format':
          instance.pag_history_list_week_date_format,
      'wlg_history_header_date_format': instance.wlg_history_header_date_format,
      'wlg_history_list_week_date_format':
          instance.wlg_history_list_week_date_format,
      'dt_format_short_month_full_year':
          instance.dt_format_short_month_full_year,
      'dt_format_date_short_month': instance.dt_format_date_short_month,
      'how_to_earn_points_steps_number_format':
          instance.how_to_earn_points_steps_number_format,
      'how_to_earn_points_points_number_format':
          instance.how_to_earn_points_points_number_format,
      'num_format_non_decimal_number': instance.num_format_non_decimal_number,
      'txtbronze_subtext_002': instance.txtbronze_subtext_002,
      'txtsilver_subtext_002': instance.txtsilver_subtext_002,
      'txtgold_subtext_002': instance.txtgold_subtext_002,
      'txtplatinum_subtext_002': instance.txtplatinum_subtext_002,
      'txtblue_subtext_001': instance.txtblue_subtext_001,
      'txtmentalwellbeingasessmentcomplete_001':
          instance.txtmentalwellbeingasessmentcomplete_001,
      'txtVHC_Submissions_Date_001': instance.txtVHC_Submissions_Date_001,
      'txtVHC_Submissions_Date_002': instance.txtVHC_Submissions_Date_002,
      'txtavailabledevices_001': instance.txtavailabledevices_001,
      'txtonlyonedevicecashback_001': instance.txtonlyonedevicecashback_001,
      'pending_calculation_001': instance.pending_calculation_001,
      'txt_uptoperc_cashback_001': instance.txt_uptoperc_cashback_001,
      'txt_ptstostatus_001': instance.txt_ptstostatus_001,
      'txtdaterange_001': instance.txtdaterange_001,
      'txthowtoearnsubtext_001': instance.txthowtoearnsubtext_001,
      'txthowtoearnsubtext_004': instance.txthowtoearnsubtext_004,
      'txthowtoearnsubtext_005': instance.txthowtoearnsubtext_005,
      'txtcheckups_001': instance.txtcheckups_001,
      'txt_pag_earn_maximum': instance.txt_pag_earn_maximum,
      'txt_pag_no_maximum': instance.txt_pag_no_maximum,
      'txt_hf_paused_001': instance.txt_hf_paused_001,
      'txt_automatic_gym_note': instance.txt_automatic_gym_note,
      'txt_reward_pendactivation_time_001':
          instance.txt_reward_pendactivation_time_001,
      'txtrewardsmall_001': instance.txtrewardsmall_001,
      'txt_hf_purchase_fresh_001': instance.txt_hf_purchase_fresh_001,
      'ctrl_txt_pts_001': instance.ctrl_txt_pts_001,
      'ctrl_txt_yourcurrentmembershipyear_001':
          instance.ctrl_txt_yourcurrentmembershipyear_001,
      'ctrl_txt_totalpointsearned_001': instance.ctrl_txt_totalpointsearned_001,
      'ctrl_txt_daysleft_001': instance.ctrl_txt_daysleft_001,
      'ctrl_txt_statusrewards_001': instance.ctrl_txt_statusrewards_001,
      'ctrl_txt_yourrewardlevel_001': instance.ctrl_txt_yourrewardlevel_001,
      'ctrl_txt_discounton_001': instance.ctrl_txt_discounton_001,
      'ctrl_txt_activitiestoearnpts_001':
          instance.ctrl_txt_activitiestoearnpts_001,
      'ctrl_txt_keepearningpts_001': instance.ctrl_txt_keepearningpts_001,
      'ctrl_txt_thehealthieryouare_001':
          instance.ctrl_txt_thehealthieryouare_001,
      'ctrl_txt_relatedfaqs_001': instance.ctrl_txt_relatedfaqs_001,
      'txtotheractivitiestext_001': instance.txtotheractivitiestext_001,
      'pg_txt_title_001': instance.pg_txt_title_001,
      'pg_calendar_day_002': instance.pg_calendar_day_002,
      'pg_btnactivatereward_003': instance.pg_btnactivatereward_003,
      'pg_txthowitworks_004': instance.pg_txthowitworks_004,
      'pg_txtheaderactivatebenifit_005':
          instance.pg_txtheaderactivatebenifit_005,
      'pg_txtactivatebenifitdesc_line1_005':
          instance.pg_txtactivatebenifitdesc_line1_005,
      'pg_txtactivatebenifitdesc_line2_005':
          instance.pg_txtactivatebenifitdesc_line2_005,
      'pg_txtheaderletexercise_006': instance.pg_txtheaderletexercise_006,
      'pg_txtdescletexercise_006': instance.pg_txtdescletexercise_006,
      'pg_txtheaderletexercise_007': instance.pg_txtheaderletexercise_007,
      'pg_txtdescletexercise_line1_007':
          instance.pg_txtdescletexercise_line1_007,
      'pg_txtdescletexercise_line2_007':
          instance.pg_txtdescletexercise_line2_007,
      'pg_txtheaderpromocodes_008': instance.pg_txtheaderpromocodes_008,
      'pg_txtdescpromocodes_line1_008': instance.pg_txtdescpromocodes_line1_008,
      'pg_txtdescpromocodes_line2_008': instance.pg_txtdescpromocodes_line2_008,
      'pg_txtdescpromocodes_line3_008': instance.pg_txtdescpromocodes_line3_008,
      'pg_txtcardtitleweeklyphyactgoal_009':
          instance.pg_txtcardtitleweeklyphyactgoal_009,
      'pg_txtcardtitleRelatedfaqs_010': instance.pg_txtcardtitleRelatedfaqs_010,
      'pg_txt_notinstalldialog_hd_011': instance.pg_txt_notinstalldialog_hd_011,
      'pg_txt_notinstalldialog_body_012':
          instance.pg_txt_notinstalldialog_body_012,
      'pg_txtheaderactivatedgoal_013': instance.pg_txtheaderactivatedgoal_013,
      'pg_txtsubtitleactivategoal': instance.pg_txtsubtitleactivategoal,
      'pg_btngotit_014': instance.pg_btngotit_014,
      'pg_txtyourfirstgoalstartsondate_016':
          instance.pg_txtyourfirstgoalstartsondate_016,
      'pg_txtcardtitlearcsupitem_017': instance.pg_txtcardtitlearcsupitem_017,
      'pg_txtarchivedgiftcards_018': instance.pg_txtarchivedgiftcards_018,
      'pg_txtnogiftcardsarchived_019': instance.pg_txtnogiftcardsarchived_019,
      'pg_txtnogiftcardsarchiveddesc_020':
          instance.pg_txtnogiftcardsarchiveddesc_020,
      'pg_btnarchive_021': instance.pg_btnarchive_021,
      'pg_txtitemswillshowhere_022': instance.pg_txtitemswillshowhere_022,
      'pg_txtclaimsurpriseitem_023': instance.pg_txtclaimsurpriseitem_023,
      'pg_txtrevealyourpromocode_024': instance.pg_txtrevealyourpromocode_024,
      'pg_txtbrandName_025': instance.pg_txtbrandName_025,
      'pg_btnrevealcode_026': instance.pg_btnrevealcode_026,
      'pg_txtusethecodetoredeem_027': instance.pg_txtusethecodetoredeem_027,
      'pg_txtdiscountcode_028': instance.pg_txtdiscountcode_028,
      'pg_btncopy_029': instance.pg_btncopy_029,
      'pg_btnvisitwebsite_030': instance.pg_btnvisitwebsite_030,
      'pg_btnarchivegiftcard_031': instance.pg_btnarchivegiftcard_031,
      'pg_txtarchivedgiftcards_032': instance.pg_txtarchivedgiftcards_032,
      'pg_txtaechivesurpriseitemtitle_033':
          instance.pg_txtaechivesurpriseitemtitle_033,
      'pg_txtexpiesInDays_034': instance.pg_txtexpiesInDays_034,
      'pg_txtexpies_035': instance.pg_txtexpies_035,
      'pg_txtheadererror_036': instance.pg_txtheadererror_036,
      'pg_txterrordescription_037': instance.pg_txterrordescription_037,
      'pg_txtredeemcodehelp_038': instance.pg_txtredeemcodehelp_038,
      'pg_txtanynotefromsupplier_039': instance.pg_txtanynotefromsupplier_039,
      'pg_txtheadersurprise_040': instance.pg_txtheadersurprise_040,
      'pg_txtsubheadersurprise_041': instance.pg_txtsubheadersurprise_041,
      'pg_btninstall_042': instance.pg_btninstall_042,
      'pg_btncancel_043': instance.pg_btncancel_043,
      'vj_pageHeaderH2_v1': instance.vj_pageHeaderH2_v1,
      'vj_pointsTracker_v1': instance.vj_pointsTracker_v1,
      'vj_singleContentLine_v1': instance.vj_singleContentLine_v1,
      'vj_ctrl_btnselectgoal_001': instance.vj_ctrl_btnselectgoal_001,
      'vj_txthowtocomplete_001': instance.vj_txthowtocomplete_001,
      'vj_txttakeatwominutemovementbreakeverywakinghour_001':
          instance.vj_txttakeatwominutemovementbreakeverywakinghour_001,
      'vj_txttakeatwominutemovementbreakeverywakinghour_002':
          instance.vj_txttakeatwominutemovementbreakeverywakinghour_002,
      'vj_sectionHeaderH5_v1': instance.vj_sectionHeaderH5_v1,
      'vj_paragraphText_v1': instance.vj_paragraphText_v1,
      'vj_txtcheckintoyourgoalthesameday_001':
          instance.vj_txtcheckintoyourgoalthesameday_001,
      'vj_txtcheckintoyourgoalthesameday_002':
          instance.vj_txtcheckintoyourgoalthesameday_002,
      'vj_txtcheckintoyourgoalthesameday_003':
          instance.vj_txtcheckintoyourgoalthesameday_003,
      'vj_txtcheckintoyourgoalthesameday_004':
          instance.vj_txtcheckintoyourgoalthesameday_004,
      'vj_ctrl_txtappsanddevices_001': instance.vj_ctrl_txtappsanddevices_001,
      'vj_ctrl_txtaboutphysicalactivitygoal_001':
          instance.vj_ctrl_txtaboutphysicalactivitygoal_001,
      'vj_txtsetstridelength_001': instance.vj_txtsetstridelength_001,
      'vj_txtsubmitresultsandproofexplanationcopy_001':
          instance.vj_txtsubmitresultsandproofexplanationcopy_001,
      'vj_txtpointsmaytakeupto24hourstoreflect_001_1':
          instance.vj_txtpointsmaytakeupto24hourstoreflect_001_1,
      'vj_txtpointsmaytakeupto24hourstoreflect_001_2':
          instance.vj_txtpointsmaytakeupto24hourstoreflect_001_2,
      'vj_txtpointsmaytakeupto24hourstoreflect_001_3':
          instance.vj_txtpointsmaytakeupto24hourstoreflect_001_3,
      'vj_label_': instance.vj_label_,
      'vj_ctrl_btnnext_001': instance.vj_ctrl_btnnext_001,
      'vj_txtdisconnected_001': instance.vj_txtdisconnected_001,
      'vj_supportText_': instance.vj_supportText_,
      'vj_txtgetreadytostartmoving_001':
          instance.vj_txtgetreadytostartmoving_001,
      'vj_txtyourfirststartgoal_001': instance.vj_txtyourfirststartgoal_001,
      'vj_ctrl_btngotit_001': instance.vj_ctrl_btngotit_001,
      'vj_txtautotracked_001': instance.vj_txtautotracked_001,
      'vj_comboHd1': instance.vj_comboHd1,
      'vj_txtpointsreflectnote_001': instance.vj_txtpointsreflectnote_001,
      'vj_comboHd2': instance.vj_comboHd2,
      'vj_comboHd3': instance.vj_comboHd3,
      'vj_txt_001': instance.vj_txt_001,
      'vj_txtaccountdetails_001': instance.vj_txtaccountdetails_001,
      'vj_ctrl_txthowitworks_001': instance.vj_ctrl_txthowitworks_001,
      'vj_txtbodyhowtocomplete_001': instance.vj_txtbodyhowtocomplete_001,
      'vj_sectionHd1': instance.vj_sectionHd1,
      'vj_sectionHd2': instance.vj_sectionHd2,
      'vj_txt_002': instance.vj_txt_002,
      'vj_txtmilestoneachieved_001': instance.vj_txtmilestoneachieved_001,
      'vj_txtmilestoneachieved_002': instance.vj_txtmilestoneachieved_002,
      'vj_success': instance.vj_success,
      'vj_success_sub_text': instance.vj_success_sub_text,
      'vj_ctrl_btn_001': instance.vj_ctrl_btn_001,
      'vj_physicalactivity_modalactivated_txtactivatedgoal_002':
          instance.vj_physicalactivity_modalactivated_txtactivatedgoal_002,
      'vj_physicalactivity_modalactivated_txtyourfirststartgoal_002':
          instance.vj_physicalactivity_modalactivated_txtyourfirststartgoal_002,
      'vj_ctrl_btngotit_002': instance.vj_ctrl_btngotit_002,
      'vj_ctrl_btn_002': instance.vj_ctrl_btn_002,
      'vj_txtwhatsonthewheel_001': instance.vj_txtwhatsonthewheel_001,
      'vj_dialog_body_': instance.vj_dialog_body_,
      'vj_label_text': instance.vj_label_text,
      'vj_dialog_btn1_': instance.vj_dialog_btn1_,
      'vj_dialog_btn1_1': instance.vj_dialog_btn1_1,
      'ctrl_txt_youarestatus_001': instance.ctrl_txt_youarestatus_001,
      'ctrl_txt_yourmembershipyearreset_001':
          instance.ctrl_txt_yourmembershipyearreset_001,
      'ctrl_txt_youhavedaysleft_001': instance.ctrl_txt_youhavedaysleft_001,
      'ctrl_txt_moreonvitalitystatus_001':
          instance.ctrl_txt_moreonvitalitystatus_001,
      'ctrl_txt_atthestartofeverymonth_001':
          instance.ctrl_txt_atthestartofeverymonth_001,
      'ctrl_txt_achievethehighestvitalitystatus_001':
          instance.ctrl_txt_achievethehighestvitalitystatus_001,
      'ctrl_txt_whyitsimportant_001': instance.ctrl_txt_whyitsimportant_001,
      'ctrl_txt_yourvitalitystatusisameasure_001':
          instance.ctrl_txt_yourvitalitystatusisameasure_001,
      'ctrl_txt_howstatusworks_001': instance.ctrl_txt_howstatusworks_001,
      'ctrl_txt_howvitalitystatusworks_001':
          instance.ctrl_txt_howvitalitystatusworks_001,
      'ctrl_txt_duringthistime_001': instance.ctrl_txt_duringthistime_001,
      'ctrl_txt_atthestartofeachmembershipyear_001':
          instance.ctrl_txt_atthestartofeachmembershipyear_001,
      'ctrl_txt_maintainorearnhigherrewards_001':
          instance.ctrl_txt_maintainorearnhigherrewards_001,
      'ctrl_txt_yourvitalitystatusmeasure_001':
          instance.ctrl_txt_yourvitalitystatusmeasure_001,
      'txtrewards2047': instance.txtrewards2047,
      'txtrewards2048': instance.txtrewards2048,
      'txtrewards2049': instance.txtrewards2049,
      'txtrewards2050': instance.txtrewards2050,
      'txtrewards2051': instance.txtrewards2051,
      'iconrewards2047': instance.iconrewards2047,
      'iconrewards2048': instance.iconrewards2048,
      'iconrewards2049': instance.iconrewards2049,
      'iconrewards2050': instance.iconrewards2050,
      'iconrewards2051': instance.iconrewards2051,
      'txt_no_history_001': instance.txt_no_history_001,
      'txt_hf_cashback_001': instance.txt_hf_cashback_001,
      'txt_hf_history_avail_001': instance.txt_hf_history_avail_001,
      'txt_no_cashback_earned_001': instance.txt_no_cashback_earned_001,
      'txt_perc_cashback_spend_001': instance.txt_perc_cashback_spend_001,
      'txt_earned_cashback_001': instance.txt_earned_cashback_001,
      'tc_no_history_available': instance.tc_no_history_available,
      'tc_last_week_empty_list_msg': instance.tc_last_week_empty_list_msg,
      'tc_active_challenge_starts_next_monday':
          instance.tc_active_challenge_starts_next_monday,
      'tc_goals_met': instance.tc_goals_met,
      'tc_points_earned': instance.tc_points_earned,
      'tc_unknown_error': instance.tc_unknown_error,
      'tc_error_occurred_try_again': instance.tc_error_occurred_try_again,
      'tc_Check_out_this_link': instance.tc_Check_out_this_link,
      'tc_you': instance.tc_you,
      'dt_format_day_date_short_month': instance.dt_format_day_date_short_month,
      'dt_format_day_date_short_month_full_year':
          instance.dt_format_day_date_short_month_full_year,
      'txt_maintainpoinstatus_001': instance.txt_maintainpoinstatus_001,
      'question_input_hint_102453': instance.question_input_hint_102453,
      'txt_systolic': instance.txt_systolic,
      'txt_dialostolic': instance.txt_dialostolic,
      'txt_feet': instance.txt_feet,
      'txt_inch': instance.txt_inch,
      'txt_range_001': instance.txt_range_001,
      'apps_devices_sync_date_format': instance.apps_devices_sync_date_format,
      'dt_format_date_short_month_full_year_twenty_four_hours_mins':
          instance.dt_format_date_short_month_full_year_twenty_four_hours_mins,
      'dt_format_date_short_month_full_year_twelve_hours_mins':
          instance.dt_format_date_short_month_full_year_twelve_hours_mins,
      'txtbody_maxfile_006': instance.txtbody_maxfile_006,
      'txt_points_limit': instance.txt_points_limit,
      'txt_points_limit_description_001':
          instance.txt_points_limit_description_001,
      'dialog_body_changingpassword_001':
          instance.dialog_body_changingpassword_001,
      'dialog_hd_changepassword_001': instance.dialog_hd_changepassword_001,
      'dialog_btn2_reenterpassword_001':
          instance.dialog_btn2_reenterpassword_001,
      'dialog_btn1_changepassword_001': instance.dialog_btn1_changepassword_001,
      'txtblue_subtext_001_individual': instance.txtblue_subtext_001_individual,
      'txtblue_subtext_001_sme': instance.txtblue_subtext_001_sme,
      'txtbronze_subtext_001_individual':
          instance.txtbronze_subtext_001_individual,
      'txtbronze_subtext_002_individual':
          instance.txtbronze_subtext_002_individual,
      'txtbronze_subtext_001_sme': instance.txtbronze_subtext_001_sme,
      'txtbronze_subtext_002_sme': instance.txtbronze_subtext_002_sme,
      'txtbronze_subtext_001_ind_sme': instance.txtbronze_subtext_001_ind_sme,
      'txtbronze_subtext_002_ind_sme': instance.txtbronze_subtext_002_ind_sme,
      'txtsilver_subtext_001_individual':
          instance.txtsilver_subtext_001_individual,
      'txtsilver_subtext_002_individual':
          instance.txtsilver_subtext_002_individual,
      'txtsilver_subtext_001_sme': instance.txtsilver_subtext_001_sme,
      'txtsilver_subtext_002_sme': instance.txtsilver_subtext_002_sme,
      'txtsilver_subtext_001_ind_sme': instance.txtsilver_subtext_001_ind_sme,
      'txtsilver_subtext_002_ind_sme': instance.txtsilver_subtext_002_ind_sme,
      'txtgold_subtext_001_individual': instance.txtgold_subtext_001_individual,
      'txtgold_subtext_002_individual': instance.txtgold_subtext_002_individual,
      'txtgold_subtext_001_sme': instance.txtgold_subtext_001_sme,
      'txtgold_subtext_002_sme': instance.txtgold_subtext_002_sme,
      'txtgold_subtext_001_ind_sme': instance.txtgold_subtext_001_ind_sme,
      'txtgold_subtext_002_ind_sme': instance.txtgold_subtext_002_ind_sme,
      'txtplatinum_subtext_001_individual':
          instance.txtplatinum_subtext_001_individual,
      'txtplatinum_subtext_002_individual':
          instance.txtplatinum_subtext_002_individual,
      'txtplatinum_subtext_001_sme': instance.txtplatinum_subtext_001_sme,
      'txtplatinum_subtext_002_sme': instance.txtplatinum_subtext_002_sme,
      'txtplatinum_subtext_001_ind_sme':
          instance.txtplatinum_subtext_001_ind_sme,
      'txtplatinum_subtext_002_ind_sme':
          instance.txtplatinum_subtext_002_ind_sme,
      'link_hf_supermaxi_001': instance.link_hf_supermaxi_001,
      'txt_waiting_001': instance.txt_waiting_001,
      'txt_try_again_001': instance.txt_try_again_001,
      'tc_Sticker_history': instance.tc_Sticker_history,
      'tc__only_the_10_latest_stickers_will_be_displayed':
          instance.tc__only_the_10_latest_stickers_will_be_displayed,
      'tc_sent_by': instance.tc_sent_by,
      'tc_send_sticker': instance.tc_send_sticker,
      'tc_sticker_sent': instance.tc_sticker_sent,
      'two_factor_txtinvalidnumber_001':
          instance.two_factor_txtinvalidnumber_001,
      'two_factor_txtmethodemail': instance.two_factor_txtmethodemail,
      'two_factor_txtmethodtext': instance.two_factor_txtmethodtext,
      'txtgooglefitunabletoconnect_001':
          instance.txtgooglefitunabletoconnect_001,
      'txtgooglefitunabletoconnectcopy_001':
          instance.txtgooglefitunabletoconnectcopy_001,
      'ctrl_btncancelconnection_001': instance.ctrl_btncancelconnection_001,
      'ctrl_btnopensettings_001': instance.ctrl_btnopensettings_001,
      'txtpermissionrequired_001': instance.txtpermissionrequired_001,
      'txtpermissionrequiredcopy_006': instance.txtpermissionrequiredcopy_006,
      'ctrl_btnskipfornow_001': instance.ctrl_btnskipfornow_001,
      'txtpermissionrequiredcopy_005': instance.txtpermissionrequiredcopy_005,
      'txtgooglefitwillbedisconnected_001':
          instance.txtgooglefitwillbedisconnected_001,
      'txtgooglefitwillbedisconnectedcopy_001':
          instance.txtgooglefitwillbedisconnectedcopy_001,
      'ctrl_btnback_001': instance.ctrl_btnback_001,
      'txtpermissionrequiredcopy_001': instance.txtpermissionrequiredcopy_001,
      'txtpermissionrequiredcopy_002': instance.txtpermissionrequiredcopy_002,
      'btn_viewreawards_001': instance.btn_viewreawards_001,
      'txtwelcometoanewmembershipyear_001':
          instance.txtwelcometoanewmembershipyear_001,
      'txtyourvitalitypointshavebeenreset_001':
          instance.txtyourvitalitypointshavebeenreset_001,
      'txtstatus_silver': instance.txtstatus_silver,
      'txtstatus_gold': instance.txtstatus_gold,
      'txtstatus_platinum': instance.txtstatus_platinum,
      'txtstatus_bronze': instance.txtstatus_bronze,
      'txtstatus_new_status': instance.txtstatus_new_status,
      'ctrl_btnviewrewards_001': instance.ctrl_btnviewrewards_001,
      'question_text_box_hint_10034': instance.question_text_box_hint_10034,
      'question_text_box_hint_10033': instance.question_text_box_hint_10033,
      'question_text_box_hint_10040': instance.question_text_box_hint_10040,
      'question_text_box_hint_10042': instance.question_text_box_hint_10042,
      'question_text_box_hint_10045': instance.question_text_box_hint_10045,
      'question_text_box_hint_10013': instance.question_text_box_hint_10013,
      'question_text_box_hint_10046': instance.question_text_box_hint_10046,
      'question_text_box_hint_10059': instance.question_text_box_hint_10059,
      'question_text_box_hint_10211': instance.question_text_box_hint_10211,
      'question_text_box_hint_10063': instance.question_text_box_hint_10063,
      'question_text_box_hint_10062': instance.question_text_box_hint_10062,
      'question_input_hint_100103': instance.question_input_hint_100103,
      'question_input_hint_100117': instance.question_input_hint_100117,
      'question_input_hint_100122': instance.question_input_hint_100122,
      'question_input_hint_100125': instance.question_input_hint_100125,
      'question_input_hint_100129': instance.question_input_hint_100129,
      'question_text_box_hint_10032': instance.question_text_box_hint_10032,
      'txtwhatsappchat_001': instance.txtwhatsappchat_001,
      'txtchatdialogsubtext_002': instance.txtchatdialogsubtext_002,
      'txtcontactnumber_001': instance.txtcontactnumber_001,
      'ctrl_btnemail_001': instance.ctrl_btnemail_001,
      'ctrl_btnsendfeedback_001': instance.ctrl_btnsendfeedback_001,
      'ctrl_btnstartwhatsappchat_001': instance.ctrl_btnstartwhatsappchat_001,
      'txt_foot_note': instance.txt_foot_note,
      'ctrl_txt_onlysendareport_ios_001':
          instance.ctrl_txt_onlysendareport_ios_001,
      'vj_cm': instance.vj_cm,
      'txt_profile_header_points_001': instance.txt_profile_header_points_001,
      'ctrl_btncontactnumber_001': instance.ctrl_btncontactnumber_001,
      'earn_max_coins_001': instance.earn_max_coins_001,
      'earn_max_coins_term_001': instance.earn_max_coins_term_001,
      'txtpointstowardsgoal_001': instance.txtpointstowardsgoal_001,
      'txtgoal_achieved_001': instance.txtgoal_achieved_001,
      'txtpointstowardsgoal_002': instance.txtpointstowardsgoal_002,
      'txttowardsgoalbreakdown_001': instance.txttowardsgoalbreakdown_001,
      'txt_membership_name_vitalityactivefull':
          instance.txt_membership_name_vitalityactivefull,
      'txt_membership_name_vitalityactivelite':
          instance.txt_membership_name_vitalityactivelite,
      'txt_membership_name_individual': instance.txt_membership_name_individual,
      'txt_membership_name_sme': instance.txt_membership_name_sme,
      'txt_membership_name_ind_sme': instance.txt_membership_name_ind_sme,
      'txtpointstowardsstatus_001': instance.txtpointstowardsstatus_001,
      'hc_askchatbot': instance.hc_askchatbot,
      'hc_faqs': instance.hc_faqs,
      'hc_new_bulletin_board': instance.hc_new_bulletin_board,
      'hc_video_tutorials_on_app_operation':
          instance.hc_video_tutorials_on_app_operation,
      'hc_Vitality_special_site': instance.hc_Vitality_special_site,
      'hc_users_guide_to_benefits': instance.hc_users_guide_to_benefits,
      'hc_terms_and_conditions': instance.hc_terms_and_conditions,
      'hc_apply_for_steps_points': instance.hc_apply_for_steps_points,
      'hc_send_inquiry_form': instance.hc_send_inquiry_form,
      'txtwerehavingtroubleconnectingyou_001':
          instance.txtwerehavingtroubleconnectingyou_001,
      'txtunabletoconnecttopartnerapp_001':
          instance.txtunabletoconnecttopartnerapp_001,
      'txtissueconnectingtopartnerapp_001':
          instance.txtissueconnectingtopartnerapp_001,
      'txtwhatsapp': instance.txtwhatsapp,
      'oldwheel_txt15creditshavebeendepositedinyourcygmall_001':
          instance.oldwheel_txt15creditshavebeendepositedinyourcygmall_001,
      'txttime005': instance.txttime005,
      'book_now': instance.book_now,
      'watch_videos': instance.watch_videos,
      'txtmailapplication': instance.txtmailapplication,
      'txtalertcouldntprocessyourgiftcardselection_001':
          instance.txtalertcouldntprocessyourgiftcardselection_001,
      'txt_invalidcodeerror_001': instance.txt_invalidcodeerror_001,
      'vj_update_stride_length': instance.vj_update_stride_length,
      'vj_you_done_it': instance.vj_you_done_it,
      'vj_well_done': instance.vj_well_done,
      'vj_just_collected': instance.vj_just_collected,
      'vj_next_destination': instance.vj_next_destination,
      'vj_km': instance.vj_km,
      'txt_nopointsearningactivity_002':
          instance.txt_nopointsearningactivity_002,
      'txt_points_002': instance.txt_points_002,
      'txt_day_001': instance.txt_day_001,
      'txt_days_001': instance.txt_days_001,
      'txt_uploaderror_001': instance.txt_uploaderror_001,
      'txt_mobilenumber_001': instance.txt_mobilenumber_001,
      'txt_message_001': instance.txt_message_001,
      'txt_typehere_001': instance.txt_typehere_001,
      'txt_cannottbeempty_001': instance.txt_cannottbeempty_001,
      'txt_feedback_submission_failure_001':
          instance.txt_feedback_submission_failure_001,
      'txt_feedback_001': instance.txt_feedback_001,
      'txtpoints_009': instance.txtpoints_009,
      'txt_monthly_hf_cashback_001': instance.txt_monthly_hf_cashback_001,
      'txt_progress_001': instance.txt_progress_001,
      'txt_earned_sofar_001': instance.txt_earned_sofar_001,
      'txt_no_cashback_earned_002': instance.txt_no_cashback_earned_002,
      'txt_your_insurance_reward_001': instance.txt_your_insurance_reward_001,
      'txt_unlock_cashback_001': instance.txt_unlock_cashback_001,
      'txtinvalidquickguidedata': instance.txtinvalidquickguidedata,
      'txthealthpartners': instance.txthealthpartners,
      'txttroubleshootingreportsent_001':
          instance.txttroubleshootingreportsent_001,
      'txttroubleshootingreportsent_002':
          instance.txttroubleshootingreportsent_002,
      'txt_label_off': instance.txt_label_off,
      'txt_label_on': instance.txt_label_on,
      'ha_tracking_date_format': instance.ha_tracking_date_format,
      'pag_history_list_day_date_format':
          instance.pag_history_list_day_date_format,
      'pag_tracking_header_date_format':
          instance.pag_tracking_header_date_format,
      'pag_activation_modal_started_date_format':
          instance.pag_activation_modal_started_date_format,
      'txt_biometric_note_001': instance.txt_biometric_note_001,
      'mha_tracking_next_available_date':
          instance.mha_tracking_next_available_date,
      'appdevices_strava_subtitle': instance.appdevices_strava_subtitle,
      'appdevices_withings_subtitle': instance.appdevices_withings_subtitle,
      'appdevices_fitbit_subtitle': instance.appdevices_fitbit_subtitle,
      'appdevices_garmin_subtitle': instance.appdevices_garmin_subtitle,
      'appdevices_softbank_subtitle': instance.appdevices_softbank_subtitle,
      'appdevices_polar_subtitle': instance.appdevices_polar_subtitle,
      'appdevices_suunto_subtitle': instance.appdevices_suunto_subtitle,
      'appdevices_omron_subtitle': instance.appdevices_omron_subtitle,
      'appdevices_19_subtitle': instance.appdevices_19_subtitle,
      'txtupdateyourapp_001': instance.txtupdateyourapp_001,
      'txtsoftupdate_001': instance.txtsoftupdate_001,
      'txtforcedupdate_001': instance.txtforcedupdate_001,
      'txtblacklisting_001': instance.txtblacklisting_001,
      'ctrl_btnlater_001': instance.ctrl_btnlater_001,
      'ctrl_btnupdatenow_001': instance.ctrl_btnupdatenow_001,
      'ctrl_btncloseapp_001': instance.ctrl_btncloseapp_001,
      'vhc_date_format': instance.vhc_date_format,
      'tc_error_email_max_length': instance.tc_error_email_max_length,
      'txt_we_updated_your_biometric': instance.txt_we_updated_your_biometric,
      'chip_cashbacklite_fd_001': instance.chip_cashbacklite_fd_001,
      'levels_cashbackptslite_range': instance.levels_cashbackptslite_range,
      'levels_cashbackptslite_range_last':
          instance.levels_cashbackptslite_range_last,
      'genericfd_cashbacklite_1': instance.genericfd_cashbacklite_1,
      'genericfd_cashbacklite_2': instance.genericfd_cashbacklite_2,
      'genericfd_cashbacklite_3': instance.genericfd_cashbacklite_3,
      'genericfd_cashbacklite_4': instance.genericfd_cashbacklite_4,
      'genericfd_circlecontentlite_1': instance.genericfd_circlecontentlite_1,
      'genericfd_circlecontentlite_2': instance.genericfd_circlecontentlite_2,
      'genericfd_circlecontentlite_3': instance.genericfd_circlecontentlite_3,
      'genericfd_circlecontentlite_4': instance.genericfd_circlecontentlite_4,
      'txtuptoXcashbacklite_001': instance.txtuptoXcashbacklite_001,
      'txtuptoXcashback_genp_001': instance.txtuptoXcashback_genp_001,
      'txt_generic_prod_001': instance.txt_generic_prod_001,
      'tfa_dialog_btn2_leave': instance.tfa_dialog_btn2_leave,
      'tfa_dialog_btn2_retry': instance.tfa_dialog_btn2_retry,
      'tfa_dialog_code_attempts_content':
          instance.tfa_dialog_code_attempts_content,
      'tfa_dialog_code_attempts_title': instance.tfa_dialog_code_attempts_title,
      'tfa_dialog_disable_content': instance.tfa_dialog_disable_content,
      'tfa_dialog_disable_title': instance.tfa_dialog_disable_title,
      'tfa_dialog_incorrect_password_content':
          instance.tfa_dialog_incorrect_password_content,
      'tfa_dialog_incorrect_password_title':
          instance.tfa_dialog_incorrect_password_title,
      'tfa_dialog_unsaved_changes_content':
          instance.tfa_dialog_unsaved_changes_content,
      'tfa_dialog_unsaved_changes_title':
          instance.tfa_dialog_unsaved_changes_title,
      'txtsummary': instance.txtsummary,
      'txt_calculating': instance.txt_calculating,
      'txtquestioncount_001': instance.txtquestioncount_001,
      'txtpacemanagementheader_myhealth_001':
          instance.txtpacemanagementheader_myhealth_001,
      'txtpacemanagementsubtext_myhealth_001':
          instance.txtpacemanagementsubtext_myhealth_001,
      'txtpacemanagementheader_activityes_002':
          instance.txtpacemanagementheader_activityes_002,
      'txtpacemanagementsubtext_activityes_002':
          instance.txtpacemanagementsubtext_activityes_002,
      'chip_cashbacklite_amount_001': instance.chip_cashbacklite_amount_001,
      'gab_btncollapsebarcode_001': instance.gab_btncollapsebarcode_001,
      'gab_btnexpandbarcode_001': instance.gab_btnexpandbarcode_001,
      'gab_txtfullname_001': instance.gab_txtfullname_001,
      'gab_txtgymactivationbarcode_001':
          instance.gab_txtgymactivationbarcode_001,
      'gab_txtpartyid_001': instance.gab_txtpartyid_001,
      'txtuptoxperc_cashbacklite_001': instance.txtuptoxperc_cashbacklite_001,
      'Total_cashbacklite_earned_001': instance.Total_cashbacklite_earned_001,
      'total_coinslite_earned_001': instance.total_coinslite_earned_001,
      'gds_txt_stamping_title_001': instance.gds_txt_stamping_title_001,
      'gds_txt_stamping_desc_001': instance.gds_txt_stamping_desc_001,
      'gds_txt_stamping_desc_002': instance.gds_txt_stamping_desc_002,
      'gds_txt_register_title_001': instance.gds_txt_register_title_001,
      'gds_txt_register_title_002': instance.gds_txt_register_title_002,
      'gds_txt_register_desc_001': instance.gds_txt_register_desc_001,
      'gds_txt_register_desc_002': instance.gds_txt_register_desc_002,
      'gds_btn_register_001': instance.gds_btn_register_001,
      'gds_btn_register_002': instance.gds_btn_register_002,
      'gds_txt_error_title_001': instance.gds_txt_error_title_001,
      'gds_txt_error_desc_001': instance.gds_txt_error_desc_001,
      'appdevices_18_title': instance.appdevices_18_title,
      'appdevices_19_title': instance.appdevices_19_title,
      'appdevices_39_title': instance.appdevices_39_title,
      'appdevices_98_title': instance.appdevices_98_title,
      'appdevices_98_subtitle': instance.appdevices_98_subtitle,
      'appdevices_18_subtitle': instance.appdevices_18_subtitle,
      'appdevices_39_subtitle': instance.appdevices_39_subtitle,
      'ExternalLinkReference': instance.ExternalLinkReference,
      'txt_feeds_assessments_key_001': instance.txt_feeds_assessments_key_001,
      'txt_feeds_prevention_key_001': instance.txt_feeds_prevention_key_001,
      'txt_feeds_healthcheck_key_001': instance.txt_feeds_healthcheck_key_001,
      'expedia_apply_code': instance.expedia_apply_code,
      'expedia_booking_cancelled': instance.expedia_booking_cancelled,
      'expedia_booking_confirmed': instance.expedia_booking_confirmed,
      'expedia_booking_detail': instance.expedia_booking_detail,
      'expedia_booking_detail_blue_status':
          instance.expedia_booking_detail_blue_status,
      'expedia_booking_detail_booking_status':
          instance.expedia_booking_detail_booking_status,
      'expedia_booking_detail_bronze_status':
          instance.expedia_booking_detail_bronze_status,
      'expedia_booking_detail_date_booked':
          instance.expedia_booking_detail_date_booked,
      'expedia_booking_detail_discount_applied':
          instance.expedia_booking_detail_discount_applied,
      'expedia_booking_detail_discount_value':
          instance.expedia_booking_detail_discount_value,
      'expedia_booking_detail_gold_status':
          instance.expedia_booking_detail_gold_status,
      'expedia_booking_detail_hotel_stay':
          instance.expedia_booking_detail_hotel_stay,
      'expedia_booking_detail_itinerary_number':
          instance.expedia_booking_detail_itinerary_number,
      'expedia_booking_detail_no_status':
          instance.expedia_booking_detail_no_status,
      'expedia_booking_detail_platinum_status':
          instance.expedia_booking_detail_platinum_status,
      'expedia_booking_detail_silver_status':
          instance.expedia_booking_detail_silver_status,
      'expedia_booking_history': instance.expedia_booking_history,
      'expedia_checkout': instance.expedia_checkout,
      'expedia_code_copy': instance.expedia_code_copy,
      'expedia_code_discount_code': instance.expedia_code_discount_code,
      'expedia_code_error_txt': instance.expedia_code_error_txt,
      'expedia_code_expires': instance.expedia_code_expires,
      'expedia_code_footnote_to_remember':
          instance.expedia_code_footnote_to_remember,
      'expedia_code_percent_discount': instance.expedia_code_percent_discount,
      'expedia_code_title': instance.expedia_code_title,
      'expedia_code_to_redeem_reward': instance.expedia_code_to_redeem_reward,
      'expedia_code_visit_website': instance.expedia_code_visit_website,
      'expedia_data_privacy_disagree_dialog_message':
          instance.expedia_data_privacy_disagree_dialog_message,
      'expedia_landing_available_until':
          instance.expedia_landing_available_until,
      'expedia_landing_book_button': instance.expedia_landing_book_button,
      'expedia_landing_bookings_per_year':
          instance.expedia_landing_bookings_per_year,
      'expedia_landing_discount_codes_used':
          instance.expedia_landing_discount_codes_used,
      'expedia_landing_landing_tap_to_book':
          instance.expedia_landing_landing_tap_to_book,
      'expedia_landing_percent_discount':
          instance.expedia_landing_percent_discount,
      'expedia_landing_title': instance.expedia_landing_title,
      'expedia_reserve_hotel': instance.expedia_reserve_hotel,
      'expedia_rewards_expedia_discount':
          instance.expedia_rewards_expedia_discount,
      'expedia_rewards_expedia_nights_stay':
          instance.expedia_rewards_expedia_nights_stay,
      'expedia_you_saved_on_hotel': instance.expedia_you_saved_on_hotel,
      'expedia_bookings_used': instance.expedia_bookings_used,
      'expedia_code_copied': instance.expedia_code_copied,
      'txtexpedia_001': instance.txtexpedia_001,
      'txtdiscount_001': instance.txtdiscount_001,
      'txtusethiscodetoredeem_001': instance.txtusethiscodetoredeem_001,
      'txtremembertotaponusecoupon_001':
          instance.txtremembertotaponusecoupon_001,
      'txtdiscountcodewillnotworkfoxexpedia_001':
          instance.txtdiscountcodewillnotworkfoxexpedia_001,
      'txtbookonexpedia_001': instance.txtbookonexpedia_001,
      'txtcoins_002': instance.txtcoins_002,
      'txtgiftcard_002': instance.txtgiftcard_002,
      'txt_hf_AppExpFeedback_001': instance.txt_hf_AppExpFeedback_001,
      'txt_hf_GeneralFeedback_001': instance.txt_hf_GeneralFeedback_001,
      'txt_hf_TechnicalFeedback_001': instance.txt_hf_TechnicalFeedback_001,
      'ctrl_btnviewdiscountcode_001': instance.ctrl_btnviewdiscountcode_001,
      'txtreservehotel_001': instance.txtreservehotel_001,
      'txtgotocheckout_001': instance.txtgotocheckout_001,
      'txtapplycode_001': instance.txtapplycode_001,
      'txtthisweeksprogress_001': instance.txtthisweeksprogress_001,
      'txtnopointsearnedthisweek_001': instance.txtnopointsearnedthisweek_001,
      'txtouchid': instance.txtouchid,
      'txfingerprint': instance.txfingerprint,
      'txtface': instance.txtface,
      'txtfaceid': instance.txtfaceid,
      'txtunknown': instance.txtunknown,
      'gab_btnviewgymactivationbarcode_001':
          instance.gab_btnviewgymactivationbarcode_001,
      'txtstreaksandmilestones_001': instance.txtstreaksandmilestones_001,
      'txtnew_001': instance.txtnew_001,
      'txtgoalstreaks_001': instance.txtgoalstreaks_001,
      'txtnomilestones_001': instance.txtnomilestones_001,
      'txtweeks_001': instance.txtweeks_001,
      'txtrecentmilestone_001': instance.txtrecentmilestone_001,
      'txtcurrentstreak_001': instance.txtcurrentstreak_001,
      'txt_participating_gyms': instance.txt_participating_gyms,
      'txtpointsmaynotreflect_immediately_snv_001':
          instance.txtpointsmaynotreflect_immediately_snv_001,
      'txtpointsmaynotreflect_immediately_vhc_001':
          instance.txtpointsmaynotreflect_immediately_vhc_001,
      'txtphysicalactivitygoalstreaks_001':
          instance.txtphysicalactivitygoalstreaks_001,
      'txtmilestone_001': instance.txtmilestone_001,
      'txtmilestones_001': instance.txtmilestones_001,
      'txtachieved_001': instance.txtachieved_001,
      'txthowgoalstreakswork_001': instance.txthowgoalstreakswork_001,
      'txtnextmilestones_001': instance.txtnextmilestones_001,
      'txtallmilestones_001': instance.txtallmilestones_001,
      'txthitmilestones_001': instance.txthitmilestones_001,
      'txtreachedmilestones_001': instance.txtreachedmilestones_001,
      'txtachievinggoalstreak_001': instance.txtachievinggoalstreak_001,
      'txtmilestones_002': instance.txtmilestones_002,
      'txtsharemilestones_001': instance.txtsharemilestones_001,
      'txtmilestonesachieved_001': instance.txtmilestonesachieved_001,
      'txtmilestoneheader_001': instance.txtmilestoneheader_001,
      'txtlongeststreak_001': instance.txtlongeststreak_001,
      'txtachievedstreakmilestones_001':
          instance.txtachievedstreakmilestones_001,
      'txtcompletemilestones_001': instance.txtcompletemilestones_001,
      'txtbottomsheetcontent_001': instance.txtbottomsheetcontent_001,
      'txtrewards2045': instance.txtrewards2045,
      'txtrewards2046': instance.txtrewards2046,
      'iconrewards2046': instance.iconrewards2046,
      'iconrewards2045': instance.iconrewards2045,
      'txtcouldntupdatepulldown_001': instance.txtcouldntupdatepulldown_001,
      'txt_archive_gift_cards': instance.txt_archive_gift_cards,
      'txt_archive_popup_description': instance.txt_archive_popup_description,
      'vcrm_app_bar_title_001': instance.vcrm_app_bar_title_001,
      'txt_showmore_001': instance.txt_showmore_001,
      'txt_showless_001': instance.txt_showless_001,
      'txthealthycholesterol_intro_002':
          instance.txthealthycholesterol_intro_002,
      'txthealthguidetipstext_005': instance.txthealthguidetipstext_005,
      'txthealthycholesterol_intro_003':
          instance.txthealthycholesterol_intro_003,
      'snv_ctrl_btnsubmitanotheractivity_001':
          instance.snv_ctrl_btnsubmitanotheractivity_001,
      'snv_txtdelete_001': instance.snv_txtdelete_001,
      'txt_heart_rate_condition_002': instance.txt_heart_rate_condition_002,
      'txtShortcutLinksHeading': instance.txtShortcutLinksHeading,
      'txtifyoudonthavetheapp_001': instance.txtifyoudonthavetheapp_001,
      'ctrl_btnlogintotataaia_001': instance.ctrl_btnlogintotataaia_001,
      'txtenablesharingpermissions_001':
          instance.txtenablesharingpermissions_001,
      'txtdialogbodysharepermisionsinsettings_001':
          instance.txtdialogbodysharepermisionsinsettings_001,
      'ctrl_txtsgotosettings_001': instance.ctrl_txtsgotosettings_001,
      'txtallowcameraaccess_001': instance.txtallowcameraaccess_001,
      'txtdialogbodycamearapermisionsinsettings_001':
          instance.txtdialogbodycamearapermisionsinsettings_001,
      'txt_activity_date_for_membership_year':
          instance.txt_activity_date_for_membership_year,
      'txt_google_account_data_permission_001':
          instance.txt_google_account_data_permission_001,
      'txtlanguage_001': instance.txtlanguage_001,
      'old_wheel_ctrl_btnspinlater_001':
          instance.old_wheel_ctrl_btnspinlater_001,
      'old_wheel_txt_doitlater_header_001':
          instance.old_wheel_txt_doitlater_header_001,
      'old_wheel_txt_doitlater_header_002':
          instance.old_wheel_txt_doitlater_header_002,
      'old_wheel_txt_doitlater_title_001':
          instance.old_wheel_txt_doitlater_title_001,
      'old_wheel_txt_doitlater_title_002':
          instance.old_wheel_txt_doitlater_title_002,
      'old_wheel_txt_doitlater_title_003':
          instance.old_wheel_txt_doitlater_title_003,
      'old_wheel_txt_doitlater_desc_001':
          instance.old_wheel_txt_doitlater_desc_001,
      'old_wheel_txt_doitlater_desc_002':
          instance.old_wheel_txt_doitlater_desc_002,
      'old_wheel_txt_doitlater_desc_003':
          instance.old_wheel_txt_doitlater_desc_003,
      'vg_dc_challenge_not_started': instance.vg_dc_challenge_not_started,
      'vg_dc_commitment_period': instance.vg_dc_commitment_period,
      'vg_dc_garmin_startchallenge_note':
          instance.vg_dc_garmin_startchallenge_note,
      'vg_dc_polar_startchallenge_note':
          instance.vg_dc_polar_startchallenge_note,
      'vg_dc_apple_startchallenge_note':
          instance.vg_dc_apple_startchallenge_note,
      'vg_dc_link_device_text': instance.vg_dc_link_device_text,
      'vg_dc_startchallenge_idle_btn': instance.vg_dc_startchallenge_idle_btn,
      'vg_dc_startchallenge_progress_btn':
          instance.vg_dc_startchallenge_progress_btn,
      'vg_dc_pending_page_header': instance.vg_dc_pending_page_header,
      'vg_dc_purchase_date_text': instance.vg_dc_purchase_date_text,
      'vg_dc_coins_text': instance.vg_dc_coins_text,
      'ctrl_btnsave_001': instance.ctrl_btnsave_001,
      'profile_language_txtheader_001': instance.profile_language_txtheader_001,
      'profile_language_paragraph_001': instance.profile_language_paragraph_001,
      'txt_languageSelection_01': instance.txt_languageSelection_01,
      'txt_languageSelection_02': instance.txt_languageSelection_02,
      'digital_stamp_txt_vitality_points_description':
          instance.digital_stamp_txt_vitality_points_description,
      'digital_stamp_txt_privacy_policy_description':
          instance.digital_stamp_txt_privacy_policy_description,
      'ctrl_txt_credential_breakout': instance.ctrl_txt_credential_breakout,
      'txt_contract_details_001': instance.txt_contract_details_001,
      'androidStoreUrl': instance.androidStoreUrl,
      'iosStoreUrl': instance.iosStoreUrl,
      'txt_participating_gym_automatic_gym_note':
          instance.txt_participating_gym_automatic_gym_note,
      'old_wheel_txt_earn': instance.old_wheel_txt_earn,
      'old_wheel_txt_super_wheel_spin': instance.old_wheel_txt_super_wheel_spin,
      'old_wheel_txt_this_week': instance.old_wheel_txt_this_week,
      'old_wheel_txt_getactive_title': instance.old_wheel_txt_getactive_title,
      'old_wheel_txt_getactive_desc': instance.old_wheel_txt_getactive_desc,
      'old_wheel_txt_reach_your_target_title':
          instance.old_wheel_txt_reach_your_target_title,
      'old_wheel_txt_reach_your_target_desc':
          instance.old_wheel_txt_reach_your_target_desc,
      'old_wheel_txt_rarn_super_wheelspin_title':
          instance.old_wheel_txt_rarn_super_wheelspin_title,
      'old_wheel_txt_rarn_super_wheelspin_desc':
          instance.old_wheel_txt_rarn_super_wheelspin_desc,
      'txtgetyourinsurancediscount_001':
          instance.txtgetyourinsurancediscount_001,
      'txtgetyourinsurancediscountcopy_001':
          instance.txtgetyourinsurancediscountcopy_001,
      'old_wheel_txtsuperwheelspinsubtext_001':
          instance.old_wheel_txtsuperwheelspinsubtext_001,
      'inactive_membership_date_format':
          instance.inactive_membership_date_format,
      'txtstartchallenge_001': instance.txtstartchallenge_001,
      'txt_linkdevice_001': instance.txt_linkdevice_001,
      'vg_registration_pref_email_toggle_title':
          instance.vg_registration_pref_email_toggle_title,
      'vg_registration_pref_email_toggle_description':
          instance.vg_registration_pref_email_toggle_description,
      'device_cashback_effective_from_date_format':
          instance.device_cashback_effective_from_date_format,
      'device_cashback_effective_to_date_format':
          instance.device_cashback_effective_to_date_format,
      'partner_description_223': instance.partner_description_223,
      'partner_description_2029': instance.partner_description_2029,
      'partner_description_2147': instance.partner_description_2147,
      'partner_description_2141': instance.partner_description_2141,
      'partner_description_2069': instance.partner_description_2069,
      'partner_description_2143': instance.partner_description_2143,
      'partner_description_2142': instance.partner_description_2142,
      'unable_to_link_user': instance.unable_to_link_user,
      'pg_txtallocatedsurpriseitemtitle_044':
          instance.pg_txtallocatedsurpriseitemtitle_044,
      'old_wheel_ctrl_btnconfirm_001': instance.old_wheel_ctrl_btnconfirm_001,
      'old_wheel_ctrl_btnvisitwebsite_001':
          instance.old_wheel_ctrl_btnvisitwebsite_001,
      'vg_dc_aw_landing_previous_cycle_text':
          instance.vg_dc_aw_landing_previous_cycle_text,
      'vg_dc_aw_landing_previous_cycle_body':
          instance.vg_dc_aw_landing_previous_cycle_body,
      'ctrl_btnuploadfile_002': instance.ctrl_btnuploadfile_002,
      'txt_login_biometric': instance.txt_login_biometric,
      'txtgympartners_001': instance.txtgympartners_001,
      'txtexerciseathome_001': instance.txtexerciseathome_001,
      'txthealthscreening_001': instance.txthealthscreening_001,
      'txthealthyfood_001': instance.txthealthyfood_001,
      'txthealthygear_001': instance.txthealthygear_001,
      'txtfitnessdevices_001': instance.txtfitnessdevices_001,
      'txtyourdeviceishasbeendelivered_001':
          instance.txtyourdeviceishasbeendelivered_001,
      'txtyourfirstgoalstartson_001': instance.txtyourfirstgoalstartson_001,
      'tc_maximumteams_error_001': instance.tc_maximumteams_error_001,
      'tc_sticker_history': instance.tc_sticker_history,
      'tc_txtnodata_001': instance.tc_txtnodata_001,
      'tc_btngotit': instance.tc_btngotit,
      'pag_fitbit_reminder_dialog_title':
          instance.pag_fitbit_reminder_dialog_title,
      'pag_fitbit_reminder_dialog_body':
          instance.pag_fitbit_reminder_dialog_body,
      'pag_fitbit_reminder_dialog_button_do_no_show_again':
          instance.pag_fitbit_reminder_dialog_button_do_no_show_again,
      'txtupdateyouros_001': instance.txtupdateyouros_001,
      'txtupdateyouros_002': instance.txtupdateyouros_002,
      'txtsoftosupdate_001': instance.txtsoftosupdate_001,
      'txtforcedosupdate_001': instance.txtforcedosupdate_001,
      'ctrl_btnupdatelater_001': instance.ctrl_btnupdatelater_001,
      'tc_team_members': instance.tc_team_members,
      'tc_extend': instance.tc_extend,
      'vj_format_date_compact': instance.vj_format_date_compact,
      'ha_metric_20_result_value_1': instance.ha_metric_20_result_value_1,
      'ha_metric_20_result_value_3': instance.ha_metric_20_result_value_3,
      'urlReinstatedMem': instance.urlReinstatedMem,
      'chip_sleep': instance.chip_sleep,
      'chip_meditation': instance.chip_meditation,
      'txtsupportedapporwearableapp': instance.txtsupportedapporwearableapp,
      'txthealthconnect': instance.txthealthconnect,
      'txt_app_iphone_001': instance.txt_app_iphone_001,
      'txt_app_android_001': instance.txt_app_android_001,
      'txt_device_001': instance.txt_device_001,
      'txt_help_center_forms': instance.txt_help_center_forms,
      'onboarding_modalactivated_txtthebestway_001':
          instance.onboarding_modalactivated_txtthebestway_001,
      'onboarding_modalactivated_txtthebestway_002':
          instance.onboarding_modalactivated_txtthebestway_002,
      'pag_fitbit_not_install_dialog_title':
          instance.pag_fitbit_not_install_dialog_title,
      'pag_fitbit_not_install_dialog_body':
          instance.pag_fitbit_not_install_dialog_body,
      'pag_fitbit_not_install_dialog_button_skip':
          instance.pag_fitbit_not_install_dialog_button_skip,
      'pag_fitbit_not_install_dialog_button_download':
          instance.pag_fitbit_not_install_dialog_button_download,
      'onboarding_modalactivated_txtthebestway_001_ios':
          instance.onboarding_modalactivated_txtthebestway_001_ios,
      'vj_txt_bonus_001': instance.vj_txt_bonus_001,
      'partner_description_2345': instance.partner_description_2345,
      'txtgotohealthconnect': instance.txtgotohealthconnect,
      'file_extension_manipulated_error':
          instance.file_extension_manipulated_error,
      'file_format_recognise_error': instance.file_format_recognise_error,
      'file_upload_error': instance.file_upload_error,
      'file_unable_upload_error': instance.file_unable_upload_error,
      'txtconnectyourmobilephonetofitbit':
          instance.txtconnectyourmobilephonetofitbit,
      'txttrackstepsfromyourmobilephone':
          instance.txttrackstepsfromyourmobilephone,
      'txtmaybelater': instance.txtmaybelater,
      'txtgotofitbit': instance.txtgotofitbit,
      'txtinsurername': instance.txtinsurername,
      'expedia_off_001': instance.expedia_off_001,
      'tc_chip_steps': instance.tc_chip_steps,
      'status_detail_modal_date_format':
          instance.status_detail_modal_date_format,
      'vj_connection_error': instance.vj_connection_error,
      'vj_cancel': instance.vj_cancel,
      'vj_try_again': instance.vj_try_again,
      'vj_error_message': instance.vj_error_message,
      'teamchallenges_team_left_success':
          instance.teamchallenges_team_left_success,
      'teamchallenges_team_deleted_success':
          instance.teamchallenges_team_deleted_success,
      'txt_username_001': instance.txt_username_001,
      'txt_forgot_username_001': instance.txt_forgot_username_001,
      'txt_contact_us_001': instance.txt_contact_us_001,
      'txt_privacy_policy_001': instance.txt_privacy_policy_001,
      'txt_privacy_policy_cannot_continue_001':
          instance.txt_privacy_policy_cannot_continue_001,
      'txt_terms_and_conditions_required_001':
          instance.txt_terms_and_conditions_required_001,
      'txtsupportedapp_001': instance.txtsupportedapp_001,
      'txtsleeptrackingdevice_001': instance.txtsleeptrackingdevice_001,
      'txtinsurer_001': instance.txtinsurer_001,
      'txtfirstdaynextdaycheckingoal_002':
          instance.txtfirstdaynextdaycheckingoal_002,
      'ha_section_Iconkey_121': instance.ha_section_Iconkey_121,
      'txt_youearnedyourfirstpoints_001':
          instance.txt_youearnedyourfirstpoints_001,
      'txt_morewaystoearn_001': instance.txt_morewaystoearn_001,
      'txt_heart_rate_condition_003': instance.txt_heart_rate_condition_003,
      'dialog_hd_asg': instance.dialog_hd_asg,
      'dialog_body_asg': instance.dialog_body_asg,
      'txtlinkafitnessdevice_001': instance.txtlinkafitnessdevice_001,
      'txtbodydeviceapplink_002': instance.txtbodydeviceapplink_002,
      'milestone_list_config_001': instance.milestone_list_config_001,
      'txtautotrackeddevice_001': instance.txtautotrackeddevice_001,
      'txtunlockthisreward_001': instance.txtunlockthisreward_001,
      'txtunlocktvitalityhealth_001': instance.txtunlocktvitalityhealth_001,
      'txtvitalityhealthreview_001': instance.txtvitalityhealthreview_001,
      'txtcodesrevealed_001': instance.txtcodesrevealed_001,
      'txtincreaserewardstatus_001': instance.txtincreaserewardstatus_001,
      'txtincreasecodevalue_001': instance.txtincreasecodevalue_001,
      'txtgetamonthlycode_001': instance.txtgetamonthlycode_001,
      'txtdiscount15_001': instance.txtdiscount15_001,
      'txtdiscount20_001': instance.txtdiscount20_001,
      'txtdiscount25_001': instance.txtdiscount25_001,
      'txtdiscount30_001': instance.txtdiscount30_001,
      'txtsmartscale_001': instance.txtsmartscale_001,
      'txt_type_username_password_001': instance.txt_type_username_password_001,
      'txt_select_login_pref_001': instance.txt_select_login_pref_001,
      'txtbminote_001': instance.txtbminote_001,
      'ctrl_update_001': instance.ctrl_update_001,
      'txt_bmi_condition_001': instance.txt_bmi_condition_001,
      'txt_bmi_condition_002': instance.txt_bmi_condition_002,
      'txt_contactus_description_security':
          instance.txt_contactus_description_security,
      'txt_ourdetails_001': instance.txt_ourdetails_001,
      'txt_telephone_001': instance.txt_telephone_001,
      'txt_footer_contactus_001': instance.txt_footer_contactus_001,
      'txt_contactus_availability_001': instance.txt_contactus_availability_001,
      'txtadidas_001': instance.txtadidas_001,
      'txtrevealcodeby_001': instance.txtrevealcodeby_001,
      'txtcodereveal_001': instance.txtcodereveal_001,
      'txtcouponcode_001': instance.txtcouponcode_001,
      'txtarchive_001': instance.txtarchive_001,
      'ctrl_btngotoadidas_001': instance.ctrl_btngotoadidas_001,
      'txtcopied_001': instance.txtcopied_001,
      'txtyourcantbedisplayederror_001':
          instance.txtyourcantbedisplayederror_001,
      'ctrl_btngoback_001': instance.ctrl_btngoback_001,
      'txtadidasstatuscoupon_5': instance.txtadidasstatuscoupon_5,
      'txtadidasstatuscoupon_1': instance.txtadidasstatuscoupon_1,
      'txtadidasstatuscoupon_2': instance.txtadidasstatuscoupon_2,
      'txtadidasstatuscoupon_3': instance.txtadidasstatuscoupon_3,
      'txtadidasstatuscoupon_4': instance.txtadidasstatuscoupon_4,
      'txtAdidasStatusRewardDisclaimerstatus_5':
          instance.txtAdidasStatusRewardDisclaimerstatus_5,
      'txtAdidasStatusRewardDisclaimerstatus_4':
          instance.txtAdidasStatusRewardDisclaimerstatus_4,
      'txtAdidasStatusRewardDisclaimerstatus_3':
          instance.txtAdidasStatusRewardDisclaimerstatus_3,
      'txtAdidasStatusRewardDisclaimerstatus_2':
          instance.txtAdidasStatusRewardDisclaimerstatus_2,
      'txtAdidasStatusRewardDisclaimerstatus_1':
          instance.txtAdidasStatusRewardDisclaimerstatus_1,
      'txtweek_001': instance.txtweek_001,
      'txt_vitalityid_001': instance.txt_vitalityid_001,
      'txtunsafedevice_detected_title_001':
          instance.txtunsafedevice_detected_title_001,
      'txtunsafedevice_detected_description_001':
          instance.txtunsafedevice_detected_description_001,
      'txtunsafedevice_detected_ok_001':
          instance.txtunsafedevice_detected_ok_001,
      'txt_btn_segment_picker_active_001':
          instance.txt_btn_segment_picker_active_001,
      'txt_btn_segment_picker_archived_001':
          instance.txt_btn_segment_picker_archived_001,
      'txtyourrevealedcodesswillappearhere_001':
          instance.txtyourrevealedcodesswillappearhere_001,
      'txtyoucurrentlyhavenoarchivedcodes_001':
          instance.txtyoucurrentlyhavenoarchivedcodes_001,
      'ctrl_redeem_benefit': instance.ctrl_redeem_benefit,
      'txt_prenuvolanding_footernote': instance.txt_prenuvolanding_footernote,
      'txt_completevhrtounlock_001': instance.txt_completevhrtounlock_001,
      'txt_prenuvolanding_enabler_001': instance.txt_prenuvolanding_enabler_001,
      'txt_prenuvo_001': instance.txt_prenuvo_001,
      'txt_prenuvodiscount_001': instance.txt_prenuvodiscount_001,
      'txt_usevitalityidtoredeem_001': instance.txt_usevitalityidtoredeem_001,
      'txt_gotoprenuvo_001': instance.txt_gotoprenuvo_001,
      'txt_btn_segment_picker_available_001':
          instance.txt_btn_segment_picker_available_001,
      'your_codes_001': instance.your_codes_001,
      'sharestreaks_details_001': instance.sharestreaks_details_001,
      'btn_sharenow_001': instance.btn_sharenow_001,
      'txt_confirmbyuploadingsubmission_001':
          instance.txt_confirmbyuploadingsubmission_001,
      'txt_diabetes_support': instance.txt_diabetes_support,
      'ActivityCategoryLabeltxt_85': instance.ActivityCategoryLabeltxt_85,
      'adidas_reward_landing_enabler_1':
          instance.adidas_reward_landing_enabler_1,
      'adidas_reward_landing_enabler_2':
          instance.adidas_reward_landing_enabler_2,
      'adidas_reward_landing_enabler_3':
          instance.adidas_reward_landing_enabler_3,
      'sr_revealcode_partnername_1337': instance.sr_revealcode_partnername_1337,
      'sr_revealcode_by_1337': instance.sr_revealcode_by_1337,
      'sr_code_revealed_on_1337': instance.sr_code_revealed_on_1337,
      'sr_revealcode_cta_subtext_1337': instance.sr_revealcode_cta_subtext_1337,
      'sr_revealcode_cta_text_1337': instance.sr_revealcode_cta_text_1337,
      'sr_revealcode_breakout_cta_text_1337':
          instance.sr_revealcode_breakout_cta_text_1337,
      'sr_code_display_1337': instance.sr_code_display_1337,
      'sr_code_copy_1337': instance.sr_code_copy_1337,
      'sr_code_copied_1337': instance.sr_code_copied_1337,
      'sr_code_archive_1337': instance.sr_code_archive_1337,
      'sr_code_archived_1337': instance.sr_code_archived_1337,
      'sr_revealcode_discountdetail_5_1337':
          instance.sr_revealcode_discountdetail_5_1337,
      'sr_revealcode_discountdetail_1_1337':
          instance.sr_revealcode_discountdetail_1_1337,
      'sr_revealcode_discountdetail_2_1337':
          instance.sr_revealcode_discountdetail_2_1337,
      'sr_revealcode_discountdetail_3_1337':
          instance.sr_revealcode_discountdetail_3_1337,
      'sr_revealcode_discountdetail_4_1337':
          instance.sr_revealcode_discountdetail_4_1337,
      'sr_revealcode_reward_disclaimer_5_1337':
          instance.sr_revealcode_reward_disclaimer_5_1337,
      'sr_revealcode_reward_disclaimer_4_1337':
          instance.sr_revealcode_reward_disclaimer_4_1337,
      'sr_revealcode_reward_disclaimer_3_1337':
          instance.sr_revealcode_reward_disclaimer_3_1337,
      'sr_revealcode_reward_disclaimer_2_1337':
          instance.sr_revealcode_reward_disclaimer_2_1337,
      'sr_revealcode_reward_disclaimer_1_1337':
          instance.sr_revealcode_reward_disclaimer_1_1337,
      'ActivityCategoryIconKey_85': instance.ActivityCategoryIconKey_85,
      'txt_learn_more_about_the_rules_001':
          instance.txt_learn_more_about_the_rules_001,
      'txt_about_this_reward_001': instance.txt_about_this_reward_001,
      'ha_section_Iconkey_129': instance.ha_section_Iconkey_129,
      'txt_types_001': instance.txt_types_001,
      'txt_prenuvodiscount_instructions_001':
          instance.txt_prenuvodiscount_instructions_001,
      'rewardpartner_prenuvo_url': instance.rewardpartner_prenuvo_url,
      'txt_functionhealth_discount_001':
          instance.txt_functionhealth_discount_001,
      'txt_functionhealth_001': instance.txt_functionhealth_001,
      'txt_functionhealthdiscount_instructions_001':
          instance.txt_functionhealthdiscount_instructions_001,
      'txt_gotofunctionhealth_001': instance.txt_gotofunctionhealth_001,
      'rewardpartner_functionhealth_url':
          instance.rewardpartner_functionhealth_url,
      'txt_completed_appare_1337': instance.txt_completed_appare_1337,
      'txtheader_YouAndVitality_001': instance.txtheader_YouAndVitality_001,
      'txtsubtext_YouAndVitality_001': instance.txtsubtext_YouAndVitality_001,
      'status_rewards_status_1_subtitle_1337':
          instance.status_rewards_status_1_subtitle_1337,
      'status_rewards_status_1_title_1337':
          instance.status_rewards_status_1_title_1337,
      'status_rewards_status_2_subtitle_1337':
          instance.status_rewards_status_2_subtitle_1337,
      'status_rewards_status_2_title_1337':
          instance.status_rewards_status_2_title_1337,
      'status_rewards_status_3_subtitle_1337':
          instance.status_rewards_status_3_subtitle_1337,
      'status_rewards_status_3_title_1337':
          instance.status_rewards_status_3_title_1337,
      'status_rewards_status_4_subtitle_1337':
          instance.status_rewards_status_4_subtitle_1337,
      'status_rewards_status_4_title_1337':
          instance.status_rewards_status_4_title_1337,
      'status_rewards_status_5_subtitle_1337':
          instance.status_rewards_status_5_subtitle_1337,
      'status_rewards_status_5_title_1337':
          instance.status_rewards_status_5_title_1337,
      'txt_nutrisenselanding_enabler_001':
          instance.txt_nutrisenselanding_enabler_001,
      'txt_nutrisenselanding_footernote':
          instance.txt_nutrisenselanding_footernote,
      'txt_functionhealth_enabler_001': instance.txt_functionhealth_enabler_001,
      'txt_functionhealth_footernote': instance.txt_functionhealth_footernote,
      'txt_nutrisense_redeem_weblink_001':
          instance.txt_nutrisense_redeem_weblink_001,
      'txt_maintain_your_healthy_lifestyle_001':
          instance.txt_maintain_your_healthy_lifestyle_001,
      'txt_health_priority_grey_block_001':
          instance.txt_health_priority_grey_block_001,
      'txt_health_priority_intro_004': instance.txt_health_priority_intro_004,
      'txt_tip_body_001': instance.txt_tip_body_001,
      'txt_tip_body_002': instance.txt_tip_body_002,
      'txt_tip_body_003': instance.txt_tip_body_003,
      'partner_description_113600': instance.partner_description_113600,
      'archived_codes_001': instance.archived_codes_001,
      'txt_no_archived_codes_for_year_001':
          instance.txt_no_archived_codes_for_year_001,
      'txt_no_archived_codes_for_year_copy_001':
          instance.txt_no_archived_codes_for_year_copy_001,
      'txt_you_currently_have_no_codes_001':
          instance.txt_you_currently_have_no_codes_001,
      'txt_you_currently_have_no_codes_copy_001':
          instance.txt_you_currently_have_no_codes_copy_001,
      'txt_no_archived_codes_001': instance.txt_no_archived_codes_001,
      'txt_no_archived_codes_copy_001': instance.txt_no_archived_codes_copy_001,
      'txt_oura_enabler_001': instance.txt_oura_enabler_001,
      'txt_oura_footernote': instance.txt_oura_footernote,
      'rewardpartner_oura_discount_url':
          instance.rewardpartner_oura_discount_url,
      'txt_activefitdirect_001': instance.txt_activefitdirect_001,
      'txt_afd_enabler_001': instance.txt_afd_enabler_001,
      'txt_afd_footernote': instance.txt_afd_footernote,
      'txtwehavealittleproblemcopy_001':
          instance.txtwehavealittleproblemcopy_001,
      'txtwehavealittleproblem_001': instance.txtwehavealittleproblem_001,
      'ActivityCategoryLabeltxt_87': instance.ActivityCategoryLabeltxt_87,
      'ActivityCategoryIconKey_87': instance.ActivityCategoryIconKey_87,
      'txt_afd_redeem_weblink_001': instance.txt_afd_redeem_weblink_001,
      'sr_archive_item_1337': instance.sr_archive_item_1337,
      'sr_view_terms_1_1337': instance.sr_view_terms_1_1337,
      'sr_view_terms_2_1337': instance.sr_view_terms_2_1337,
      'sr_terms_condition_1337': instance.sr_terms_condition_1337,
      'txt_reward_code_copied': instance.txt_reward_code_copied,
      'txtcodesuccessfullyarchived_001':
          instance.txtcodesuccessfullyarchived_001,
      'txtcodessuccessfullyarchived_001':
          instance.txtcodessuccessfullyarchived_001,
      'txtSafeDriving_001': instance.txtSafeDriving_001,
      'txtsubmitproof_002': instance.txtsubmitproof_002,
      'sd_submitProof_text_001': instance.sd_submitProof_text_001,
      'txtDateNote_001': instance.txtDateNote_001,
      'txtPrenatalCare_001': instance.txtPrenatalCare_001,
      'txtsubmitproof_003': instance.txtsubmitproof_003,
      'txtdownloadform_001': instance.txtdownloadform_001,
      'pc_submitProof_text_001': instance.pc_submitProof_text_001,
      'sr_code_detail_discount_detail_1337':
          instance.sr_code_detail_discount_detail_1337,
      'sr_code_detail_dynamic_terms_1337':
          instance.sr_code_detail_dynamic_terms_1337,
      'txt_whoop_footernote': instance.txt_whoop_footernote,
      'txt_doyouhavewhoopmembership_001':
          instance.txt_doyouhavewhoopmembership_001,
      'txt_pleaseletusknow_001': instance.txt_pleaseletusknow_001,
      'txt_iamnewtowhoop_001': instance.txt_iamnewtowhoop_001,
      'txt_ihaveexistingwhoop_001': instance.txt_ihaveexistingwhoop_001,
      'txt_proceed_001': instance.txt_proceed_001,
      'txt_usepromojohnhancock_001': instance.txt_usepromojohnhancock_001,
      'txt_usethiscodeatcheckout_001': instance.txt_usethiscodeatcheckout_001,
      'imagepath_dms_whoopdevice': instance.imagepath_dms_whoopdevice,
      'txt_highlightfeatures_001': instance.txt_highlightfeatures_001,
      'txt_learnmoreaboutwhoop_001': instance.txt_learnmoreaboutwhoop_001,
      'txt_redeemwhoopmembership_001': instance.txt_redeemwhoopmembership_001,
      'txt_whoop_feature_001': instance.txt_whoop_feature_001,
      'txt_whoop_feature_002': instance.txt_whoop_feature_002,
      'txt_whoop_feature_003': instance.txt_whoop_feature_003,
      'txt_whoop_feature_004': instance.txt_whoop_feature_004,
      'txt_whoop_feature_005': instance.txt_whoop_feature_005,
      'txt_whoopmembership_001': instance.txt_whoopmembership_001,
      'weblink_learnmore_whoop': instance.weblink_learnmore_whoop,
      'weblink_redeemmembership_whoop': instance.weblink_redeemmembership_whoop,
      'weblink_renewmembership_whoop': instance.weblink_renewmembership_whoop,
      'txt_burned_calories_details_001':
          instance.txt_burned_calories_details_001,
      'txt_proofnote_sd_001': instance.txt_proofnote_sd_001,
      'txt_proofnote_sd_002': instance.txt_proofnote_sd_002,
      'txt_proofnote_sd_003': instance.txt_proofnote_sd_003,
      'txt_proofnote_pc_001': instance.txt_proofnote_pc_001,
      'txt_proofnote_pc_002': instance.txt_proofnote_pc_002,
      'txt_proofnote_pc_003': instance.txt_proofnote_pc_003,
      'txt_proofnote_pc_004': instance.txt_proofnote_pc_004,
      'txt_proofnote_pc_005': instance.txt_proofnote_pc_005,
      'txt_diabetes_hba1c_001': instance.txt_diabetes_hba1c_001,
      'txtproofnote_pc_001': instance.txtproofnote_pc_001,
      'txt_grail_enabler_001': instance.txt_grail_enabler_001,
      'txt_grail_footernote': instance.txt_grail_footernote,
      'txt_grail_age_eligible_001': instance.txt_grail_age_eligible_001,
      'txt_grail_001': instance.txt_grail_001,
      'txt_graildiscount_001': instance.txt_graildiscount_001,
      'txt_accessid_001': instance.txt_accessid_001,
      'txt_useaccessidtoredeem_001': instance.txt_useaccessidtoredeem_001,
      'txt_graildiscount_instructions_001':
          instance.txt_graildiscount_instructions_001,
      'txt_gotograil_001': instance.txt_gotograil_001,
      'rewardpartner_grail_url': instance.rewardpartner_grail_url,
      'txt_grail_freeTest_001': instance.txt_grail_freeTest_001,
      'ActivityCategoryLabeltxt_93': instance.ActivityCategoryLabeltxt_93,
      'ActivityCategoryIconKey_93': instance.ActivityCategoryIconKey_93,
      'txthealthprioritygreyblock_001': instance.txthealthprioritygreyblock_001,
      'txthealthpriorityintro_004': instance.txthealthpriorityintro_004,
      'txttipbody_001': instance.txttipbody_001,
      'txttipbody_002': instance.txttipbody_002,
      'txttipbody_003': instance.txttipbody_003,
      'hc_text_participating_partners': instance.hc_text_participating_partners,
      'hc_text_schedule_screening': instance.hc_text_schedule_screening,
      'hc_text_reperio': instance.hc_text_reperio,
      'hc_text_quest_diagnostics': instance.hc_text_quest_diagnostics,
      'earn_points_active_calories_001':
          instance.earn_points_active_calories_001,
      'earn_points_active_calories_002':
          instance.earn_points_active_calories_002,
      'earn_points_active_calories_003':
          instance.earn_points_active_calories_003,
      'earn_points_active_calories_details':
          instance.earn_points_active_calories_details,
      'sr_revealcode_partner_store_url_1337':
          instance.sr_revealcode_partner_store_url_1337,
      'dt_format_short_day_month_full_year':
          instance.dt_format_short_day_month_full_year,
      'vna_expedia_contentnote': instance.vna_expedia_contentnote,
      'txt_onboarding_reward_imagepath_freemium':
          instance.txt_onboarding_reward_imagepath_freemium,
      'txt_onboarding3_header_freemium':
          instance.txt_onboarding3_header_freemium,
      'txt_onboarding3_subtext_freemium':
          instance.txt_onboarding3_subtext_freemium,
      'txt_onboarding4_header_freemium':
          instance.txt_onboarding4_header_freemium,
      'txt_onboarding4_subtext_freemium':
          instance.txt_onboarding4_subtext_freemium,
      'txt_onboarding5_header_freemium':
          instance.txt_onboarding5_header_freemium,
      'txt_onboarding5_subtext_freemium':
          instance.txt_onboarding5_subtext_freemium,
      'txtbronze_subtext_001_freemium': instance.txtbronze_subtext_001_freemium,
      'txtsilver_subtext_001_freemium': instance.txtsilver_subtext_001_freemium,
      'txtgold_subtext_001_freemium': instance.txtgold_subtext_001_freemium,
      'txtglucose_intro_004': instance.txtglucose_intro_004,
      'txt_check_in_at_the_gym': instance.txt_check_in_at_the_gym,
      'txt_manually_submit_a_workout': instance.txt_manually_submit_a_workout,
      'txt_submit_workout': instance.txt_submit_workout,
      'txt_gym_check_in': instance.txt_gym_check_in,
      'txt_steps_description': instance.txt_steps_description,
      'earn_points_steps_001': instance.earn_points_steps_001,
      'earn_points_steps_002': instance.earn_points_steps_002,
      'earn_points_steps_003': instance.earn_points_steps_003,
      'earn_points_heart_rate_001': instance.earn_points_heart_rate_001,
      'earn_points_heart_rate_002': instance.earn_points_heart_rate_002,
      'earn_points_heart_rate_003': instance.earn_points_heart_rate_003,
      'earn_points_workout_calories_001':
          instance.earn_points_workout_calories_001,
      'earn_points_workout_calories_002':
          instance.earn_points_workout_calories_002,
      'earn_points_workout_calories_003':
          instance.earn_points_workout_calories_003,
      'txtplatinum_subtext_001_freemium':
          instance.txtplatinum_subtext_001_freemium,
      'txt_headspace_enabler_001': instance.txt_headspace_enabler_001,
      'txt_headspace_footernote': instance.txt_headspace_footernote,
      'txt_bookinglastupdatedon_001': instance.txt_bookinglastupdatedon_001,
      'txt_payoutdate_enabler_001': instance.txt_payoutdate_enabler_001,
      'txt_maintainplatinum_001': instance.txt_maintainplatinum_001,
      'txt_streakprogress_001': instance.txt_streakprogress_001,
      'txt_achieveplatinumvhr_001': instance.txt_achieveplatinumvhr_001,
      'txt_maintainplatinum_vhr_001': instance.txt_maintainplatinum_vhr_001,
      'txt_yearofstreak_001': instance.txt_yearofstreak_001,
      'txt_free_oneyearmembership_001': instance.txt_free_oneyearmembership_001,
      'txt_submit_a_photo_of_your_workout':
          instance.txt_submit_a_photo_of_your_workout,
      'txt_at_home_workout': instance.txt_at_home_workout,
      'txt_manually_submit_a_workout_title':
          instance.txt_manually_submit_a_workout_title,
      'txt_manually_submit_a_workout_desc':
          instance.txt_manually_submit_a_workout_desc,
      'vhc_alt_header_txtpoints_001': instance.vhc_alt_header_txtpoints_001,
      'vhc_alt_submenu_txtpoints_001': instance.vhc_alt_submenu_txtpoints_001,
      'vhc_txt_max_points_for_submission_type_001':
          instance.vhc_txt_max_points_for_submission_type_001,
      'vhc_txt_max_points_earn_for_activity':
          instance.vhc_txt_max_points_earn_for_activity,
      'vhc_txt_max_points_earn_for_health_check_submission':
          instance.vhc_txt_max_points_earn_for_health_check_submission,
      'headspace_breakout_url_01': instance.headspace_breakout_url_01,
      'txtunlockthisreward_002': instance.txtunlockthisreward_002,
      'txtactivateyourhealthyfood_001': instance.txtactivateyourhealthyfood_001,
      'txt_hf_appexperiencefeedback_001':
          instance.txt_hf_appexperiencefeedback_001,
      'txt_hf_autoapplied_001': instance.txt_hf_autoapplied_001,
      'txt_hf_cashbabck_calculated_001':
          instance.txt_hf_cashbabck_calculated_001,
      'txt_hf_discount_001': instance.txt_hf_discount_001,
      'txt_hf_enabler_001': instance.txt_hf_enabler_001,
      'txt_hf_footernote': instance.txt_hf_footernote,
      'txt_rep_enabler_001': instance.txt_rep_enabler_001,
      'txt_que_enabler_001': instance.txt_que_enabler_001,
      'ActivityCategoryLabeltxt_96': instance.ActivityCategoryLabeltxt_96,
      'ActivityCategoryIconKey_96': instance.ActivityCategoryIconKey_96,
      'ctrl_btnactivateprogram_001': instance.ctrl_btnactivateprogram_001,
      'jh_hc_cotinine_earn_points': instance.jh_hc_cotinine_earn_points,
      'txt_healthy_gear_purchase_rei_cta':
          instance.txt_healthy_gear_purchase_rei_cta,
      'txt_healthy_gear_order_history': instance.txt_healthy_gear_order_history,
      'txt_healthy_gear_gift_card_value_001':
          instance.txt_healthy_gear_gift_card_value_001,
      'txt_healthy_gear_you_saved_001': instance.txt_healthy_gear_you_saved_001,
      'txt_healthy_gear_landing_enabler_001':
          instance.txt_healthy_gear_landing_enabler_001,
      'txt_healthy_gear_giftcard_value_001':
          instance.txt_healthy_gear_giftcard_value_001,
      'txt_healthy_gear_vhr_unlock_001':
          instance.txt_healthy_gear_vhr_unlock_001,
      'partner_description_113615': instance.partner_description_113615,
      'partner_description_113616': instance.partner_description_113616,
      'txt_fitbit_discount_membership_001':
          instance.txt_fitbit_discount_membership_001,
      'txt_fitbit_discount_feature_001':
          instance.txt_fitbit_discount_feature_001,
      'txt_fitbit_discount_feature_002':
          instance.txt_fitbit_discount_feature_002,
      'txt_fitbit_discount_feature_003':
          instance.txt_fitbit_discount_feature_003,
      'txt_fitbit_discount_feature_004':
          instance.txt_fitbit_discount_feature_004,
      'txt_fitbit_discount_feature_005':
          instance.txt_fitbit_discount_feature_005,
      'txt_fitbit_discount_feature_006':
          instance.txt_fitbit_discount_feature_006,
      'txt_learnmoreabout_fitbit_discount_001':
          instance.txt_learnmoreabout_fitbit_discount_001,
      'txt_redeem_fitbit_discount_membership_001':
          instance.txt_redeem_fitbit_discount_membership_001,
      'txt_fitbit_discount_footernote': instance.txt_fitbit_discount_footernote,
      'txt_claim_your_complimentary_device':
          instance.txt_claim_your_complimentary_device,
      'txt_garmin_vivosmart': instance.txt_garmin_vivosmart,
      'txt_highlight_features': instance.txt_highlight_features,
      'txt_features_1year_battery': instance.txt_features_1year_battery,
      'txt_shows_sleep_and_workout_data_including_intensity':
          instance.txt_shows_sleep_and_workout_data_including_intensity,
      'txt_automatic_activity_detection':
          instance.txt_automatic_activity_detection,
      'txt_stay_active_reminders': instance.txt_stay_active_reminders,
      'txt_automatic_sync_to_garmin_connect':
          instance.txt_automatic_sync_to_garmin_connect,
      'txt_learn_more_about_garmin_vivosmart_5':
          instance.txt_learn_more_about_garmin_vivosmart_5,
      'txt_claim_garmin_vivosmart_5': instance.txt_claim_garmin_vivosmart_5,
      'txt_footnote_complimantary': instance.txt_footnote_complimantary,
    };
