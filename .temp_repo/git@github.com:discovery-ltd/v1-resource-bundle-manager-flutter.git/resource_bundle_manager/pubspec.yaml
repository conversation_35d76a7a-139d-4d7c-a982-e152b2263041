name: "resource_bundle_manager"
description: "A description of your project"
version: 0.0.500
homepage: https://github.com/discovery-ltd/v1-resource-bundle-manager-flutter
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=2.17.0"
dependencies:
  flutter:
    sdk: flutter
  json_annotation: ^4.4.0
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.118
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.6
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.97
  path_provider: ^2.0.5
  http: 1.1.0
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.412
  flutter_localizations:
    sdk: flutter
dependency_overrides:
  watcher: ^1.1.0
  http: 1.1.0
  intl: ^0.19.0
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.118
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.6
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
  json_serializable: ^6.1.1
  mockito: ^5.4.0
  analyzer: ^5.13.0
  path_provider_platform_interface: ^2.1.2
  plugin_platform_interface: ^2.1.8
flutter:
  assets:
    - assets/
    - assets/mock/
