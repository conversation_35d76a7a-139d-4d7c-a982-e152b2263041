{"already_have_an_account_2": "Already have an account? <PERSON>gin", "appdevices_applehealth_txtconnect_001": "Syncing data from your Health app to Vitality helps you achieve your weekly goals. Apple's Health app will track data from your iPhone and/or Apple Watch. ", "appdevices_applehealth_txtmanagepermissions_001": "You can manage the data you share through Health app at any time. To do this, go to Health app > tap profile picture > Apps > Vitality. ", "appdevices_applehealth_txtsynctime_001": "When logged in to the <Insurer Vitality> app", "appdevices_fitbit_txtconnect_001": "You need a Fitbit account to link to <Insurer Vitality> and track your physical activity.", "appdevices_fitbit_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Fitbit.", "appdevices_fitbit_txtsynctime_001": "Shared data will automatically sync to Vitality every 2 minutes.", "appdevices_garmin_txtconnect_001": "You need a Garmin account to link to <Insurer Vitality> and track your physical activity from your Garmin device. ", "appdevices_garmin_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Garmin.", "appdevices_garmin_txtsynctime_001": "Shared data will automatically sync to Vitality every 5 minutes", "appdevices_googlefit_txtconnect_001": "You need a Google Fit account to link to <Insurer Vitality> and track your physical activity.", "appdevices_googlefit_txtmanagepermissions_001": "You can manage the data you share through Google Fit at any time. To do this, go to Google Fit > Profile > Settings > Manage connected apps > Vitality. ", "appdevices_googlefit_txtsynctime_001": "When logged in to the <Insurer Vitality> app", "appdevices_polar_txtconnect_001": "You need a Polar account to link to <Insurer Vitality> and track your physical activity from your Polar device. ", "appdevices_polar_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Polar.", "appdevices_polar_txtsynctime_001": "Shared data will automatically sync to Vitality every hour.", "appdevices_samsunghealth_txtconnect_001": "Syncing data from your %1$@ app to Vitality helps you achieve your weekly goals. Samsung’s Health app will track data from your Android phone and/or Samsung watch.", "appdevices_samsunghealth_txtmanagepermissions_001": "You can manage the data you share through %1$@  at any time. To do this, go to %2$@  > tap Menu > Settings > Data Permissions > Vitality.", "appdevices_samsunghealth_txtsynctime_001": "When logged into the <Insurer Vitality> app", "appdevices_strava_txtconnect_001": "You need a Strava account to link to <Insurer Vitality> and track your physical activity.", "appdevices_strava_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Strava.", "appdevices_strava_txtsynctime_001": "Shared data will automatically sync to Vitality every 10 minutes.", "appdevices_suunto_txtconnect_001": "Syncing data from your Suunto account can help you achieve your weekly goals. Vitality connects to your Suunto account to track data from your Suunto device.", "appdevices_suunto_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Suunto.", "appdevices_suunto_txtsynctime_001": "Every 2 hours", "appdevices_withings_txtconnect_001": "You need a Withings account to link to <Insurer Vitality> and track your physical activity.", "appdevices_withings_txtmanagepermissions_001": "You can stop sharing data with Vitality at any time by disconnecting Withings.", "appdevices_withings_txtsynctime_001": "Shared data will automatically sync to Vitality every 10 minutes.", "apppsdevices_landing_txtlastsyncdate_001": "Last data shared: %1$@", "appsdevices_fitbit_txtsyncaccount_001": "We’ll take you to the Fitbit app so that you can log in to your account, and begin syncing your data.", "appsdevices_samsunghealth_txtallpermissions_001": "Turn on all permissions", "appsdevices_samsunghealth_txtbodyallpermissions_001": "Allow Vitality to receive data from your %1$@ app to track activity in Vitality. Select “turn on all permissions” for the best experience.", "appsdevices_samsunghealth_txtbodyconnectionerror_001": "We weren't able to connect your %1$@ app at the moment due to an issue with our server. Please wait a few minutes and try again.", "appsdevices_samsunghealth_txtbodydisconnect_001": "This will stop %1$@ from sharing your data with us. Previously collected data will still be stored.", "appsdevices_samsunghealth_txtconnected_001": "%1$@ is now connected to <Insurer Vitality>", "appsdevices_samsunghealth_txtdisconnectalert_001": "%1$@ disconnected", "appsdevices_samsunghealth_txthddisconnect_001": "Disconnect app", "appsdevices_samsunghealth_txtsyncaccount_001": "Let’s take you to your %1$@ app to start synching your data.", "appsdevices_samsungsupport_txtbodyhowitworks_001": "Allow Vitality to read your steps data to get points toward your get active goal. \\nManually entered or edited data will not count toward your goal.\\nShared data will automatically sync to Vitality when you are logged into the app. It can take up to 24 hours for your points to reflect.\\nTurning off data syncing in %1$@ will stop transferring data to Vitality, but it will not delete any previously synced data.\\nSome program features may need additional data points not previously shared through  %1$@. \\nYou can manage the data you share through  %1$@  at any time. To do this, go to  %1$@  > tap Menu > Settings > Data Permissions > Vitality.", "appsdevices_samsungsupport_txthdhowitworks_001": "How it works?", "body_AppCrashSettins": "Share app crashes and errors so that we can fix them", "body_cholestrolAbout": "It is best to have regular vaccinations as prevention is always better than cure. <PERSON><PERSON><PERSON> points when getting your vaccinations done.", "body_EmailSettings": "Receive emails about offers and updates", "body_emptyState_": "Goal activated and will start tracking on %1$@. Check back soon", "body_emptyState_noPromotion": "Come back later to read some wellness wisdom.", "body_MembershipCancelled": "We are sorry to see you go.\n\nShould you want to re-activate your membership, you have until\n27 January 2023 before any points and rewards are removed.\n\nA membership cancellation email has been sent to you upon cancellation with any other relevant information as well as the status of, and access to, any remaining benefits and rewards.\n\nFor any assistance or queries regarding the above, please contact our support centre.\n\n", "body_NeedHelp": "Need help? <insurer contact>", "body_Needhelp": "Need help? <insurer contact>", "body_PrivacyPolicy": "1.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n1.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n1.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. In egestas erat imperdiet sed euismod. Pulvinar etiam non quam lacus suspendisse faucibus interdum. Venenatis a condimentum vitae sapien pellentesque habitant morbi tristique senectus.\n\n1.1.3. Viverra orci sagittis eu volutpat odio facilisis. Fringilla ut morbi tincidunt augue interdum velit euismod. Vestibulum lectus mauris ultrices eros in cursus. Vivamus at augue eget arcu.\n\n1.1.4. Nunc sed id semper risus in hendrerit gravida. Fermentum posuere urna nec tincidunt praesent semper.\n\n2.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n2.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n2.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. ", "body_SaveEmailLogin": "Save my email address when I log in", "body_ScreenAnalyticsSettings": "Share screen interactions anonymously so that we can improve the app", "body_submitproofparagraph": "Only confirm completion for screenings and vaccinations that you can upload verified proof for.", "body_TsCs": "1.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n1.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n1.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. In egestas erat imperdiet sed euismod. Pulvinar etiam non quam lacus suspendisse faucibus interdum. Venenatis a condimentum vitae sapien pellentesque habitant morbi tristique senectus.\n\n1.1.3. Viverra orci sagittis eu volutpat odio facilisis. Fringilla ut morbi tincidunt augue interdum velit euismod. Vestibulum lectus mauris ultrices eros in cursus. Vivamus at augue eget arcu.\n\n1.1.4. Nunc sed id semper risus in hendrerit gravida. Fermentum posuere urna nec tincidunt praesent semper.\n\n2.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n2.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n2.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. ", "btn_Agree": "Agree", "btn_Disagree": "Disagree", "btn_LogOut": "Log out", "btn_next": "Next", "btn_ResetPw": "Reset password", "btn_SavePreferences": "Save preferences", "btn_showAll": "View more", "chip_activeEnergy": "Active Energy", "chip_heartRate": "Heart rate", "chip_steps": "Steps", "chip_weight": "Weight", "crd_title_supportQ1": "<Question title goes here>", "crd_title_supportQ2": "<Question title goes here>", "crd_title_supportQ3": "<Question title goes here>", "crd_title_supportQ4": "<Question title goes here>", "ctrl_appdevices_applehealth_btnConnect_001": "Connect Apple Health", "ctrl_appdevices_applehealth_txtsupport_001": "Support for Apple Health", "ctrl_appdevices_fitbit_btnConnect_001": "Connect Fitbit", "ctrl_appdevices_fitbit_txtsupport_001": "Support for Fitbit", "ctrl_appdevices_garmin_btnConnect_001": "Connect Garmin", "ctrl_appdevices_garmin_txtsupport_001": "Support for G<PERSON>in", "ctrl_appdevices_googlefit_btnConnect_001": "Connect Google Fit", "ctrl_appdevices_googlefit_txtsupport_001": "Support for Google Fit", "ctrl_appdevices_polar_btnConnect_001": "Connect Garmin", "ctrl_appdevices_polar_txtsupport_001": "Support for Polar", "ctrl_appdevices_samsunghealth_btnConnect_001": "Connect %1$@", "ctrl_appdevices_samsunghealth_txtsupport_001": "Support for %1$@", "ctrl_appdevices_strava_btnConnect_001": "Connect Strava", "ctrl_appdevices_strava_txtsupport_001": "Support for Strava", "ctrl_appdevices_suunto_btnConnect_001": "Connect Suunto", "ctrl_appdevices_suunto_txtsupport_001": "Support for <PERSON><PERSON><PERSON>", "ctrl_appdevices_withings_btnConnect_001": "Connect Withings", "ctrl_appdevices_withings_txtsupport_001": "Support for Withings", "ctrl_appsdevices_applehealth_btndisconnect_001": "Disconnect Apple Health", "ctrl_appsdevices_applehealth_btnmanpermissions_001": "Manage permissions", "ctrl_appsdevices_fitbit_btndisconnect_001": "Disconnect Fitbit", "ctrl_appsdevices_garmin_btndisconnect_001": "Disconnect G<PERSON>in", "ctrl_appsdevices_googlefit_btndisconnect_001": "Disconnect Google Fit", "ctrl_appsdevices_polar_btndisconnect_001": "Disconnect Polar", "ctrl_appsdevices_samsunghealth_btndisconnect_001": "Disconnect", "ctrl_appsdevices_samsunghealth_btnmanpermissions_001": "Manage permissions", "ctrl_appsdevices_strava_btndisconnect_001": "Disconnect Strava", "ctrl_appsdevices_suunto_btndisconnect_001": "Disconnect Suunto", "ctrl_appsdevices_withings_btndisconnect_001": "Disconnect Withings", "ctrl_btnactivateacc_001": "Activate account", "ctrl_btnactivategoal_001": "Activate goal", "txtcholesterol_guide_001": "Healthy cholesterol guide", "txthealthycholesterol_intro_001": "<Having cholesterol levels in an optimal range contributes to a longer and healthier life. There are steps you can take to better manage and reduce your cholesterol and in turn reduce your risk for heart disease.>", "txttips_cholesterol_001": "Tips to manage cholesterol", "txttips_cholesterol_content_001": "Use this guide to help create a sustainable healthy lifestyle for cholesterol management, health and longevity.", "ctrl_btnagree_002": "Agree", "ctrl_btnallow_001": "Allow", "ctrl_btnancel_001": "Cancel", "ctrl_btnangleright_001": "angle-right", "ctrl_btnappsanddevices_001": "Apps and devices", "ctrl_btncancel_001": "Cancel", "ctrl_btncancel_002": "Cancel", "ctrl_btnconfirm_001": "Confirm", "ctrl_btnconfirm_002": "Confirm", "ctrl_btnconnect_001": "Connect", "ctrl_btncontinue_001": "Continue", "ctrl_btnctaconnect_001": "Connect", "ctrl_btnchoose_product_001": "Order Product", "ctrl_btndisagree_001": "Disagree", "ctrl_btndisconnect_001": "Disconnect", "ctrl_btndontallow_001": "Don’t allow", "ctrl_btngotit_001": "Got it", "ctrl_btngotovitalitymall_001": "Go to <Vitality insurer> mall", "ctrl_btnhealth_001": "Health", "ctrl_btnhide_001": "<PERSON>de", "ctrl_btnhome_001": "Home", "ctrl_btniunderstand_001": "I understand", "ctrl_btnlearnmore_001": "Learn more", "ctrl_btnletsgo_001": "Let’s go", "ctrl_btnlinknow_001": "Link now", "ctrl_btnLoggingIn_001": "Logging in...", "ctrl_btnlogin_001": "Log in", "ctrl_btnloginfinger_001": "Log in with <Fingerprint>", "ctrl_btnLoginNewPw_001": "Log in with new password", "ctrl_btnloginwith_001": "Log in with", "ctrl_btnLoginWithEmail_001": "Log in with your email address", "ctrl_btnok_001": "Ok", "ctrl_btnokay_001": "Okay", "ctrl_btnonnect_001": "Connect", "ctrl_btnprofile_001": "Profile", "ctrl_btnradiooff_001": "Use <Fingerprint> to login", "ctrl_btnrequestcode_001": "Request code", "ctrl_btnrequestingcode_001": "Requesting code...", "ctrl_btnrequestnow_001": "Request now", "ctrl_btnresetpassword_001": "Reset password", "ctrl_btnreturnlogin_001": "Return to login", "ctrl_btnseeyousoon_001": "We'll see you soon", "ctrl_btnrewards_001": "Rewards", "ctrl_btnsavedetails_001": "Save details", "ctrl_btnsavepref_001": "Save preferences", "ctrl_btnsavingdetails_001": "Saving your details...", "ctrl_btnsavingpref_001": "Saving preferences...", "ctrl_btnshow_001": "Show", "ctrl_btnskip_001": "<PERSON><PERSON>", "ctrl_btntick_001": "check_box_outline_blank", "ctrl_btntryagain_001": "Try again", "ctrl_btnusepass_001": "Use Password", "ctrl_btnusepasswrd_001": "Use Password", "ctrl_btnvector_001": "Save details", "ctrl_btnverify_001": "Verify", "ctrl_btnverifying_001": "Verifying...", "ctrl_linktxtmanuallyrefresh_001": "Manually refresh", "ctrl_lnkforgotpass_001": "Forgot password?", "ctrl_lnkloginemail_001": "Log in with your email address", "ctrl_loginwithbiometric_001": "Log in with your %1$@", "ctrl_lnkprivpolcy_001": "Privacy Policy", "ctrl_lnkresendactivcode_001": "Resend activation code", "ctrl_login_forgotpassfeedback_btnloginnewpass_001": "Be sure to check your spam folder if you don’t see the email.", "ctrl_physicalactivity_landing_btnactivategoal_001": "Activate goal", "ctrl_txt_keep_email_001": "Keep my email address populated when I log in", "ctrl_txt_use_facial_001": "Use %1$@ to log in ", "ctrl_txtaboutphysicalactivitygoal_001": "About physical activity goal", "ctrl_txtappsanddevices_001": "Apps and devices", "ctrl_txtgoalhistory_001": "Goal history", "ctrl_txthowitworks_001": "How it works", "ctrl_txthowtocomplete_001": "How to complete", "ctrl_txthowtoearnpoints_001": "How to earn points", "ctrl_txtlearnmore_001": "Learn more about the %1$@ vaccine", "ctrl_txtmanageappsdevices_001": "Manage apps and devices", "ctrl_txtmanagedevicesandapps_001": "Manage apps and devices", "ctrl_txtsamsungconnected_001": "Samsung Health", "ctrl_txtsupport_001": "Support", "ctrl_btnyeslogout_001": "Yes, log out", "ctrl_btnnogoback_001": "No, go back", "dialog_body_BiometricsDisabled": "The use of <fingerprint> for this app has been disabled and can be enabled again from settings.", "dialog_body_NotRecognised": "Not recognised", "dialog_body_TsCsRequired": "If you “disagree,” you will be logged out and unable to continue until you agree with the terms and conditions.", "dialog_body_UnableToRegister": "You need to be 18 years or older to register on the app.", "dialog_btn1_": "Ok", "dialog_btn1_Ok": "Okay", "dialog_btn1_Okay": "Okay", "dialog_btn2_Cancel": "Cancel", "dialog_errfinprntdis_hgconfirm_001": "Confirm using your fingerprint", "dialog_hd_BiometricsDisabled": "<Fingerprint> disabled", "dialog_hd_fingerAuthentication": "fingerprint authentication", "dialog_hd_fingerFacial": "fingerprint/facial recognition", "dialog_hd_touch": "Touch ID", "dialog_hd_TsCsRequired": "Terms and Conditions are required to move forward", "dialog_hd_UnableToRegister": "Unable to register", "dialog_hg_Biometrics": "Confirm using Face", "footnote_ManagePermissions": "You can manage your permissions within our app, under settings. ", "guide_Privacy": "Your privacy matters to us. Only anonymous data is recorded. We do not capture anything that could identify individuals.", "guide_supportQ2": "Vitality will collect your choice of health stats from a health app or fitness device shared through a health app to earn you physical activity points.", "hd_connectApp": "Connect your app", "hd_doB": "Please enter your date of birth to continue", "login_dobafter_txthdenterdob_001": "Enter your date of birth to continue", "hd_emptyState_noPromotion": "Sorry, this article isn’t available right now.", "hd_justOneMoreStep": "Just one more step!", "hd_TsCs": "Terms and Conditions", "hg_PersonalisePreferences": "Personalise your preferences", "hg_PrivacyPolicy": "Privacy Policy", "input_": " %1$@", "label_App": "App", "label_insurerVitalty": "<Insurer  Vitality>", "label_mobilePhone": "Mobile  phone", "label_or": "or", "label_PWReq_01": "Minimum of 12 characters", "label_PWReq_02": "At least 1 number", "label_PWReq_03": "At least 1 uppercase letter", "label_PWReq_04": "At least 1 lowercase letter", "label_PWReq_05": "At least 1 special character", "label_Step": "STEP ", "label_wearableDevice": "Wearable \ndevice", "labelRight": "Show", "login_dialog_bdface_001": "Tap Confirm to complete", "login_dialog_bdfingerprnt_001": "Use your <fingerprint> for faster, easier access to your account.\n\nConfirm <fingerprint> to continue.", "login_dialog_bdincorrectpwd_001": "The email or password you've entered is incorrect.\n\nIf this is your first time logging in, please activate your account to continue.", "login_dialog_bdtouchfingerprnt_001": "Touch the fingerprint sensor", "login_dialog_hdface_001": "Authenticate with face", "login_dialog_hdfingerprnt_001": "<Fingerprint> authentication", "login_dialog_hdincorrectpwd_001": "Incorrect email or password", "login_dialog_hdtouchfingerprnt_001": "Authenticate with fingerprint", "login_forgotpassfeedback_txtbodygotmail_001": "No worries. Just enter the email you use for Vitality communications, and we’ll send your instructions to reset it.", "login_memnotactive_txthdmemnotactive_001": "Your %1$@ membership is not active yet", "login_txtand_001": " and ", "login_txtbdyaccativ_002": "You’ll be able to get healthy and rewarded on %1$@, once your %2$@ membership is active.", "login_txtbdyacccancelled_001": "No worries. Just enter the email you use for Vitality communications, and we’ll send your instructions to reset it.", "login_txtbylogginin_001": "By logging in, you agree to our", "login_txtdob_error_001": "Please enter Date of birth", "login_txthdwelcomebk_001": "Welcome back,", "login_txtpassword_error_001": "Password must be at least 12 characters", "login_txtrememberme_001": "Remember Me", "login_txtturnoffrememberme_001": "Turn off ‘Remember Me’ if you prefer to enter your email every time you access the app.", "login_txtyouvegotmail_001": "You’ve got mail", "loginScreenTitle": "Login Screen", "phd_MembershipCancelled": "Your Membership has been cancelled", "phd_supportSection": "About Vitality", "physicalactivity_alert_txtgoalmet_001": "Met", "physicalactivity_dialogalert_txtbodydeviceapplink_001": "Link a fitness device or app to easily track your physical activity.", "physicalactivity_dialogalert_txthddeviceapplink_001": "Link a fitness device or app", "physicalactivity_goalhistory_txtgoalhistory_001": "Goal history", "physicalactivity_landing_lvautotracked_001": "Automatically tracked through a connected app", "physicalactivity_landing_lvnewgoal_001": "New goal every {Monday}", "physicalactivity_landing_lvreward_001": "Earn {a reward}", "physicalactivity_landing_txtbodyhowtocomplete_001": "You’ll receive a new get active goal every Monday, personalized to you. You can earn physical activity points toward your goal when you:", "physicalactivity_landing_txtbodytip_001": "Being physically active is important to your overall health. If you are just starting to get more active, the safest route is to begin slowly and build up gradually.", "physicalactivity_landing_txtbodytip_002": "If you are already physically active, focus on increasing the intensity of your workouts throughout the week.", "physicalactivity_landing_txtbodytip_003": "If you have a medical condition or are or may be pregnant, talk with your doctor before engaging in physical activity.", "physicalactivity_landing_txtbodywhyimportant_001": "Being physically active is important to your overall health. If you are just starting to get more active, the safest route is to begin slowly and build up gradually.", "physicalactivity_landing_txtbodywhyimportant_002": "If you are already physically active, focus on increasing the intensity of your workouts throughout the week.", "physicalactivity_landing_txtbodywhyimportant_003": "If you have a medical condition or are or may be pregnant, talk with your doctor before engaging in physical activity.", "physicalactivity_landing_txtgoalname_001": "Eat for your health", "physicalactivity_modalactivated_txtactivatedgoal_001": "You've activated your physical activity goal", "physicalactivity_modalactivated_txtactivatedgoal_002": "You’ve just earned %1$@ for activating your goal", "physicalactivity_modalactivated_txtyourfirststartgoal_001": "Your first physical activity goal starts on %1$@", "physicalactivity_modalactivated_txtyourfirststartgoal_002": "Your weekly points target starts on %1$@", "physicalactivity_weeklygoals_txtearnreward_001": "Earn a spin or coins when you meet your goals", "physicalactivity_weeklygoals_txtweeklygoals_001": "Weekly goals", "reg_accactivtd_txtbdyaccativ_001": "Your account is now active.", "reg_accactivtd_txtbdyaccativ_002": "Your account is activated, and the features of this app will be available when your membership is active on <27 January 2022>.\nReach out to <Insurer> with questions.", "reg_accactivtd_txthgaccactiv_001": "You’re all set!", "reg_activation_confirmpass_001": "Confirm password", "reg_activation_input_001": "***************", "reg_activation_step3_txtsetlogin_001": "Set up your login details", "reg_activation_txtconfirmpass_001": "Confirm password", "reg_activation_txtcreatepass_001": "Create password", "reg_codesentmsg_001": "Enter the email address you have on record with %1$@ to receive your activation code.", "reg_notif_bdpushnotif_001": "Stay updated with important information, like when there are rewards available or a time-sensitive event.", "reg_notif_hdpushnotif_001": "<Insurer Vitality> would like to send you push notifications", "reg_privacypolicy_txtbdprivpol_001": "1.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n1.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n1.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. In egestas erat imperdiet sed euismod. Pulvinar etiam non quam lacus suspendisse faucibus interdum. Venenatis a condimentum vitae sapien pellentesque habitant morbi tristique senectus.\n\n1.1.3. Viverra orci sagittis eu volutpat odio facilisis. Fringilla ut morbi tincidunt augue interdum velit euismod. Vestibulum lectus mauris ultrices eros in cursus. Vivamus at augue eget arcu.\n\n1.1.4. Nunc sed id semper risus in hendrerit gravida. Fermentum posuere urna nec tincidunt praesent semper.\n\n2.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n2.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n2.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. ", "reg_privacypolicy_txthgprivpol_001": "Privacy Policy", "reg_step1_002": "STEP 2 OF 4", "reg_step1_input_001": "jessica.woodemail.com", "reg_step1_txtemailbody_001": "Enter the email address on record with <Insurer> to receive your activation code.", "reg_step1_txtgetcode_001": "Get your activation code", "reg_step1_txtwelcomvit_001": "Welcome to Vitality", "reg_step2_002": "STEP 2 OF 4", "reg_step2_enteractiv_001": "Enter your activation code", "reg_step2_input_001": "125X", "reg_step2_txtenteractiv_001": "Enter your activation code", "reg_step2_txtsecacc_001": "Secure your account", "reg_step2_txtverifyid_001": "Verify your identity", "reg_step2_verifyid_001": "Let’s verify your identity", "reg_step2txtcodesentmsg_001": "Your code was sent to %1$@. Don’t see it? Check your spam folder.", "reg_step3_003": "STEP 3 OF 4", "reg_step3_confirmpass_001": "Confirm password", "reg_step3_createpass_001": "Create password", "reg_step3_input_001": "**A", "reg_step3_secacc_001": "Time to secure your account ", "reg_step3_setlogin_001": "Set up your login details", "reg_step3_txtcreatepass_001": "Create password", "reg_step3_txtinput_001": "***************", "reg_step3_txtsecacc_001": "Secure your account", "reg_step3_txtsetlogin_001": "Set up your login details", "reg_step4_txtguideprivacy_001": "Your privacy matters to us. Only anonymous data is recorded. We do not capture anything that could identify individuals.", "reg_step4_txtjustonestep_001": "Just one more step!", "reg_step4_txtpersonalise_001": "Personalise your preferences", "reg_termsconditions_txtbdtcs_001": "1.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n1.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n1.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. In egestas erat imperdiet sed euismod. Pulvinar etiam non quam lacus suspendisse faucibus interdum. Venenatis a condimentum vitae sapien pellentesque habitant morbi tristique senectus.\n\n1.1.3. Viverra orci sagittis eu volutpat odio facilisis. Fringilla ut morbi tincidunt augue interdum velit euismod. Vestibulum lectus mauris ultrices eros in cursus. Vivamus at augue eget arcu.\n\n1.1.4. Nunc sed id semper risus in hendrerit gravida. Fermentum posuere urna nec tincidunt praesent semper.\n\n2.1. <PERSON>rem ipsum dolor sit amet, consectetur adipiscing elit:\n\n2.1.1. Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Massa tincidunt dui ut ornare lectus sit. Morbi tempus iaculis urna id volutpat lacus.\n\n2.1.2. Sagittis aliquam malesuada bibendum arcu vitae elementum. ", "reg_termsconditions_txthdtcs_001": "Terms and Conditions", "reg_txtstep1_001": "STEP 1 OF 4", "reg_welcome_findoutmore_001": "Find out more", "reg_welcome_insupolicy_001": "You need a qualifying <Insurer> policy to use this app. Find out more ", "reg_welcome_needqualify_001": "You need a qualifying", "reg_welcome_policyto_001": "policy to \nuse this app. ", "reg_welcome_txthealthliv_001": "Healthy living has never been this rewarding", "reg_welcome_txtinsupolicy_001": "You need a qualifying <Insurer> policy to use this app. Find out more ", "sample123": "Already have an account? <PERSON>gin", "sectionHd_lastmembershipyear": "Last membership year", "sectionHd_promotion": "Subheading", "txt_footnoteVitalityprogramme_001": "Your privacy is important to us, and your individual data will never be shared without your consent. Vitality collects steps, heart rate, sleep information, and other data that you can choose to share from your connected app or device, so that you can get rewarded for your positive lifestyle choices. ", "txt1spin_001": "%1$@ spin", "txtaccount_001": "Account", "txtactivateaccountlogin_001": "Activate your account", "txtactivatecode_001": "Activation code", "txtallowedtoread_001": "Allowed to read", "txtalreadyacc_001": "Already have an account? <PERSON>gin", "txtalreadyacc_002": "Already have an account? ", "txtapp_001": "App", "txtappcrashsettings_001": "Share app crashes and errors so that we can fix them", "txtappdevices_001": "Apps and devices", "txtapplehealth_001": "Apple Health", "txtapplehealthh_001": "Apple Health", "txtappsanddevicesfooter": "Your privacy is important to us, and your individual data will never be shared without your consent. Vitality collects steps, heart rate, sleep information, and other data that you can choose to share from your connected app or device, so that you can get rewarded for your positive lifestyle choices. ", "txtappver_001": "Vitality 3.1.2 (********)", "txtareyousureyouwanttodisconnect_001": "Are you sure you want to disconnect?", "txtauthcodesent_001": "We’ve resent your activation code", "txtautosync_001": "Automatic syncing", "txtautosyncautomaticallythroughouttheda_001": "Your data syncs automatically throughout the day", "txtautotracked_001": "Automatically tracked through a connected app", "txtavailableagaindate_001": "Available again %1$@", "txtbdconncterr_001": "A connection error has occurred. Please make sure that you are connected to a Wi-Fi or cellular network and try again.", "txtbdincrrdetail_001": "Remember to use same sign up details that <insurer> has on record.", "txtbdonestepaway_001": "All you need to do is accept our Terms and Conditions and Privacy Policy, plus set your app preferences.", "txtbdonestepaway_002": "All you need to do is set your app preferences.", "txtbdprobnerr_001": "We weren't able to verify your activation code. Please re-enter the code. If the problem persists, request a new code.", "txtbdsomewrngerr_001": "We weren't able to activate your account. Please try again.", "txtbdtcrequired_001": "If you “disagree,” you will be logged out and unable to continue until you agree with the terms and conditions.", "txtbdunknwnerr_001": "An unknown error has occurred. Please try again.", "txtbdyacccancelled_001": "If this is an error, not to worry. Contact %1$@'s support centre before %2$@ to reactivate your membership and ensure that your points and rewards are not affected.\n\nIf you have cancelled your membership, we're sorry to see you go. We've sent you a membership cancellation email which includes details of your remaining benefits and rewards. These will be available until %3$@.\n\nHave a question? Get in touch.", "txtbloodglucose_001": "Blood glucose", "txtbloodpressure_001": "Blood pressure", "txtbodyapperror_001": "Something went wrong. We're working on it to get it fixed as soon as we can.   Thanks for your patience.", "txtbodyconnectcompatibleapps_001": "Share your relevant data with us to earn Vitality points, achieve your goals, increase your status and get rewarded.", "txtbodyconnectedmsg_001": "Every time you open this app, your %1$@ data will sync.", "txtbodyforgotpass_001": "No worries. Just enter the email you use for Vitality communications, and we’ll send your instructions to reset it.", "txtbodyforgotpass_002": "No worries. You'll just need to head over to <Insurer name>'s website to reset it.", "txtbodyhowtocomplete_001": "You’ll receive a new get active goal every Monday, personalized to you. You can earn physical activity points toward your goal when you:", "txtbodymassindex_001": "body mass index", "txtbodynohistoryavailable_001": "This is where you will be able to view your physical activity goal history.", "txtbodypromofitnesscard_001": "<Get %1$@ off a new Garmin device and start earning cashback rewards!>", "txtbodyresetpass_001": "We've sent you instructions to reset your password. Check your email inbox (%1$@) and spam folder too. \n\nThen, return to log in with your new password.", "txtbodysharedata_001": "Allow Vitality to read the recommended data from your app to get points toward your weekly physical activity goal, monthly cashback goals and annual status. Permissions can be updated at any time.", "txtbodysharedata_garmin_001": "For the best experience, share all the above data with us. You can manage the data you share through your Garmin account at any time.", "txtbtnletsgo_001": "Let’s go", "txttips_drink_001": "Tips for sensible drinking", "txttips_drink_content_001": "These tips can help to maintain sensible drinking habits for those who choose to drink. Keep up your healthy choices if you don't drink alcohol - you may share this with someone who would benefit from healthier drinking habits.", "txttips_smoking_001": "Quit smoking tips that work", "txttips_smoking_content_001": "This guide offers tips for those who want to quit cigarettes. Because you don't smoke, you may want to share it with someone you know who does.", "txttips_smoking_content_002": "This guide offers proven tips on how to quit smoking. Vitality is here to support you.", "txttipstomanagecholesterol_001": "Tips to manage cholesterol", "txttips_cardio_001": "Tips for building your cardio fitness level", "txttips_cardio_content_001": "Use this guide to find a cardio-building method that suits your current routine and lifestyle.", "txtcardio_guide_001": "Cardio fitness guide", "txtcardio_intro_001": "Your fitness level is in the optimal range - continue your exercise habits to enjoy many healthy years ahead!", "txtcardio_intro_002": "Did you know that you could maximize your health when you increase your fitness level to an \"above average\" level?", "txtcardio_intro_003": "For your best health, it's recommended you improve your fitness level. This guide can help get you started.", "txtcardio_intro_004": "Your first step is to find out your VO2 Max or cardio fitness score, and make it a goal to reach the optimal (above average) cardio fitness level. This measure is often found on fitness apps and devices.", "txtconsulthealthcareprovider_001": "Consult with your healthcare provider", "txtconsulthealthcareprovider_content_001": "Check with your care provider before you start an exercise programme or make significant changes to your current one.", "txtconsulthealthcareprovider_content_002": "Check with your care provider before you start an exercise programme or make significant changes to your current one.", "txtsmoking_guide_001": "Quit smoking guide", "txtsmoking_intro_001": "Continue a smoke-free lifestyle and enjoy many healthy years ahead.", "txtsmoking_intro_002": "Congratulations on quitting smoking and staying quit. Continue your smoke-free lifestyle and enjoy many healthy years ahead.", "txtsmoking_intro_003": "Quitting smoking may be one the toughest challenges you will face. Keep in mind others have done it - and so can you!", "txtdrink_guide_001": "Drink sensibly guide", "txtdrink_intro_001": "Continue to avoid drinking for your best health.", "txtdrink_intro_002": "Continue to drink sensibly for good health and longevity.", "txtdrink_intro_003": "Your health may benefit from reducing the amount of alcohol you drink. It’s not impossible! You can do it.", "txtdrinkslowly_001": "Drink slowly ", "txtdrinkslowly_content_001": "While drinking, eat a meal or alternate alcohol with water or non-alcoholic drinks.  Doing so helps your body to gradually process the alcohol, slowing down the affects of it and helps you feel fuller so you don’t drink as much.", "txtintensity_001": "Increase intensity", "txtintensity_content_001": "For example, add hills to your usual walking or cycling route. Or add speed or high-intensity intervals to your routine.", "txtlimitdrinking_001": "Limit drinking to one or less drinks a day", "txtlimitdrinking_content_001": "Have one or less drinks a day or no more than 14 units (about 6 pints of standard strength beer, 6 glasses of wine or cider, or 6 shots of liquor) spread throughout the week.", "txtlimittoomuchatonce_001": "Limit drinking too much at once", "txtlimittoomuchatonce_content_001": "Aim for only one or two drinks per occasion. Heavy drinking sessions are harmful to your health and increase the risk for unintentional injuries and social problems.", "txtquitedate_001": "Set a quit date", "txtquitedate_content_001": "A quit date shows your commitment to quitting. Set a date about two weeks away – that way you have time to put your plan in place but it’s not too far away that you change your mind.", "txt8020rule_001": "Try the 80/20 rule", "txt8020rule_content_001": "Exercise at a steady pace for most (80%) of your workouts, while at a higher intensity for the remaining (20%) workouts. So, if you exercise 5 times in a week make one of those days a high-intensity interval day.", "txt8020rule_content_002": "Exercise at a steady pace for most (80%) of your workouts while at a higher intensity for the remaining (20%) sessions. Start with a short high intensity interval training session like the 7-minute workout one day a week.", "txtplanahead_001": "Plan ahead", "txtplanahead_content_001": "Make sure you have a safe ride home and be prepared with your “No, thank you” statement for refusing drinks as necessary.  Or change up your routine and make plans that don't involve drinking.", "txtlistreasons_001": "List your reasons to quit", "txtlistreasons_content_001": "Naming and claiming why you want to quit can be empowering. A list of reasons can act as a motivational reminder to help you manage urges to smoke.", "txtaddtime_001": "Add time to your workout", "txtaddtime_content_001": "Add up to 10% of your current exercise minutes every week.  As an example, if you exercise 30 minutes five times one week, move up to 33 minutes every session the following week, and so on.", "txtaddtime_content_002": "As an example, start by walking 20 minutes three times one week and move up to 22 minutes every session the following week, and so on.", "txtdontdrinkanddrive_001": "Don't drink and drive", "txtdontdrinkanddrive_content_001": "Even one drink can impair your ability to drive. Volunteer to drive others home and don’t drink at all.", "txtgathersupport_001": "Gather support", "txtgathersupport_content_001": "Family, friends or participation in an organised programme can provide you support during the quitting process.", "txtplantodealwithurges_001": "Plan to deal with urges", "txtplantodealwithurges_content_001": " A craving usually passes after five to ten minutes. When the urge to smoke hits, try these five \"D's\": Deep breath. Drink water or herbal tea. Distract yourself. Discuss your cravings with a friend or counsellor. Delay smoking.", "txtslowlyadd_001": "Slowly add to your routine", "txtslowlyadd_content_001": "Gradually add more intensity, time, or minutes to your routine to prevent injuries and illness.", "txtexerciseregularly_001": "Exercise regularly", "txtexmoreoften_001": "Exercise more often", "txtexmoreoften_content_001": "Add sessions to your usual routine - such as, exercise four days a week instead of three. Or add an extra 15-minute walk to your lunch break or commute on some days.", "txtmedicationsupport_001": "Get medication support", "txtmedicationsupport_content_001": "There are many types of smoking cessation aids that are proven to work such as the patch, gum, varenicline and bupropion SR. \n\nAsk your health care provider to help you decide which medications may suit you.", "txtcardiovascular_001": "Cardiovascular", "txtchildhood_001": "Childhood", "txtcholesterol_001": "cholesterol", "txtchoosereward": "<PERSON><PERSON>", "txtchooseyourgiftcard_001": "Choose your gift card", "txtcoins": "Coins", "txtcolonoscopy_001": "Colonoscopy", "txtconnectaccount_001": "Connect your account", "txtconnectapp_001": "Connect your app", "txtconnectionerror_001": "Oops, something went wrong", "txtconnectyougarmintoearnvitalitypoint_001": "Connect your Garmin account to earn Vitality points, achieve your goals and get rewarded. Your Garmin account uses data from your Garmin device.", "txtconnectyourappordevice_001": "Connect your app or device", "txtdentalcheckups_001": "Dental checkups", "txtdisclaimer_001": "Your privacy is important to us, and your individual data will never be shared without your consent. Vitality collects steps, heart rate, sleep information, and other data that you choose to share from your connected devices so that you can get rewarded for your activity.", "txtdob_001": "Date of birth", "txtdod_001": "Date of birth", "txtearncoinsactivation": "Earn  %1$@ coins for activation", "txtearncoinsreward_001": "Earn %1$@ coins in your mall digital wallet", "txtearngiftcardreward_001": "Earn a $5 gift card for activation", "txtearnspinreward_001": "Earn a spin", "txtemailadd_001": "<EMAIL>", "txtemailadd_001l_": "Email address", "txtemailerrsup_001": "Enter a valid email address", "txtemailsetting_001": "Receive emails about offers and updates", "txtenteremail_001": "<EMAIL>", "txtewrngmailadd_001": "jess.wood.email.com", "txtexpiresinxdays_001": "Expires in %1$@ days.", "txtfastingglucose_hba1c_001": "<FASTING Glucose / HbA1c>", "txtfirsttimelog_001": "First time logging in? Activate account", "txtfirsttimelogin_001": "First time logging in? ", "txtfitbit_001": "Fitbit", "txtflu_001": "Flu", "txtfobt_001": "FOBT", "txtfooter_005": "Points may not be awarded immediately and may take up to 24 hours to reflect.", "txtgarmin_001": "<PERSON><PERSON><PERSON>", "txtgastriccancer_001": "Gastric cancer", "txtgetanewgoaleveryday_001": "Get a new goal every %1$@", "txtgetreadytostartmoving_001": "Get ready to start moving!  ", "txtglaucoma_001": "Glaucoma", "txtgoaldates_001": "%1$@ - %2$@", "txtgooglefit_001": "Google Fit", "txtguideprivacy_001": "Your privacy matters to us. Only anonymous data is recorded. We do not capture anything that could identify individuals.", "txthbA1c_001": "Glycated haemoglobin (HbA1c)", "txthdapperror_001": "App temporarily unavailable", "txthdconncterr_001": "Connection error", "txthdconnectcompatibleapps_001": "Connect compatible apps", "txthdforgotpass_001": "Forgot your password?", "txthdincrrdetail_001": "Have you entered the correct details?", "txthdonestepaway_001": "You're one step away from living life with Vitality", "txthdprobnerr_001": "We have a little problem", "txthdpromofitnesscard_001": "Don’t have a fitness device? ", "txthdsharedata_001": "Share relevant data", "txthdsomewrngerr_001": "Something went wrong", "txthdtcrequired_001": "Terms and Conditions are required to move forward", "txthdunknwnerr_001": "Unknown error", "txthdwelcomebk_001": "Welcome back,\n<PERSON>", "txthealthresults_001": "Health results", "txthello_001": "Hello", "txthepatitisb_001": "Hepatitis B", "txthiv_001": "HIV", "txthowtocomplete_001": "How to complete", "txthpv_001": "HPV", "txtincludefooter_001": "<Include ability to display footer content related to biometrics in this space>", "txtinsurervitality_001": "<Insurer  Vitality>", "txtjustonestep_001": "Just one more step!", "txtkeepemailpop_001": "Keep email address populated on login", "txtlastsyncdate_001": "Last data shared: %1$@", "txtlblemailaddress_001": "Email address", "txtlblpassword_001": "Password", "txtlivercancer_001": "Liver cancer", "txtlungcancer_001": "Lung Cancer", "txtmaintenanceerror_001": "Our app is currently unavailable while we make some improvements. We'll be back soon.", "txtmammogram_001": "Mammogram", "txtmanperms_001": "Manage your preferences within the app, under Settings.", "txtmeningococcal_001": "Meningococcal", "txtmobilephone_001": "Mobile phone", "txtneedhelp_001": "Need help? Contact: ", "txtnewgoaldates_001": "New goal every Monday", "txtnotconnected_001": "You don't have any apps or devices connected.", "txtonlyqualifyingphysicalactivitygoal_001": "Only qualifying physical activity goal activity is displayed here. Refer to points history in the Profile tab for all activity events. ", "txtovariancancer_001": "Ovarian cancer", "txtpapsmear_001": "Pap smear", "txtpassreqnotmet_001": "Password requirements not met", "txtpassword_001": "Password", "txtpermissions_001": "Manage permissions", "txtpersonalise_001": "Personalise your preferences", "txtphysicalactivitygoal_001": "Physical activity goal", "txtpleasere_enteravaliddateofbirth_001": "Please re-enter a valid date of birth.", "txtpneumococcal_001": "Pneumococcal", "txtpoints_001": "Points", "txtpointsareavailableeverymembershipyear_001": "Points are available every membership year", "txtpointsreflectnote_001": "Points may take up to %1$@ hours to reflect.", "txtpointsreflecttime_001": "Points may take up to 24 hours to reflect", "txtpointstwiceamembershipyearmonthsapart_001": "%1$@ points twice a membership year, six months apart", "txtpolar_001": "Polar", "txtPTS_001": "Earn %1$@ points", "txtqualifyingqeventsandpoints_001": "Qualifying events and points", "txtrecomcalories_001": "Calories (recommended)", "txtrecomheartrate_001": "Heart rate (recommended)", "txtrecomsteps_001": "Steps (recommended)", "txtsamesignupdetails_001": "Remember to use the same sign-up details that <<Insurer>> has on record.", "txtsamsunghealth_001": "Samsung Health", "txtsamsungsupport_001": "Support for %1$@", "txtsaveemail_001": "Save my email address when I log in", "txtscreenings_001": "Screenings", "txtscreeningsandvaccinations_001": "Screenings and vaccinations", "txtscreeningspagebodycopy_001": "It is best to have regular screenings as prevention is always better than cure. Earn points for doing gender, age, and lifestyle-specific screenings.", "txtsharescreeninteract_001": "Share your screen interactions anonymously to help us improve your app experience", "txtshareyourdatawithus_001": "Share your data with us", "txtshow_001": "Show", "txtskincancer_001": "Skin cancer", "txtspamguide_001": "Be sure to check your spam folder if you don’t see the email.", "txtspinrewardearned_001": "Spin earned", "txtstrava_001": "Strava", "txtsubmitproof_001": "Submit proof", "txtsuccesfullyconnected_001": "<%1$@> successfully connected", "txtsuunto_001": "<PERSON><PERSON><PERSON>", "txtsyncyourdevicetoyourgarminaccount_001": "Sync your device to your Garmin account so your relevant data can be shared.", "txttcprivacy_001": "By logging in you agree to our Terms and Conditions \nand Privacy Policy.", "txttermcnd_001": "Terms and Conditions and Privacy Policy Need help? <insurer contact>", "txttncprivacy_001": "I agree to the Terms and Conditions and \nPrivacy Policy", "txttypesofactivities_001": "Types of activities", "txtunabletoconnectyoursamsunghealthapp_001": "We weren't able to connect your  %1$@ app at the moment due to an issue with our server. Please wait a few minutes and try again.", "txturinaryprotein_001": "URINARY PROTEIN", "txtusefinger_001": "Keep email address populated on login", "txtvaccinations_001": "Vaccinations", "txtwearabledevice_001": "Wearable device", "txtweight_001": "Weight", "txtwhyimportant_001": "Why this is important", "txtwhythisisimportant_001": "Why this is important", "txtwithings_001": "Withings", "txtyoudiditnowspinit_001": "You did it! Now spin it.", "txtyourenowconnected_001": "You're now connected", "txtyourfirstgoalstartsonmonday_001": "Are you ready to get active, %1$@? Your first goal starts on %2$@.", "txtyourspinexpiresinxdays_001": "Your spin expires in  %1$@ days.", "txtyourweeklypointstargetstartsondate_001": "Your weekly points target starts on %1$@", "txtzoster_001": "<PERSON><PERSON><PERSON>", "view_reg_txtemailbody_001": "Enter the email address on record with %1$@ to receive your activation code.", "welcome_screen_1": "Healthy living has never been this rewarding", "txtjustonestep_closer_001": "You’re one step closer to living life with Vitality", "txttncprivacy_agree_001": "I agree to the", "reg_step4_txtprivacyidentified_001": "Your privacy is important to us. We only record anonymous data, so you can’t be identified.", "txtmakesurepassmatch_001": "Please make sure your password match", "txtagreetnc_001": "You need to agree to the Terms & Conditions to continue", "txtdisagreelogout_001": "If you disagree, you’ll be logged out and unable to continue", "txtnopointsearningactivity_001": "Points pending", "txtniceyouhaveearnedfidcoins_001": "Nice! You’ve earned %1$@ fidcoins.", "physicalactivity_modalactivated_txtyourfirststartgoal_003": "We are busy adding your coins to your online store balance.", "txt1spin_002": "%1$@ coins in your mall digital wallet", "txtactivatedyouhaveveearnedfidcoins_001": "Activated! You’ve earned %1$@ fidcoins.", "txtactivatedyouhaveveearnedfidcoins_002": "Nice! You’ve earned %1$@ fidcoins.", "txtpointsreflectnote_002": "Your points may take up to 24 hours to reflect.\n\nPoints earned from physical activity also contributes toward your Vitality status.", "txtswiptetospin_001": "Swipe to spin for your reward.", "ctrl_txtwhatsonthewheel_001": "What’s on the wheel?", "txtxcoins_001": "10 x 100 coins", "txtxgiftcards_001": "5 x $5 gift cards", "txtxgiftcards_002": "1 x $5 Starbucks gift card", "txtxgiftcards_003": "1 x $5 Calm gift card", "ctrl_txtgotit_001": "Got it", "txtsubtextonreward_001": "Choose a gift card from our range of exciting partners.", "txtsubtextonreward_002": "This will be added to your total coin balance.", "txtsubtextonreward_004": "Which reward will you choose?", "txtsubtextonreward_003": "Have a great time at the movies.", "txtwonreward_001": "Great news: you earned a %1$@!", "txtwonreward_002": "Great news: you earned %1$@ coins!", "txtwonreward_003": "Great news: you earned a %1$@ gift card!", "txtwonreward_004": "Great news: you earned %1$@!", "body_EmailSettings_offers": "Receive emails with the latest offers and updates", "ctrl_btnsavingpref_your_001": "Saving your preferences ...", "txtyourspinexpiresindays_001": "Your spin expires in %1$@ days.", "txt_earn_points_001": "Earn %1$@ points", "txtrewards1081": "<PERSON><PERSON>", "txtrewards1082": "1 Spin", "txtrewards1083": "1 Spin", "txtrewards1084": "Earn 250 Coins", "txtrewards1085": "Earn 250 Coins", "txtrewards1086": "1 Spin", "txtrewards2270": "Earn 25 coins for activation", "txtmonday": "Monday", "txtpts": "pts", "txtpending": "pending", "txtgoalspending": "GOAL PENDING", "txtpointspending": "Points pending", "txtqualifyingtext": "Only qualifying physical activity goal activity is displayed here. Refer to points history in the Profile tab for all activity events.", "txtactivategoal": "Activate goal", "txtopensettings": "Open Settings", "txtapplehealth": "Apple Health", "txtvitalitymallstore": "Go to our <Vitality mall/store>", "txtpermissions": "You have deliberately declined permission(s), please go to settings to change them", "iconrewards1081": "solid gift", "iconrewards1082": "regular ferris-wheel", "iconrewards1083": "regular ferris-wheel", "iconrewards1084": "solid coins", "iconrewards1085": "solid coins", "iconrewards1086": "regular gift-card", "txtandor_001": "and/or", "txtor_001": "or", "txtfooter_003": "Your privacy is important to us, and your individual data will never be shared without your consent. <Your insurer has access to aggregate data and by sharing consent, it will not affect your policy.> Vitality collects steps, heart rate, sleep information, and other data that you choose to share from your connected devices so that you can get rewarded for your activity.", "useToLogin": "Use %1$@ to log in", "biometricSettings": "%1$@ for this app has been disabled. Go to your app settings to re-enable it.", "stepOf4": "%1$@ %2$@ OF 4", "sentActivationCodeError": "Resent your activation code failed", "clearLabel": "Clear label", "codeVerified": "Code verified", "resendInsurerSuccess": "Resend Insurer Successfully", "networkError": "Network error", "exceptionUserRegistered": "User already registered", "exceptionValidateInsurer": "Validate Insurer Code Request Failed", "exceptionTimeout": "The request timed out.", "dobValidationFailed": "Date of Birth validation failed.", "ctrl_navbar_tabhome": "Home", "ctrl_navbar_tabrewards": "Rewards", "ctrl_navbar_tabhealth": "Health", "ctrl_navbar_tabprofile": "Profile", "txtentercodeanddob_001": "Enter your activation code and date of birth", "ctrl_btnnext_001": "Next", "ctrl_btnrelatedfaqs_001": "Related FAQs", "ctrl_btnhelpcentre_001": "Help centre", "txt_footer_001": "While the Vitality programme is based on state-of-the-art science, it can’t replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "home_landing_txtheaderplatinum_001": "Well done, you’ve reached %1$@ status", "txt_homeintro_001": "You are on %1$@ Vitality status and need %2$@ points to reach %3$@", "txtbronzestatus_001": "You are on %1$@ Vitality status and need 2,000 points to unlock a 2.5% insurance discount.", "txtbronzestatusaia_002": "You are on %1$@ Vitality status and need %2$@ points to reach %3$@ and earn 5% insurance discount.", "txtsilverstatusaia_001": "You are on %1$@ Vitality status and need %2$@  points to reach %3$@ and earn a 10% insurance discount.", "txtgoldstatusaia_001": "You are on %1$@ Vitality status and need %2$@  points to reach %3$@ and earn a 20% insurance discount.", "txtvitalitystatus_001": "Status", "spend": "Spend", "txtcoins_001": "Gems", "txt_goodmorningwithname_001": "Good morning, %1$@", "txtgoodmorning_001": "Good morning", "txt_goodafternoonwithname_001": "Good afternoon, %1$@", "txtgoodafternoon_001": "Good afternoon", "txt_goodeveningwithname_001": "Good evening, %1$@", "txtgoodevening_001": "Good evening", "txtnopromotion_001": "Sorry, this article isn't available right now.", "txtnopromotionbody_001": "Come back later to read more wellness wisdom.", "chip_workoutDuration": "Workout duration", "txtactivationwithrewardsuccess1": "You’ve just earned 25\ncoins for activating\nyour goal", "txtactivationsuccess": "Get ready to start\nmoving!", "txtconnectionerrorbody": "This page is unable to load due to issues with our server. We’re working to get this fixed as quickly as possible - thanks for your understanding.", "txtachieved": "Achieved", "txt_points_001": "pts", "txtpointsreflectnoteandvitlaitystatus_002": "Your points may take up to 24 hours to reflect.\n \nPoints earned from physical activity also contributes toward your Vitality status.", "ctrl_btnchoosegiftcard_001": "Choose gift card", "txtexpires_001": "Expires: %1$@", "txtexpired_001_date": "Expired: %1$@", "txtusethecodetoredeem_001": "Use this code to redeem your reward", "txtusethecodetoredeem_002": "Use the discount and pin codes to redeem your reward.", "txtdiscountcode_001": "Discount code", "ctrl_btnrevealcode_001": "Reveal code", "txtpincode_001": "Pin code", "ctrl_btnviewallgiftcards_001": "View all gift cards", "ctrl_btncopy_001": "Copy", "ctrl_btnvisitwebsite_001": "Visit website", "txtthisbarcodecanbeusedforinstorepurchases_001": "Going in-store? Use this barcode to get a discount on your purchase.", "txtvisitthewebsitetoredeemyourgiftcard_001": "Visit the website to redeem your gift card.", "txtlinkafitnessdeviceorapp_001": "Connect your app or device", "txtbodydeviceapplink_001": "Share your relevant data with us to earn Vitality points, achieve your goals, increase your status and get rewarded.", "ctrl_txtmaybelater_001": "Maybe later", "ctrl_txtconnectnow_001": "Connect now", "txtselectyourgiftcard_001": "Choose your gift card", "ctrl_btnselectlater_001": "<PERSON><PERSON> later", "login_errfaceiddchange_txthgfaceidchange_001": "%1$@ changes", "login_errfaceidchange_txtbdfaceidchange_001": "A %1$@ has been added or removed from this device. Would you like to continue using %2$@? If no, the use of %3$@ will be disabled from this app.", "ctrl_btnyescontinueusingfaceid_001": "Yes, continue using %1$@", "ctrl_btnnodisableusingfaceid_001": "No, disable %1$@", "txtarchivedgiftcards_001": "Archived gift cards", "txtnogiftcardsarchived_001": "No gift cards archived", "txtnogiftcardsarchivedcopy_001": "You can archive gift cards from your gift card screen.\n\nExpired gift cards will automatically be archived and appear here.", "txtgiftcard_001": "%1$@ gift card", "txtyourgiftcardisbeingprepared_001": "Your gift card is being prepared", "txtyourgiftcardisbeingpreparedcopy_001": "This can sometimes take a while to retrieve the code.\nCheck back later to see if it’s available", "txtyourgiftcards_001": "Your gift cards", "txtgiftcards_001": "Gift cards", "txthowtoearncoins_001": "How to earn coins", "txtexpiressoon_001": "EXPIRES SOON", "ctrl_btnarchive_001": "Archive", "txtnogiftcards_001": "No gift cards available", "txtyourgiftcardswillappearhere_001": "Your gift cards will appear here.", "txtnoexpirydate_001": "No expiry date", "txtyouhaveXgiftcards_001": "You have %1$@ gift cards", "txtyouhaveXgiftcard_001": "You have %1$@ gift card", "ctrl_btnsubmitresultsandproof_001": "Submit results and proof", "txtnsubmitproof_001": "Submit results and required proof of participation", "txtpoints_002": "200-2,000 Points PER EVENT", "txt_fitnessevents": "Fitness events", "txtpersonalisegoalsbody_001": "We’ll continue to personalise and update your goal recommendations as we learn more about you", "txtpersonalisegoals_001": "We’re personalising your goals…", "txtgetweeklygoals_001": "Get weekly \n goals", "txteachmondaywellrecommendthreegoalsforyou_001": "Each week, we’ll recommend three lifestyle goals to help you build and maintain healthy habits.\n\nSelect one goal you’d like to complete.", "txtenjoyrewards_001": "Enjoy\nrewards", "txtwhenyoucompleteyourgoal_001": "Achieve your goal each week to add 100 coins towards your fuel bar. On completion of your fuel bar, you earn a gift card.", "txttellusmoreaboutyou_001": "Tell us more\nabout you", "txtthemoreweknow_001": "Answer three quick questions so that we can recommend the best goals for you.", "ctrl_btngetstarted_001": "Get started", "txt_iunderstand_001": "I understand", "txtcsubmitproofofparticipation_001": "Submit proof of participation", "txtsupportexplanation_001": "We’ll only accept verified proof that you’ve completed an event.", "txteventdetails_001": "Event details", "txtproof_001": "Proof", "ctrl_btnsubmit_001": "Submit", "txtFitnesseventsubmitted_001": "Proof submitted successfully", "txtpointsmaynotreflect_immediately_001": "Your points may not reflect immediately.", "txtfooter_006": "Your points may not reflect immediately.", "txthowtoearnpoints_001": "How to earn points", "txtsubmitted_001": "SUBMITTED", "txteventscompletedwithinthelastsixmonthsqualify_001": "Events must be completed within the last six months", "txtuptopointspersubmission_001": "Earn up to %1$@ points per event", "txteventtype_001": "Event type", "txt_label_resultURL_001": "Result URL", "txtpleaseenteravalidurl_001": "Please enter a valid URL", "ctrl_btnupload_001": "Upload", "txtformatnotrecognised_pleasetryagain_001": "File format not recognised. Please try again.", "txtforyou_001": "For you", "txtwhyitsimportantcopy_001": "Being sedentary, or sitting too much, can be harmful to your health. The more you move, the more calories you burn, which in turn will help you get closer to your goal weight.", "txtwlgonbaordingerror_001": "Oops, something went wrong", "txtwlgonboaringerror_body_001": "We're unable to save your answers and activate your weekly lifestyle goals right now.\n\nPlease try again.", "txtwlgonboaringerror_body_002": "We're unable present your personalised weekly lifestyle goals right now.\n\nPlease try again.", "txtpoints_003": "0/20,000 POINTS", "txtgetproof_001": "Submit completed form from healthcare provider", "ctrl_btnsubmitproof_001": "Submit form", "ctrl_btnsubmitproof_002": "Download submission form", "txtnote_001": "Get %1$@ off your screenings and vaccinations when you go to any of these participating partners.", "txtpointsforsubmitting_001": "%1$@ points for submitting", "txtabout_001": "About", "txtmammogramaboutcopy_001": "A mammogram uses X-rays to create detailed images of the breast tissue to detect and diagnose breast-related conditions, such as breast cancer. Breast cancer starts when cells in the breast begin to grow out of control. These cells usually form a tumour that can often be seen on an X-ray or felt as a lump. The tumour is cancerous if the cells grow into the surrounding tissue or spreads to other areas of the body. Although many types of breast cancer can cause a lump in the breast, not all do. Any breast lump needs to be checked by a healthcare provider to determine whether it’s benign or cancerous.", "ctrl_btnseeless_001": "See less", "body_submitproofparagraph_001": "Confirm that you’ve completed a screening or vaccination by uploading your submission form as proof.", "txttype_001": "Type", "txtselecttype_001": "Select type", "txtdateofactivity_001": "Date of activity", "txtdateofactivitynotedate_001": "Your activity must have been completed between %1$@ and %2$@.", "txtbody_cholestrolAbout_001": "PDF, JPG or PNG", "txtbody_cholestrolAbout_002": "5mb max", "txtbody_cholestrolAbout_003": "Error uploading file. Please try again", "txtbody_cholestrolAbout_004": "File format not recognised. Please try again", "txtbody_cholestrolAbout_005": "Exceeds the maximum file size. Please try again", "txtbuttondone_001": "Done", "txtbuttondone_002": " Submit another activity", "txtpoints_004": "2,000/20,000 POINTS", "txtcomplete_001": "Complete", "txtcompleteddate_001": "Completed: %1$@", "iconrewardsclip": "clipboard-check", "iconrewardsarrowdown": "arrow-down-to-bracket", "txthealth_001": "Health", "txtlearnmoreaboutyourhealth_001": "Learn more about your health", "txtcompleteha_001": "Complete the health assessment to find out your Vitality Age and the most important thing you can do for your health.", "txtforaccurateresults_001": "For more accurate results, complete your Vitality Health Check to provide us with your latest biometrics.", "txtrelatedactivities_001": "Related activities", "txthealthassessment_001": "Health assessment", "txthealthcheck_001": "Health check", "ctrl_textlearnmoreaboutthescience": "Learn more about the science", "ctrl_helpcentre_001": "Help centre", "txtstateoftheartscience_001": "While the Vitality programme is based on state-of-the-art science, it can’t replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "txtcompletehctoget_001": "Complete your Vitality Health Check to get more accurate, personalised recommendations.", "txttoimprove_001": "To improve", "txtdoingwell_001": "Doing well", "txtunknown_001": "Unknown", "txthealthpriority_001": "HEALTH PRIORITY", "txtimproveyoursleep_001": "Improve your sleep", "txtfuturehealth_001": "Future Health", "txtdiscoverthehealthiestversionofyou_001": "Discover the healthiest version of you.", "txtcompleted_on_date_001": "Completed on %1$@", "ctrl_btnseeallactivities_001": "See all activities", "txtvitalityageandhealthresults_001": "Vitality Age and health results", "txtexploreyourresults_001": "Vitality Age is a measure of how healthy you are relative to your actual age.", "txtloweryourbloodpressure_001": "Lower your blood pressure", "txtvitalityage_001": "Vitality Age", "txtwhyitsimportant_001": "Why it's important", "ctrl_btnselectgoal_001": "Select goal", "txtselectandcompletealifestylegoalrecommendedforyou_001": "Choose a lifestyle goal recommended for you", "txtcheckbackonmonday_001": "Every %1$@, you’ll receive new goal recommendations.", "ctrl_btnseemore_001": "See more", "txtyourlifestylegoalismet_001": "Well done! You’ve\nachieved your goal", "txtyourlifestylegoalismetcopy_001": "Your completed profile helps us recommend more personalised goals for you.", "ctrl_btnviewsummary_001": "View summary", "txtscreeningorvaccination_001": "Screenings and vaccinations", "ctrl_btnsubmitanotheractivity_001": "Submit another activity", "txteventdate_001": "Event date", "txtonrewardsubtext_001": "Choose a gift card from our range of exciting partners.", "txtrewards_001": "Rewards", "ctrl_btngotothefidelidadestore_001": "Go to the Fidelidade store", "txtweeklyrewards_001": "Weekly rewards", "txtfooter_007": "The partners represented are not sponsors of or affiliated with Vitality. The logos and trademarks used are owned by each partner and/or their affiliates. Visit our partner websites to view their Terms and Conditions.", "txtonreward_001": "Well done! Your coins added up to a %1$@ gift card.", "txtonrewardfidcoins_001": "Well done! %1$@ coins have been added to your Fidelidade store.", "txtaboutthispartner_001": "About this partner", "txtgiftcardpartners_001": "Gift card partners", "txtonrewardsubtext_002": "Earn more coins for achieving your weekly goals.", "txtenjoytherewards_001": "Enjoy rewards and exclusive benefits just for you.", "ctrl_btnilldothislater_001": "I’ll do this later", "txthowtoearncoinsintrotext_001": "Achieve your weekly physical activity goal to get a spin on the wheel. Earn as much as <<X>> coins for each spin. Get a %1$@ gift card when your coins add up to %2$@.", "txthowtoearncoinsintrotext_002": "Achieve your weekly physical activity goal to get a spin on the wheel. Earn as much as <<X>> coins for each spin. Achieve your lifestyle goal to earn <<X>> coins.\n\nGet %1$@ gift card when your total coins add up to %2$@.", "txthowtoearncoinsintrotext_003": "Achieve your weekly goals to earn coins. Get a %1$@ when your coins add up to %2$@.", "textcoins_002": "coins", "txtselectimage_001": "Select image", "txt_takeaphoto_001": "Take a photo", "ctrl_txtselectfromgallery_001": "Select from gallery", "txtexercise_guide_001": "Exercise guide", "txtexercise_intro_001": "Continue your exercise routine to maximize your healthspan and lifespan alike.", "txtexercise_intro_002": "Well done, you're meeting the recommended amount of physical activity! That's great news for your health. But, did you know that for most healthy adults, doing more is even better for your health and longevity? Consider doing more as your lifestyle and ability allows.", "txtexercise_intro_003": "Did you know regular exercise helps to lower blood pressure? 30 minutes of moderate intensity physical actvity on most days (a total of 150 minutes every week) is enough to experience this benefit.", "txtexercise_intro_004": "You likely know that exercising regularly is one way to help manage weight. But did you know exercise also has many other health benefits like better mental health, improving muscle and bone health and lowering risk for premature death and certain cancers? Aim to build up to at least 150 minutes of moderate-intensity activity weekly.", "txtexercise_intro_005": "Did you know that regular exercise has mental health benefits as well as physical health benefits? Exercise helps boost mood and people who meet the minimum amount experience less symptoms of anxiety and depression. Aim to build up to at least 150 minutes of moderate-intensity activity weekly.", "txtexercise_intro_006": "You may be missing out on all the mental and physical health benefits of regular exercise like more energy and better sleep.\n\nGradually add physical activity that you enjoy and can fit in to your everyday lifestyle to gain these benefits.", "txttips_excercise_001": "Tips for adding physical activity to your lifestyle", "txttips_excercise_content_001": "Review the tips below for living a physically active lifestyle.", "txtrecommendations_001": "What are the recommendations?", "txtrecommendations_content_001": "For good health, experts recommend that adults participate in at least 150 minutes of moderate intensity, 75 minutes of vigorous intensity or a combination thereof, weekly, along with 2 sessions of muscle strengthening exercises.", "txtwhatcounts_001": "What counts?", "txtwhatcounts_content_001": "Any activity that gets your heart pumping and breathing rate up, like walking, cycling, swimming or dancing, counts towards your physical activity target. Other types of physical activity like housework or raking leaves count too, as long as it's continuous.", "txtfitmorein_001": "How can I fit more in?", "txtfitmorein_content_001": "Think about your everyday habits and where you might be able to sneak in some exercise. Add more steps to your day by getting off the train or bus stop one stop further from your destination. Make time to play with your children or grandchildren. ", "txtavoidboredom_001": "How can I avoid boredom?", "txtavoidboredom_content_001": "Think outside the box! Find something you enjoy like dancing, a new sport, take a class with a friend or go for a hike in nature. Listen to music, your favorite playlist or podcast while exercising. Enter a walk, run or cycle event and challenge a friend to a friendly competition.", "txtexcsafely_001": "Exercise safely", "txtexcsafely_content_001": "Remember these tips for avoiding illness and injury while working out: Stay hydrated. Warm up and cool down at a slower pace. Alternate days of strength training so you don't work the same muscle group two days in a row. Stretch regularly. Have at least one rest day.", "txtexcsafely_content_002": "Gradually increase your pace, minutes or sessions to your routine to prevent injuries and illness. Warm up and cool down at an easy pace before and after exercise.", "txtconsultwithyourdoctor_001": "Consult with your healthcare provider", "txtconsultwithyourdoctor_content_001": "If you were to make significant changes to your current routine, consult with your healthcare provider first.", "txtconsultwithyourdoctor_content_002": "Check with your healthcare provider before you start an exercise programme or make significant changes to your current one.", "txtmentalwellbeing_guide_001": "Mental wellbeing guide", "txtmentalwellbeing_intro_001": "Continue to cope well with the stress in your life.", "txtmentalwellbeing_intro_002": "There are self-care tools you can use to promote optimal mental well-being.", "txttips_mentalwellbeing_001": "Tips to build resilience and manage stress", "txttips_mentalwellbeing_content_001": "This guide offers tips for strengthening your mental health.", "txtprioritizesleep_001": "Prioritize sleep", "txtprioritizesleep_content_001": "You're meeting the recommended amount of sleep regularly - brilliant. Getting enough sleep is important to your mental health.\n\nIf you find that your quality of sleep is suffering or affecting your physical or mental health, or is a result of poor mental health, reach out to your doctor or therapist.", "txtprioritizesleep_content_002": "Regularly sleeping more than 9 hours a day may be an indication of a medical or mental health problem. Speak with your doctor or therapist about your sleep habits.", "txtprioritizesleep_content_003": "It's not always possible but, for your mental health, prioritise sleep and find a way to get a good night's sleep. On average, people who get at least 7 hours of sleep regularly have better mental and physical health.\n\nIf you're having trouble with insomnia or experiencing other reasons for not sleeping well, speak with your doctor or therapist about your sleep habits.", "txtconnectwithothers_001": "Connect with others", "txtconnectwithothers_content_001": "Build a strong social network of friends and family to help you strengthen your mental health and be prepared for stressful times. You don't need a large number of people you can count on but having a few strong connections is important for your mental wellbeing.", "txthealthymindset_001": "Maintain a healthy mindset and thoughts", "txthealthymindset_content_001": "Boost your mental health with a positive mindset. Give yourself a pep talk every day. Be mindful of negative thoughts and practice letting them go or replacing them with positive thoughts.", "txtcalmingtechniques_001": "Practice calming techniques", "txtcalmingtechniques_content_001": "Deep breathing, progressive muscle relaxation, and visualisation are proven methods that you may want to try when you have a stressful moment or are experiencing chronic stress.", "txtresiliencetechniques_001": "Practice resilience building techniques", "txtresiliencetechniques_content_001": "Try meditation, mindfulness, and yoga on a regular basis, to prepare you for coping better with stressful moments or events. When you're resilient, instead of stress becoming debilitating, it is something you're able to better accept, mourn, cope with, and learn from.", "txtdiabetes_guide_001": "Healthy diabetes guide", "txtdiabetes_intro_001": "Well done, your HbA1c is in a healthy range! This means you're at lower risk for diabetes-related medical problems.", "txtdiabetes_intro_002": "For most people with diabetes, an HbA1c below 7% is recommended. Yours is above that. Speak with your healthcare provider to better understand if it may be putting you at higher risk for diabetes-related medical problems and how you might improve it.", "txtdiabetes_intro_003": "Talk with your healthcare provider about having an HbA1c test done. HbA1c is an indicator of your average blood sugar over time. It's an important test to have measured to help understand if your blood sugar is well-managed.", "txttips_diabetes_001": "Tips to manage diabetes", "txttips_diabetes_content_001": "The healthy habits in this guide can help keep or bring blood glucose levels into range, reduce your risk for developing complications from diabetes, and promote overall wellness.", "txtcheckbloodsugarreg_001": "Check your blood sugar level regularly", "txtcheckbloodsugarreg_content_001": "When you regularly monitor your blood sugar level, you can see patterns that will help you and your healthcare team make appropriate plans to improve your diabetes care.", "txteatwell_001": "Eat well", "txteatwell_content_001": "Better regulate blood glucose with these guidelines in mind: Eat a variety of foods. Limit added sugars. Eat a fibre-rich diet. Eat at regular times. Include complex carbohydrates, lean protein, and healthy fats in all snacks and meals.", "txteatwell_content_002": "You don’t have to completely eliminate sugar, but do limit foods that make you feel sluggish or cause your energy to crash like fried foods, sweets, and beverages with added sugars. Instead, focus on eating a variety of vegetables, fruits, grains, legumes, lean proteins and low-fat dairy or dairy alternative.", "txteatwell_content_003": "Follow a heart-healthy eating pattern, especially reducing alcohol, sodium and for some people, increasing potassium. Try the Dietary Approaches to Stop Hypertension (DASH) eating plan. It's simple to follow, includes common foods, and has been proven to reduce blood pressure.", "txteatwell_content_004": "Regularly eating healthily gives the body the nourishment it needs to work properly and protects it from many medical conditions like high blood pressure.", "txteatwell_content_005": "Follow a heart-healthy eating pattern, especially reducing alcohol, sodium and for some people, increasing potassium. Try the DASH (Dietary Approaches to Stop Hypertension) eating pattern. It is simple to follow, includes common foods, and has been proven to reduce blood pressure.", "txteatwell_content_006": "A healthy eating plan includes regularly eating a variety of foods. The DASH diet (Dietary Approaches to Stop Hypertension) and Mediterranean-style eating plans are recommended for long-term healthy eating and can help with weight control.", "txteatwell_content_007": "Find a weight loss eating plan that is right for you. Low-fat, low-carb and high-protein, and plant-based plans are a few good choices. The DASH (Dietary Approaches to Stop Hypertension) and Mediterranean-style eating plans are recommended for long-term healthy eating and often result in weight loss.", "txttakemedasdirected_001": "Take medication as directed", "txttakemedasdirected_content_001": "When you take your medication according to your healthcare provider’s instructions, you will have better control of your blood sugar and over time, experience fewer diabetes-related medical problems.", "txtbeactive_001": "Be active", "txtbeactive_content001": "Continue to be active with a goal of 30 minutes of exercise, most days. Perform muscle strengthening exercises at least twice a week. Avoid sitting for long periods of time.\n\nTalk with your healthcare team to find out your blood sugar target range while exercising so that you can continue to workout safely.", "txtbeactive_content002": "Physical activity is encouraged because it’s a key part of managing blood glucose levels, and has many additional health benefits. First, talk with your health care team to find out your safe blood sugar target range while exercising. Gradually add more exercise to your daily routine - remember 10 minutes at a time can add up. Most of all, avoid sitting for long periods of time.", "txtwatchyourweight_001": "Watch your weight", "txtwatchyourweight_content_001": "Research shows losing 5% to 7% of your body weight can lower your blood glucose level, reverse prediabetes and reduce the risk for type 2 diabetes.", "txtwatchyourweight_content_002": "Follow a healthy eating pattern and exercise regularly to manage weight and keep blood sugar steady.", "txtwatchyourweight_content_003": "Maintain a healthy weight - it helps to manage blood glucose levels among other health benefits.", "txtwatchyourweight_content_004": "Good news, your weight and waist measure fall into a healthy range. Being at a healthy weight helps to manage blood glucose levels and decreases your risk for diabetes-related complications like heart disease.", "txtwatchyourweight_content_005": "Your body better regulates blood sugar when weight and waist circumference are in an optimal range and your risk for complications like heart disease is decreased. Follow a healthy eating pattern, exercise regularly, and take medication as directed to help manage weight.", "txtwatchyourweight_content_006": "Studies have shown for every kilogram (2.2 pounds) of weight lost, you will improve your blood pressure by one point. Consider setting a goal to lower your weight by focusing on eating more mindfully.", "txtwatchyourweight_content_007": "Studies have shown for every kilogram (2.2 pounds) of weight lost, you will improve your blood pressure by one point. Consider setting a goal to lower your weight by focusing on eating more mindfully.", "txtwatchyourweight_content_008": "Continue to maintain a healthy weight - as weight creeps up, blood pressure often does too.", "txtwatchyourweight_content_009": "Continue to keep your weight in the optimal range to manage cholesterol.", "txtwatchyourweight_content_010": "Continue to watch your weight to help maintain healthy cholesterol levels. Having a high weight is linked to poor cholesterol levels.", "txtwatchyourweight_content_011": "Having a high weight is linked to higher LDL levels. Losing weight may help manage cholesterol. Start small with eating mindfully for your good health.", "txtreceivecare_001": "Receive care as recommended", "txtreceivecare_content_001": "Visit your healthcare provider team and have lab and screening tests done per their direction.\n\nImportant tests for people with diabetes include blood pressure readings, a cholesterol test, kidney disease screening, a diabetes eye exam, and screening for depression. Speak with your healthcare provider to find out how often you should receive these tests.", "txtplantbased_guide_001": "Guide to eating plenty of plant-based foods", "txtplantbased_intro_001": "Regularly eating healthily gives the body the nourishment it needs to work properly and protects it from many medical conditions.", "txtplantbased_intro_002": "Continue your commitment to eating healthily.", "txttips_plantbased_001": "Tips for eating more plant-based foods", "txttips_plantbased_content_001": "A healthy eating plan includes regularly eating a wide variety of food including fruits, vegetables, peas, beans, lentils and whole grains.", "txthowmanysohuldIeat_001": "How many should I eat?", "txthowmanysohuldIeat_content_001": "Good news! You're meeting the optimal number of servings of at least four vegetables, three fruits and three whole grain foods daily along with three servings of legumes (beans, peas and lentils) weekly.", "txthowmanysohuldIeat_content_002": "For good health, nutrition guidelines recommend eating five fruits and vegetables, including legumes, every day and to make half your grains whole grains.\n\nResearch also shows that for optimal health and longevity,  eat at least four vegetables, three fruits, and three whole grain foods daily as well as three servings of legumes weekly. It might take some time to get there, but you can do it!", "txthowmanysohuldIeat_content_003": "Speak with a dietitian or nutrition educator to find foods that have the nutrients you may be missing from restricting fruits, vegetables, peas, beans, lentils or whole grains.", "txtchooserawandwhole_001": "Choose raw and whole when you can", "txtchooserawandwhole_content_001": "Choose fresh, frozen, cooked, canned or dried fruits and vegetables. When you do, opt for those without added sugar or salt. When reasonable, eat the skin as if often provides even more health benefits.", "txtchooserawandwhole_content_002": "Having a restriction on common foods can be challenging. Reach out to a dietitian or nutrition educator on tips for how others have been successful in restricting fruits or vegetables, while also making sure you don't miss out on key nutrients.", "txteatmorecolour_001": "Eat more color", "txteatmorecolour_content_001": "A dietician or nutrition educator can give you ideas to include suitable alternatives for fruit or vegetables so that you don't miss out on key nutrients.", "txteatmorecolour_content_002": "Again, reach out to a dietitian or nutrition educator on tips for how others have been successful in restricting fruits or vegetables, while also making sure you don't miss out on key nutrients. Each color has unique nutritional benefits and it would be beneficial if you could find at least a select few fruits or vegetables as part of your usual eating patterns. Try red, yellow, orange, white, tan, brown, green, blue and purple ones!", "txtsneakbeansandpeas_001": "Sneak beans and peas into other dishes", "txtsneakbeansandpeas_content_001": "Continue to eat plenty of legumes like beans, peas and lentils. They're a good source of fibre, vitamins and minerals that help manage weight, keep blood sugar stable and promote overall wellness.", "txtsneakbeansandpeas_content_002": "Try adding beans, peas or lentils into soups and salads to reach the recommended amount for better health and longevity.", "txtsneakbeansandpeas_content_003": "There are alternatives to legumes - make sure to speak to your dietician or nutrition educator for dietary input.", "txtoptforwholegrains_001": "When you eat grains, opt for whole grains more often", "txtoptforwholegrains_content_001": "Continue to eat whole grains for better health. If you find yourself getting bored, mix it up with some less common whole grains like quinoa, amaranth and sorghum. Remember to look out for added sugar and salt when eating whole grain products.", "txtoptforwholegrains_content_002": "Swap refined foods for whole grain foods like white rice for brown or wild rice and white bread for wholegrain bread. Commit to start your day with a whole grain breakfast and eat oats, wholegrain cereal, toast or buckwheat pancakes.", "txtoptforwholegrains_content_003": "Speak to your dietician or nutrition expert about different ways of including the important nutritients found in grain, in your diet.", "txthealthyeating_guide_001": "Guide to enjoying eating healthily", "txthealthyeating_intro_001": "Regularly eating healthily gives the body the nourishment it needs to work properly and protects it from developing many medical conditions.", "txthealthyeating_intro_002": "Continue your commitment to eating healthily.Continue your commitment to eating healthily.", "txttips_eatinghealthy_001": "Tips for enjoying meals", "txttips_eatinghealthy_content_001": "Opt for foods that make you feel more energized in place of foods that can make you feel sluggish or cause your energy to crash.", "txtsweetcravings_001": "Manage sweet cravings", "txtsweetcravings_content_001": "Try the 5 D's when you feel a craving for a sugar-sweetened coffee, tea, soft drink or other sweet drink: Distract yourself. Delay drinking - a craving often goes away in 15 minutes. Drink water or herbal tea instead. Deeply breathe. Discuss what's on your mind with a friend.", "txtsweetcravings_content_002": "Some people crave sugar when feeling stressed. Try other ways to keep your emotions in check like stepping outdoors for fresh air or practicing deep breathing.", "txtsugarswaps_001": "Try these sugar swaps", "txtsugarswaps_content_001": "Try chopped fruit on cereal instead of sugar or honey. Slowly cut back on sugar in your coffee or tea, until your taste buds adjust. Use cinnamon, vanilla, lemon, or other natural flavors instead of sugar.", "txtherbsandspices_001": "Use herbs and spices for flavor", "txtherbsandspices_content_001": "Instead of salt, try ground pepper, dry mustard powder, garlic, onion, curry powder or ginger. Add fresh or dried herbs such as basil, chives, cilantro, mint, oregano, parsley, rosemary, sage, tarragon and thyme. Use lemon or lime juice and vinegar to add extra flavor to your food.", "txtherbsandspices_content_002": "Try these healthy options for flavoring foods: Freshly ground pepper or dry mustard powder. Lemon juice, lime juice and vinegar. A sprinkle of dried herbs. Chopped fresh herbs such as basil, chives, cilantro, mint, oregano, parsley, rosemary, sage, tarragon and thyme. Use garlic, chili, onion, curry powder, ginger, shallots or leeks.", "txtflavourcombos_001": "Be inspired by these flavor combinations", "txtflavourcombos_content_001": "Instead of using salt, sugar or a saturated fat to flavor dishes, try these: Make salad dressing with, olive oil, garlic, mustard seeds, lemon juice and black pepper. Instead of butter or sour cream on baked potatoes, try rosemary, garlic and olive oil. Season chicken with, sage, tarragon, garlic, white wine and chili.", "txtprotein_guide_001": "Guide to choosing lean protein", "txtprotein_intro_001": "A regular, healthy eating pattern gives your body the nourishment it needs to work properly and protects it from many preventable conditions including heart disease, diabetes and certain types of cancer. Choose good sources of protein such as poultry, seafood, nuts and lean cuts of red meat like loin, tenderloin, and trim the fat.", "txtprotein_intro_002": "Stay committed to eating healthily. Choose good sources of protein such as poultry, seafood, nuts and lean cuts of red meat like loin, tenderloin, and trim the fat.", "txtfreshorfrozeninsteadofprocessed_001": "Opt for fresh or frozen instead of processed meat", "txtfreshorfrozeninsteadofprocessed_content_001": "Continue to limit or avoid processed meat like deli meat, sausage and bacon.", "txtfreshorfrozeninsteadofprocessed_content_002": "Replace processed meat (like deli meats, sausage and bacon) with fresh, frozen lean meat, poultry, or seafood. That way you can still satisfy your desire for meat but without the added preservatives and salt found in processed meats which can be harmful to health.", "txtchooseredleancuts_001": "If you choose red meat, eat lean cuts", "txtchooseredleancuts_content_001": "Continue to avoid red meat like beef, pork and lamb for your good health and longevity.", "txtchooseredleancuts_content_002": "Continue to limit or avoid red meat like beef, pork and lamb. If you choose to eat it, opt for the leanest cut like loin, tenderloin or round, and trim the fat.", "txtchooseredleancuts_content_003": "When you choose to eat beef, pork or lamb, eat the leanest cut like loin, tenderloin or round, and trim the fat.", "txthealthierprep_001": "Use healthier prep methods", "txthealthierprep_content_001": "When preparing seafood, fish, poultry and meat, try a healthier way like baking, broiling, poaching or air frying. Experiment with olive oil and natural herbs and spices instead of butter or salt for flavoring. ", "txtvarietyproteinsources_001": "Choose a variety of protein sources", "txtvarietyproteinsources_content_001": "Try plant-based protein options like beans, peas and lentils; or try eggs, low-fat dairy and dairy alternatives.\nIf you follow a vegan or vegetarian style eating plan, speak with a dietitian to make sure you don't miss essential nutrients like specific proteins, vitamins B12 and D, and calcium.", "txtvarietyproteinsources_content_002": "Try plant-based options like nuts and seeds; or get protein in your diet from eggs, low-fat dairy and dairy alternatives.\nIf you follow a vegan or vegetarian style eating plan, speak with a dietitian to make sure you don't miss  essential nutritients like specific proteins, vitamins B12 and D, and calcium.", "txtvarietyproteinsources_content_003": "Try plant-based options like nuts, seeds, beans, peas and lentils; or get protein in your diet from eggs, low-fat dairy and dairy alternatives.\nIf you decide to follow a vegan or vegetarian style eating plan, speak with a dietitian to make sure you don't miss essential nutrients like specific proteins, vitamins B12 and D, and calcium.", "txtmediterraneanstyle_001": "Try Mediterranean-style eating", "txtmediterraneanstyle_content_001": "For good health try this style which is typically high in monounsaturated fats, especially olive oil, and low in saturated and trans fatty acids. It's also high in fruit, root and green leafy vegetables, beans, peas, lentils, nuts, seeds, whole-grains and Omega-3-rich fatty fish. Red meat is eaten infrequently and in small amounts.", "txtbloodpressure_guide_001": "Healthy blood pressure guide", "txtbloodpressure_intro_001": "An optimal blood pressure contributes to a longer and healthier life.  Good news, yours is in the optimal range!", "txtbloodpressure_intro_002": "Your blood pressure is elevated above the standard optimal range. However, due to your medical history, the target blood pressure number that you and your doctor have set may be different. Take action toward meeting that goal.", "txtbloodpressure_intro_003": "Due to your medical history of hypertension (high blood pressure), it's important to know and monitor your blood pressure. Ask your healthcare provider what your goal number is and take action toward watching and reaching it.", "txtbloodpressure_intro_004": "An optimal blood pressure contributes to a longer and healthier life.  Good news, yours is in the optimal range!", "txtbloodpressure_intro_005": "Your blood pressure is elevated above the standard optimal range. Please speak with your healthcare provider about this measure as you may be at risk for developing health problems like a heart attack or stroke, or development of vision loss, kidney disease, or cognitive decline.", "txtbloodpressure_intro_006": "Your results show your blood pressure is in the range of someone who has hypertension. Although, one test is not be enough to determine if you have hypertension, finding out if you do is important to your health. We encourage you to seek your healthcare provider's advice.", "txtbloodpressure_intro_007": "An optimal blood pressure contributes to a longer and healthier life. It's important to find out this measure because if you find its high, it can be managed and your risk for further health problems reduced. Risks include a heart attack or stroke, development of vision loss, kidney disease, or cognitive decline.", "txttips_bloodpressure_001": "Tips to manage blood pressure", "txttips_bloodpressure_content_001": "Use this guide as your personal healthy blood pressure (hypertension) action plan. ", "txttips_bloodpressure_content_002": "Use this guide to assist you in maintaining a healthy blood pressure.", "txttakeyourmedications_001": "Take your medications", "txttakeyourmedications_content_001": "It's the most important action you can take to meet your blood pressure goals. If you find it challenging, speak with your healthcare team right away. They can help you better understand why your medication is important and explore other medication options.", "txttakeyourmedications_content_002": "Taking the medication prescribed for you is the most important action you can take to meet your blood pressure goals. Continue to maintain your routine!", "txttakeyourmedications_content_003": "Many people need medication to manage blood pressure while some don't because it often can be managed through healthy lifestyle behaviors like a heart-healthy diet and exercise. Speak with your healthcare team to find out if medication is on your personal healthy blood pressure action plan.", "txtmindfulofacl_001": "Be mindful of alcohol", "txtmindfulofacl_content_001": "Continue to avoid drinking alcohol. It's high in calories and affects blood sugar levels. Staying away from it can help keep blood sugar levels stable and lower the risk for developing type 2 diabetes.", "txtmindfulofacl_content_002": "Continue to limit the amount of alcohol you drink. Alcohol is high in calories, affects blood sugar, and can cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_003": "When you limit the amount of alcohol you drink, your blood glucose level will likely improve. Alcohol affects blood sugar levels, is high in calories which can lead to weight gain, and it can cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_004": "Alcohol is high in calories and affects blood sugar levels. Consider decreeasing the amount you drink to help to keep blood sugar levels stable and lower the risk for developing type 2 diabetes. It can also cause harmful effects to the body like liver and heart disease when consumed in excess.", "txtmindfulofacl_content_005": "Continue to avoid drinking alcohol. Staying away from it can help keep blood pressure in the optimal range and reduce the risk for complications from hypertension.", "txtmindfulofacl_content_006": "Continue to limit the amount of alcohol you drink. Alcohol is high in calories,  raises blood pressure and can cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_007": "Limit the amount of alcohol you drink. Reguar drinking may raise blood pressure, lead to weight gain, and cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_008": "Continue to avoid drinking alcohol. It's high in calories and may raise blood pressure. Staying away from it can help keep blood pressure in the optimal range and reduces the risk for heart disease.", "txtmindfulofacl_content_009": "Continue to limit the amount of alcohol you drink. Alcohol is high in calories,  raises blood pressure and can cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_010": "When you limit the amount of alcohol you drink, your blood pressure will likely improve. Alcohol affects blood pressure, is high in calories which can lead to weight gain, and it can cause other harmful effects to the body if consumed in excess.", "txtmindfulofacl_content_011": "Alcohol is high in calories and affects blood pressure. Take steps to reduce the amount you drink to help keep blood pressure in the optimal range and reduce the risk for heart disease an other harmful effects on the body.", "txtquitsmoking_001": "Quit smoking", "txtquitsmoking_content_001": "Being smoke free is one of the best things you can do for your health and longevity. Continue a smoke-free lifestyle.", "txtquitsmoking_content_002": "Blood pressure improves immediately after quitting smoking. Quitting may be one of the toughest challenge of your life but keep in mind others have done it - and so can you.", "txtquitsmoking_content_003": "Being smoke free is one of the best things you can do for your health and longevity. Continue a smoke-free lifestyle.", "txtquitsmoking_content_004": "You're likely aware of the many reasons to quit smoking. In fact, blood pressure improves immediately after quitting. Quitting smoking may be the toughest challenge of your life but keep in mind others have done it - and so can you.", "txtquitsmoking_content_005": "Continue to live a smoke-free lifestyle for cholesterol management.", "txtquitsmoking_content_006": "People who don't smoke tend to have better cholesterol levels. Quitting smoking may be one of the toughest challenge of your life but keep in mind others have done it - and so can you.", "txttakemedicationswhenprescribed_001": "Take medications when prescribed", "txttakemedicationswhenprescribed_content_001": "Medications for controlling cholesterol are proven to lower the risk for heart disease and stroke. If you're on them, continue to take as directed.", "txttakemedicationswhenprescribed_content_002": "Cholesterol medication is proven to lower the risk for heart disease and stroke and is often prescribed to people with poor cholesterol. Closely follow your health care provider's advice.", "txteathearthealthy_001": "Eat heart healthy", "txteathearthealthy_content_001": "Enjoy eating a variety of foods. The Therapeutic Lifestyle Change (TLC) and Mediterranean-style eating plans are recommended for cholesterol management.", "txteathearthealthy_content_002": "Include monounsaturated fats and oils, and high-fibre foods, and limit saturated and trans fats, added sugars and starches, and alcohol. The Therapeutic Lifestyle Change (TLC) and Mediterranean-style eating plans are good choices for cholesterol management.", "txtspendyourvitalitycoins_001": "Spend your Vitality coins", "txtspendyourmedals_001": "Spend your medals", "txtspendyourcoins_001": "Spend your coins", "ctrl_btnfinish_001": "Finish", "ctrl_btndone_001": "Done", "txtcheckinruleguide_001": "Check-in available for %1$@ only", "txtyesterday_001": "yesterday", "txttoday_001": "today", "txtcheckin_001": "Check in", "txtcheckingin_001": "Checking in", "txtweekday_001": "Monday", "txtweekday_002": "Tuesday", "txtweekday_003": "Wednesday", "txtweekday_004": "Thursday", "txtweekday_005": "Friday", "txtweekday_006": "Saturday", "txtweekday_007": "Sunday", "txtwheretogo": "Where to go?", "txtactivitycontentnote": "Get 10% off your screenings and vaccinations when you go to any of these participating partners.", "txtcurrentperiod_001": "Current membership year", "txttotalpointsearned_001": "Total points earned", "txtdaysleft_001": "Days left", "txtstatusdetails_001": "Status details", "txtpointshistory_001": "Points history", "ctrl_btnseeall_001": "See all", "txttbronze_001": "Bronze", "txtsilver_001": "Silver", "txtgold_001": "Gold", "txttplatinum_001": "Platinum", "txttblue_001": "Blue", "txtsettings_001": "Settings", "section_manage_001": "Manage", "txtappsanddevices_001": "Apps and devices", "txtloginpreferences_001": "Login preferences", "txtemailcommunication_001": "Email communication", "txtnotifications_001": "Notifications", "txtdatasharing_001": "Data sharing", "section_general_001": "General", "txtaccountdetails_001": "Account details", "txthelpcentre_001": "Help centre", "txtprivacypolicy_001": "Privacy Policy", "txttermsandconditions_001": "Terms and Conditions", "txtlegal1_001": "Legal 1", "txtlegal2_001": "Legal 2", "ctrl_logout_001": "Log out", "dialog_body_areyouwanttologout_001": "Are you sure you want to log out?", "dialog_btn1_yeslogout_001": "Yes, log out", "txtname_001": "Name", "txtmobile_001": "Mobile", "txtgender_001": "Gender", "txtdateofbirth_001": "Date of birth", "txtmembershipnumber_001": "Membership number", "txtcustomernumber_001": "Customer number", "txtmembershipstartdate_001": "Membership start date", "txtmembershiptype_001": "Membership type", "txtmembershipcancellation_001": "Membership cancellation", "txtthisinformationfrominsurer_001": "If your personal details are incorrect, please contact us on %1$@ to have it updated. ", "txtpointsandstatus_001": "Email communication", "ctrl_receiveemailsabout_001": "Receive emails about offers and updates", "label_commsemaiaddress_001": "Communication email address", "ctrl_btnsavechanges_001": "Save changes", "alert_updatedemailaddress_001": "We've updated your email address", "dialog_hd_disablecommunications_001": "Disable communications?", "dialog_body_disable_email_communication_001": "Are you sure you want to disable email communication to receive offers and updates.", "dialog_btn2_cancel_001": "Cancel", "dialog_btn1_continue_001": "Continue", "supportText_changeyourcurrentemail_001": "To change your current email, contact %1$@ ", "dialog_hd_changeemail_001": "Change email", "ctrl_txt_sharescreeninteractions_001": "Share screen interactions anonymously so we can improve the app", "ctrl_txt_shareappcrashes_001": "Share app crashes and errors so that we can fix them", "txt_pushnotification_contentnote_001": "Push notifications provide important app information and reminders.\n\nIf you'd like to change your notification preferences, it must be done in the settings on your device.", "txt_opensettings_001": "Open Settings", "txtyoucancompletethisprofilenow_001": "You can complete this profile now", "txttherewillbeafewquestionsaboutyou_001": "Achieve your goal by answering a few quick questions.", "ctrl_btncompletelater_001": "Complete later", "ctrl_btncompletenow_001": "Complete now", "txtwelldoneyouscored_001": "Great job! You scored %1$@%", "txtwelldoneyouscoredcopy_001": "You’ve achieved your weekly lifestyle goal.", "ctrl_btnviewresults_001": "View results", "txtbetterlucknexttime_001": "Better luck next time \n You scored %1$@%", "txtbetterlucknexttimecopy_001": "You’ve missed your goal this week. View your results to see the correct answers.", "Visit_BiW_mall_001": "Visit the BiW mall", "ctrl_btngotocygmall_001": "Visit the <<Rewards mall>>", "txt15creditshavebeendepositedinyourcygmall_001": "Well done! %1$@ credits have been added to your <<Rewards mall>>.", "txtcreditcopy_001": "Your earned credits will expire after %1$@ weeks. \n\n You'll be taken to our website to view your total credits and to shop online.", "ctrl_btnyourgoalhistory_001": "Goal history", "txtmanualcheck_in_001": "Manual check-in", "txtyourprofilesummary_001": "Your profile summary", "txtyouranswer_001": "Your answer:", "txtguidance_001": "Guidance:", "txtnewgoaleverymonday_001": "Get a new goal every %1$@", "txtscorehigherthan80_001": "Score %1$@% or higher in the quiz", "txtwhenwouldyouliketocompletethisgoal_001": "When would you like to complete this goal?", "txtifyouchooselateryouwillfinditingoalsonhome_001": "If you choose later you will find it in Goals on Home.", "txtspendyourcredit_001": "Spend your credits", "txt_reward_2048": "Earn a spin", "txt_reward_2050": "Earn 200 coins", "txtnsubmitproof_002": "Submit proof of your participation", "txtpoints_006": "Points limit reached", "txtpoints_005": "Points Category Limit Reached", "txtuploading_001": "Uploading", "txtoptional_001": "Optional", "txtselectPDF_001": "Select PDF", "txtproofnote_003": "Submit confirmation that you’ve completed the event by sharing:\n •  a website link to your official results or\n •  any other form of proof", "txtproofnote_001": "Submit confirmation that you’ve completed the event by sharing proof", "txtproofnote_002": "Submit confirmation that you’ve completed the event by sharing a website link to your official results", "how_to_earn_gift_cards_001": "How to earn gift cards", "how_to_earn_gift_cards_details_001": "<Achieve your weekly goals to earn %1$@ gift card>.", "txthhmmss_001": "hh:mm:ss", "txtselect_001": "Select", "txt_fitness_disclaimer_001": "fitness-events-disclaimer", "txtfirstdaynextdaycheckingoal_001": "Come back tomorrow to check-in for today’s progress", "txtcheckintomorrow_001": "Check-in tomorrow", "txtcheckintomorrowdialog_001": "Today’s goal progress can only be checked-in tomorrow.", "txtyourquizsummary_001": "Your quiz summary", "login_dialog_txtbdincorrectpwd_002": "The email address or password you've entered is incorrect.\n\nFirst time logging in? Activate your account to continue.", "txtnonsmokerdeclaration_001": "Non-smoker declaration", "txtnonsmokerstatus_001": "Non-smoker declaration", "txtnonsmokerstatus_002": "Smoking status", "txttime_001": "Takes 1 minute to complete", "txtguide_participatingpartners_001": "Ready to quit smoking? We’re here to support you with access to leading programmes.", "txtpointsareavailableeverymembershipyear_002": "Earn points every membership year", "ctrl_btndeclarenonsmokingstatus_001": "Declare your non-smoking status", "txtguide_participatingpartners_002": "<Partners to help you quit smoking>", "txtparticpatingpartners_001": "Smoking cessation partners", "txtnonsmokerstatusdeclarationcomplete_001": "Non-smoker declaration complete", "txtstatus_001": "Status", "txtsmokingdeclarationcomplete_001": "Smoking status complete", "nsd_declaration_txtsmoker_001": "I confirm that I have smoked tobacco or e-cigarette in the past six months, regularly or occasionally.", "nsd_declaration_txtnonsmoker_001": "I declare I have not smoked any tobacco or e-cigarettes in the past 6 months regularly or occasionally.\n\nI agree that I will may be asked to complete a cotinine or nicotine test to confirm this if requested my non-smoking status.", "nsd_modalcompleted_txtstatusconfirmed_002": "You can only earn points when you declare that you’re a non-smoker.\n\nYou may update your smoking status again in six months.", "nsd_confirm_smoker_001": "Smoker's declaration", "nsd_confirm_smoker_002": "You declare that you are a smoker and have smoked cigarettes / e-cigarettes occasionally or regularly over the the past 3 months\n\nThis activity will become available again in 3 months time should you have quit smoking and wish to update it.\n\nIf you wish to change this status before the 3 month period, you agree that you may need to undergo a test to prove your non-smoker status should Sumitomo Vitality request one.\n\nThe test includes you providing a urine sample to test the nicotine levels within your body.", "nsd_confirm_non_smoker_001": "Non-smoker's declaration", "nsd_confirm_non_smoker_002": "You declare that you have not smoked cigarettes / e-cigarettes occasionally or regularly over the the past 3 months.\n\nYou agree that you may need to undergo a test to prove your non-smoker status should Sumitomo Vitality request one.\n\nThe test includes you providing a urine sample to test the nicotine levels within your body.", "txtkmtokm_001": "%1$@km to %1$@km", "txtkmandmore_001": "%1$@km and more", "txtxpoints_001": "%1$@ pts", "txtpointsfordistance_001": "Points for distance", "txtautomaticallytracked_001": "Automatically tracked", "txt_how_to_earn_Points_body_001": "Your connected app or device will automatically sync data to earn points towards your goals and Vitality status", "txtheartratenote_001": "Earn points based on your workout intensity. The longer you work out, the more points you’ll earn. Calculate your maximum heart rate by subtracting your age from 220.", "txt_heart_rate_condition_001": "%1$@ min at %2$@%", "txt_potential_points_001": "%1$@ pts", "txtgymworkout_001": "Gym workout", "txtgymnote_001": "Use our gym check-in to confirm your gym visit.", "txt_steps_range_001": "%1$@ - %2$@ steps", "txt_steps_range_002": "%1$@+ steps", "txt_steps_range_003": "%1$@ steps", "txt_register_sport_classes_001": "Register here", "txt_sport_classes_details_001": "Easily book sports classes nearby and earn extra physical activity points towards your weekly and monthly goals as well as earn status points.", "txt_min_at_kCal_001": "%1$@ min at %2$@ kCal", "txt_min_and_more_at_kCal_001": "+%1$@ min at %2$@ kCal", "txtmindistanceofxkmatanaveragespeedof_001": "Min distance of %1$@km at an average speed of %1$@", "txvalidtimeformat_0001": "Please enter valid time format", "txtgetverifiedprooffromhealthcareprovider_001": "Submit test results and completed form from healthcare provider", "txtguide_participatingpartners_003": "Get <<one free Vitality health check per year>> at any of these participating partners.", "txtrelatedfaqs_001": "Related FAQs", "txtpointsforsubmitting_002": "Earn %1$@ points for submitting test result", "txtpointsforresultsinhealthyrange_001": "Plus, %1$@ points for results in a healthy range", "txtsubmittedearnpoints_001": "You can earn another %1$@ points if you submit verified results within healthy range", "txthealthyrange": "Healthy range", "txtyoucansubmiteverysixmonths_001": "Points available once every membership year", "txtsubmitresultsandform_001": "Submit results and form", "txtsubmitresultsandproofexplanationcopy_001": "Confirm that you’ve completed your health checks by uploading your results and submission form as proof.", "txthealthcheckdate_001": "Health check date", "txtyouractivitydatemusthaveoccurredbetweendate_001": "Your activity must have been completed between %1$@ and %1$@.", "txtsubmitresultsandproofexplanationcopy_002": "Confirm that you’ve completed your health checks by uploading your results and proof, here. You don’t need to upload all results at once.", "guide_": "Points for this date will count towards your previous membership year.", "txtproofnote_004": "Points are only awarded for waist circumference in a healthy range when your BMI is out of healthy range", "txtproofsubmittedsuccessfully_001": "Proof submitted successfully", "txtpointsmaynotreflectimmediately_001": "Your points may not reflect immediately.", "txtpointsawardedtopreviousmembershipyear_001": "Your points will be added to your previous membership year and may not reflect immediately.", "ctrl_btnsubmitascreeningorvaccination_001": "Submit screenings and vaccinations", "txtyoucansubmiteverysixmonths_002": "Points available again: %1$@", "txtlastmembershipyear_001": "Last membership year", "ctrl_txtlearnmoreaboutbodymassindex_001": "Learn more about body mass index", "txtrange1250_001": "Result must be between %1$@ and %2$@", "txtresubmitresultsandform_001": "Re-submit results and form", "body_smokesResultSummary": "Summary: Lorem ipsum dolor sit amet, consectetur adipiscing elit. Duis tristique ut mauris quis pretium.", "crd_statusMarker_outOfRange_001": "Take note", "crd_title_001": "Total cholesterol", "crd_subtext_001": "Your result: %1$@ mmol/L", "crd_subtext_002": "Target result: < %1$@ mmol/L", "txtreferences_001": "References:", "crd_title_002": "LDL cholesterol", "crd_subtext_003": "Target range: < %1$@ mmol/L", "crd_statusMarker_outOfRange_002": "Doing well", "ctrl_txthistory_001": "History", "ctrl_txtquickguide_001": "Quick guide", "ctrl_txtlearnmoreaboutcholesterol_001": "Learn more about cholesterol", "txtsectionHd_001": "<Health partners/ Apps>", "ctrl_txt_001": "App/ Partner", "txtfooterhealth": "While our calculations are based on state-of-the-art science, it can’t replace the advice of a healthcare professional. We encourage you to speak with a provider about your health results.", "ctrl_btnshareyoursmokingstatus_001": "Share your smoking status", "txtcurrentlysmokes_001": "Currently smokes", "txtnonsmoker_001": " Non-smoker", "txtproofnote_005": "We’ll only accept the following proof to confirm you’ve completed your tests:\n • verified results and\n • a submission form from your healthcare provider", "txtpoints_007": "0/1,000 POINTS", "txtpoints_008": "0/10,000 POINTS", "sport_class_url": "www.google.com", "txt_fingerprint_facial": "Face and Fingerprint", "txt_goals_001": "Earn points", "txthealthcheck_002": "See all activities", "txtactivities_001": "Activities", "txtcheckinruleguide_002": "You have %1$@ days left to increase your status.", "txtmaximumstatuspointsearnedfromactivities_001": "Maximum status points earned from activities", "txtkeepearningpointstomeetyourgoals_001": "Keep earning points to meet your goals", "ctrl_txtpointshistory_001": "Points history", "txtfootnoteVitalityprogramme_001": "While the Vitality programme is based on state-of-the-art science, it can’t replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "txt_sv_disclaimer_001": "Screenings Vaccs Legal Disclaimer", "txtgiftcardsuccessfullyarchived_001": "Gift card successfully archived", "txtgiftcardsuccessfullyarchived_002": "Gift cards successfully archived", "txtnohistoryavailable_001": "No history available", "txtthepointsyouearnforcompletingactivitieswillposthere_001": "Earn points for completing activities. Your activity history will appear here.", "ctrl_btngotoyouractivities_001": "View activities", "txtxpointsmonth_002": "%1$@ points earned this month", "txtnohistoryavailableyet_001": "No history available yet", "txt30million_001": "35 million", "txtvitalitymembersworldwide_001": "Vitality members worldwide", "txtvitalitymemberslive_001": "Vitality members live", "txtlongerhealthierlives_001": "longer, healthier lives", "txttheworldslargestsciencebased_001": "The world’s largest science-based behavioural change program that rewards you for making healthier choices.", "txtonboardingearnpoints_001": "Earn <span class='primary h2 medium'>points</span> for completing healthy activities", "txtonboardingvitalitystatus_001": "The more points you earn, the higher your <span class='primary h2 medium'>status</span>", "txtonboardingdiscountonpremium_001": "The higher your status, the greater the <span class='primary h2 medium'>discount on your premium</span>", "txtonboardinggoals_001": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>", "txtonboardingrewardsheader_002": "Achieve your weekly goals to earn <span class='primary h2 medium'>coins</span> for <span class='primary h2 medium'>gift cards</span>", "txtonboardingearnpoints_002": "Categories include assessments, screenings and physical activity.", "txtonboardingvitalitystatus_002": "Your Vitality Status is a measure of your health. You start at Bronze and work your way to Platinum.", "txtonboardingdiscountonpremium_002": "Earn up to <x%> <annual discount on your insurance premium,> <and discounts on Expedia> based on your status.", "txtonboardinggoals_002": "Points earned from physical activity will contribute toward your daily, weekly and monthly goals.\n\nAnd every week, choose a lifestyle goal to form healthy habits.", "txtonboardingrewardscopy_002": "By achieving your weekly goals you will earn coins from spinning the wheel.\n\nOnce you have 400 coins you will get a $5 gift card to <Nike or Amazon>.", "ctrl_btnideclare_001": "I declare", "title_featured_articles_001": "Browse featured articles", "txtxpoints_003": "earned this month", "txtxpoints_002": "%1$@ points", "txtyouremissingoutonpointscopy_001": "Start earning points as you complete healthy activities. The more points you earn, the higher your Vitality status – the greater your rewards", "txtyouremissingoutonpoints_001": "Your points are waiting for you", "txtfuturehealthintro_001": "Improve all your health results to reach the healthiest version of you.", "txtyouareclosetomaximising_001": "close to reaching your maximum healthspan", "txtbasedonyourvitalityprofile_002": "based on your current lifestyle choices.", "txtfuturehealthsectionheader_001": "Well done!", "txtfuturehealthsectiontext_001": "Keep up your healthy habits to enjoy the best quality of life as you age.", "txtfuturehealthsectionheader_002": "Where to focus your efforts?", "txtfuturehealthsectiontext_002": "Take it to the next level by focusing on your Health Priority.", "txtbasedonyourvitalityprofile_001": "Your Vitality profile shows that you’re", "txtyoucanadduptohealthyyears_001": "you can add up to %1$@ healthy years", "txtbasedonyourvitalityprofile_003": "Your Vitality profile shows that", "txtfuturehealthsectionheader_003": "How does this impact your Vitality Age?", "txtfuturehealthsectiontext_003": "If you improved all your health results to the optimal target, you can shift your Vitality Age %1$@ years younger.", "txtfuturehealthsectiontext_004": "Focus on your Health Priority to have the biggest impact on your healthspan.", "txtyoucanadd10ormorehealthyyears_001": "you can add more than 10 healthy years", "txtyoucanaddmorehealthyyears_001": "you can add more than %1$@ healthy years", "txtfuturehealthsectiontext_005": "If you improved all your health results, you could shift your Vitality Age to be younger than your actual age.", "txtfuturehealthsectiontext_006": "We encourage you to speak with a healthcare provider for guidance on your overall health. In the meantime, Vitality is here to help you focus on your Health Priority to have the biggest impact on your healthspan.", "txtbasedonyourvitalityprofile_004": "to your life by making positive lifestyle choices.", "ctrl_submitresultsandproof_001": "Submit results and proof", "ctrl_updateassessmentresults_001": "Update assessment results", "ctrl_txtmoreaboutsmoking_001": "More about smoking", "ctrl_txtmoreaboutsleep_001": "More about sleep", "ctrl_txtmoreaboutbloodglucose_001": "More about blood glucose", "ctrl_txtmoreaboutbodycomposition_001": "More about body composition", "ctrl_txtmoreaboutcholesterol_001": "More about cholesterol", "ctrl_txtmoreaboutalcoholconsumption_001": "More about alcohol consumption", "ctrl_txtmoreaboutphysicalactivity_001": "More about physical activity", "ctrl_txtmoreaboutcardiofitness_001": "More about cardio fitness", "ctrl_txtmoreaboutmentalwellbeing_001": "More about mental wellbeing", "ctrl_txtmoreaboutbloodpressure_001": "More about blood pressure", "ctrl_txtmoreaboutnutrition_001": "More about nutrition", "txthealthpartnersapps_001": "Health partners/ Apps", "txtdomoreofthis_001": "Do more of this", "txtkeeparecord_001": "Keep a record", "txtmakeacommitment_001": "Make a commitment", "ctrl_txtlearnmoreaboutthescience_001": "Learn more about the science", "txtunabletologin_001": "Unable to login", "txtunabletologincopy_001": "Your account has been locked due to too many incorrect login attempts", "partner_description_1044": "Donate € 5 to the Johan Cruyff Foundation\n\nHow to earn this reward\n\nGet active for someone else and donate your reward to a good cause. Currently, your reward of € 5 goes to the Cruyff Foundation.\n\nHow to use this reward\n\nChoose the option 'Donation' in the app under rewards, and donate your weekly reward.\n\nMore about charity\n\nDo you donate your weekly reward to the Cruyff Foundation? Then you also help others with their health! With your donation you contribute to the development of Cruyff Courts, with which we literally give children the space to play and move. And that is very necessary because sport and games are important for a good and healthy development of children. Unfortunately, not a lot of children have this opportunity yet.", "partner_description_224": "Achieve the MACHI café coffee or smoothie when you meet the weekly goals of the Active Challenge.", "partner_description_116": "If you reach the weekly goal of Active Challenge, you can get a drink ticket worth 500 yen.", "partner_description_1008": "A bookworm and homemaker’s paradise, Indigo offers bestselling books, toys, fashion, home décor, stationery, electronics & so much more.", "partner_description_128": "This reward is not applicable for Takaful membership holders\n\nGet 50% discount on your next movie!\n\neasytickets lets you buy movie tickets in their partner cinema in the simplest, easiest and the smartest way without the unnecessary glitz. Search, choose and buy movie tickets. It can’t get easier than this.\n\neasytickets reaches around 100 screens in the 4 major cities. easytickets also offers information about upcoming movies, show timings, movie trailers & reviews. On achieving your weekly target, you can choose easytickets as your reward coupon and get to watch a movie for a 50% discount. In fact, If you work hard on your weekly targets for two consecutive weeks, then your loved one / friend / partner will also be able to watch the movie for 50% discount. Book your tickets in advance and watch the next block buster movie for a 50% discount. Enjoy!", "partner_description_129": "Get discount on your next meal\n\nAre you hungry? Had a long and busy day? Then foodpanda is the right place for you! Foodpanda offers you a long and detailed list of the best restaurants near you. You can order any kind of healthy food you are craving, foodpanda has an extensive roaster of 750 restaurants, further, if you are in the mood for Indian, Pakistani, or Afghan cuisines, there are plenty of restaurants available for you to order food online with home delivery.\n\nIGI Life Vitality strongly recommends that your selection of restaurant should contain a balance of food groups and all the nutrients necessary to promote good health. Healthy eating is the practice of making choices about what and/or how much one eats with the intention of improving or maintaining good health. Healthy eating is not about strict dietary limitations, staying unrealistically thin, or depriving yourself of the foods you love. Rather, it’s about feeling great, having more energy, improving your health, stabilizing your mood and being more active. We leave it your good judgement about choosing healthy food and hope you enjoy your healthy meals.", "partner_description_1040": "Get PKR 500 top-up\n\nTop up your mobile number (pre-paid or post-paid) on any network in Pakistan, instantly through Easytickets mobile top-up, anytime, anywhere by simply entering the phone number and Voucher code. This is applicable for voice and data packages as defined by your GSM network operator.\n\nNote : Voucher validity is four weeks from the date of issue.", "txtcalculatingyourresults_001": "We’re calculating your results...", "txtcalculatingresultssubtext_001": "Your personalised feedback is based on global leading science and clinical expertise, and will be available shortly.", "txtsourcetext_001": "Source: %1$@. Submitted: %2$@", "txtsourcedateonlytext_001": "Submitted: %1$@", "txttarget_001": "Target:", "partner_description_254": "Enjoy the latest the big screen has to offer for less at Cineplex, Canada’s most popular destination for movies.", "partner_description_252": "A bookworm and homemaker’s paradise, Indigo offers bestselling books, toys, fashion, home décor, stationery, electronics & so much more.", "partner_description_255": "With a variety of products on offer including the popular Always Fresh Coffee, a range of hot beverages, cold beverages and many quick meal options, Tim Hortons will put a smile on your face any time of day.", "partner_description_2025": "Hudson’s Bay is Canada’s iconic department Store. Shop for handbags, women’s and men’s clothing and shoes, housewares and much more.", "partner_description_1167": "Description for Celeiro - Placeholder. Description to be provided by market", "partner_description_1168": "Description for Pingo Doce - Placeholder to be translated by market", "partner_description_1161": "Programa de Cessação Tabágica\n\nJá tentou deixar de fumar, mas não conseguiu? O Programa de Cessação Tabágica da Medicina Online, sem qualquer custo adicional, é realizado de forma remota e é personalizado com base no seu grau de dependência de nicotina, as suas preocupações/receios e tendo em conta um dia D (o dia em que deixará de fumar).\n\nDurante 12 meses e ao longo de 5-7 sessões, contará com uma equipa multidisciplinar. O programa inicia-se com uma consulta com um Psicólogo que definirá o seu plano para deixar de fumar e irá acompanhá-lo ao longo de todo o programa, coordenando as consultas com os restantes profissionais de saúde (Médico de Medicina Geral e Familiar e Nutricionista), caso seja necessário.\n\nAs consultas telefónicas podem ser agendadas dias úteis, das 9h às 19h. Para se inscrever, basta enviar um <NAME_EMAIL>, indicando o seu nome, idade, email, telemóvel e horário preferencial de contacto, e um profissional da Medicina Online entrará em contacto consigo.", "partner_description_1162": "Orientação Nutricional\n\nQuer melhorar os seus hábitos alimentares? O serviço de Orientação Nutricional da Medicina Online é realizado por Nutricionistas que o poderão ajudar em temas relacionados com hábitos alimentares saudáveis, perda de peso, intolerâncias ou alergias alimentares e ainda alimentação adequada a determinadas patologias. Sem qualquer custo adicional e através dos nossos profissionais, poderá definir um plano alimentar personalizado e adequado ao seus objetivos e/ou necessidades e realizar sessões de seguimento.Poderá ainda partilhar com o Nutricionista resultados de exames ou análises realizadas.\n\nAs consultas telefónicas podem ser agendadas dias úteis, das 9h às 19h. Para se inscrever, basta enviar um <NAME_EMAIL>, indicando o seu nome, idade, email, telemóvel e horário preferencial de contacto, e um profissional da Medicina Online entrará em contacto consigo.", "partner_description_1169": "Description for Decathlon - Placeholder. Market to provide description", "partner_description_1170": "Description for Cinemas NOS - Placeholder. Market to provide description", "partner_description_2081": "Programa Põe-te em Forma\n\nQuer perder peso ou procura apoio na prática de exercício?\n\nO programa Põe-te em Forma da cobertura de Medicina Online disponibiliza-lhe um acompanhamento personalizado durante 8 semanas com o intuito de criar hábitos saudáveis ao nível da sua alimentação e prática de exercício.\nUma equipa de nutricionistas e personal trainers irá apoiá-lo remotamente na definição de um plano alimentar e um plano de atividade física para que atinja os seus objetivos, através de consultas de avaliação, receitas saudáveis e aulas online com personal trainers.\n\nPor ter um seguro de Saúde Multicare ou tendo aderido ao programa Vitality associado ao seu seguro Vida Fidelidade (desde que elegível), tem acesso a este serviço que o ajuda a melhorar o seu bem-estar, sem qualquer custo adicional. Disponível dias úteis, das 9h às 19h.\n\nPara se inscrever, basta enviar um <NAME_EMAIL> indicando o seu nome, NIF, idade, email, telemóvel e horário preferencial de contacto, e um profissional da Medicina Online entrará em contacto consigo.", "partner_description_2082": "Consulta de Psicologia\n\nSente que precisa de apoio a nível emocional?\n\nA cobertura de Medicina Online do seguro de saúde Multicare disponibiliza Consultas de Psicologia online que o podem ajudar a lidar com situações de incerteza ou fragilidade, que causam estados emocionais de ansiedade, stress, tristeza ou desmotivação.\n\nPor ter um seguro de Saúde Multicare ou tendo aderido ao programa Vitality associado ao seu seguro Vida Fidelidade (desde que elegível) tem acesso a este serviço que o ajuda a melhorar o seu bem-estar, sem qualquer custo adicional. Disponível dias úteis, das 9h às 19h.\nSempre que possível será acompanhado pelo mesmo psicólogo e em caso de indicação clínica será feito o encaminhamento para um acompanhamento presencial.\n\nPara solicitar uma consulta, envie um <NAME_EMAIL> indicando o seu nome, NIF, idade, email, telemóvel e horário preferencial de contacto, e um profissional da Medicina Online entrará em contacto consigo.", "partner_description_1124": "Programa Dormir Melhor\n\nQuer alcançar um sono mais descansado ou apoio para lidar com insónias?\n\nO programa Dormir Melhor da cobertura de Medicina Online disponibiliza-lhe um acompanhamento personalizado por um psicólogo online, que visa apoiá-lo a identificar novas rotinas e técnicas de relaxamento que melhorem a qualidade do seu sono.\nPor ter um seguro de Saúde Multicare ou tendo aderido ao programa Vitality associado ao seu seguro Vida Fidelidade (desde que elegível), tem acesso a este serviço que o ajuda a melhorar o seu bem-estar, sem qualquer custo adicional. O programa tem a duração de 4 a 6 sessões e está disponível dias úteis, das 9h às 19h.\n\nPara se inscrever, envie um <NAME_EMAIL> indicando o seu nome, NIF, idade, email, telemóvel e horário preferencial de contacto.", "partner_description_1123": "Programa de Gestão de Stress e Ansiedade\n\nQuer apoio para o ajudar a gerir o stress e/ou a ansiedade?\n\nO programa de Gestão de Stress e Ansiedade da cobertura de Medicina Online, disponibiliza-lhe um acompanhamento personalizado por um psicólogo, que irá apoiá-lo na avaliação destas emoções e na identificação de estratégias para as controlar/reduzir.\nPor ter um seguro de Saúde Multicare ou tendo aderido ao programa Vitality associado ao seu seguro Vida Fidelidade (desde que elegível) tem acesso a este serviço que o ajuda a melhorar o seu bem-estar, sem qualquer custo adicional. O programa tem a duração de 6 a 8 sessões e está disponível dias úteis, das 9h às 19h.\n\nPara se inscrever, envie um <NAME_EMAIL> indicando o seu nome, NIF, idade, email, telemóvel e horário preferencial de contacto.", "partner_description_654079": "Kies één van de geselecteerde Etos producten\nZo verdien je deze belonging\nKom in beweging, haal je weekdoel en kies de Etos-beloning in de app.\nZo gebruik je deze beloning\nGa naar een Etos bij jou in de buurt. Kies daar een van de geselecteerde producten. Laat je voucher op je smartphone scannen en neem je product mee. Let op: je kunt alleen kiezen uit geselecteerde producten.\nMeer over Etos\nWe bieden een wisselend aanbod van Etos producten aan waar je zelf uit kunt kiezen.Kies voor één van de vitamines, beautyproducten of bad-en doucheproducten.Verwen jezelf!", "partner_description_1065": "Earn credit for the a.s.r. Vitality webshop\n\nHow to earn this reward\n\nGet moving, reach your weekly goal and click on 'View your savings and choose a reward' in the app. Every time you get your weekly reward, you earn a credit.\n\nHow to use this reward\n\nCreate an account for the a.s.r. Vitality Webshop. You can create this account as soon as you redeem your reward here. In the a.s.r. Vitality Webshop shows you how many credits you need for the weekly reward you want.\n\nMore about the a.s.r. Vitality webshop\nIn the a.s.r. Vitality webshop you will find constantly changing weekly rewards such as gift cards from Zalando, bol.com or Wehkamp.", "partner_description_654081": "Verdien een € 5 cadeaukaart voor je volgende aankoop.\nZo verdien je deze beloning\nKom in beweging, haal je weekdoel en kies de INTERSPORT -beloning in de app.\nZo gebruik je deze beloning\nWissel deze cadeaukaart(en) in bij een INTERSPORT winkel bij jou in de buurt of shop online. Je mag meerdere cadeaukaarten gebruiken per aankoop en er is geen minimaal bestelbedrag.\nMeer over INTERSPORT\nINTERSPORT biedt uitstekende service en een ruim assortiment van topmerken. Je vindt er alles voor wandelen, voetbal, fitness, tennis, running, zwemmen, indoor, hockey en meer.", "whatsNew_item1On": "True", "whatsNew_item2On": "True", "whatsNew_item3On": "True", "whatsNew_item4On": "True", "txtwevemadesomeupdates_001": "We’ve made some <span class='primary500 h2 medium'>updates</span>", "txtwevemadesomeupdatescopy_001": "An enhanced and refreshed overall app experience.", "txtwevemadesomeupdatescopy_002": "Health assessment has been upgraded with personalised results and health priorities.", "txtwevemadesomeupdatescopy_003": "Quick access to rewards on the bottom navigation bar.", "txtwevemadesomeupdatescopy_004": "A new home for points and status in profile.", "txtinch_001": "Inch", "txtfeet_001": "Feet", "txt_processing_begin_shortly": "Processing will begin shortly", "txt_processing_file": "Processing file", "txt_taking_longer_than_usual": "Apologies. It is taking longer than usual.", "txt_no_internet": "No Internet Connection. Please try again later.", "txt_maximum_file_exceeds": "Exceeds the maximum file size. Please try again", "txtyourpointsmaynot_001": "Your points may not reflect immediately.", "no_coins_account_threshold_001": "1000", "no_account_description_001": "Achieve your weekly goals to earn coins", "txtnopromotionbody_002": "History temporarily not available. We're working on it to get it fixed as soon as we can.", "ActivityCategoryLabeltxt_81": "Physical Activity & Fitness", "ActivityCategoryLabeltxt_82": "Assessments", "ActivityCategoryLabeltxt_83": "Prevention", "ActivityCategoryLabeltxt_84": "Nutrition Healthy Food", "txttime_002": "%1$@ minutes to complete", "ctrl_btnstartassessment_001": "Start assessment", "ctrl_btncontinueassessment_001": "Continue assessment", "ctrl_btnrestartassessment_001": "Restart assessment", "txtnextdate_001": "Available again: %1$@", "ctrl_btneditresponses_001": "Edit responses", "healtassessment_modalsuccess_txthealthassessmentcomplete_001": "Great work on completing your health assessment", "healtassessment_modalsuccess_txthealthassessmentcomplete_002": "Learn how to improve your health and longevity", "ctrl_btncalculateresults_001": "Calculate results", "txtphysicalactivityandfitness_001": "Physical activity and fitness", "txtphysicalactivityquestion_001": "On average, how many days a week do you do exercise?", "txtnutrition_001": "Nutrition", "txtbiometricsandhealthconditions_001": "Biometrics and health conditions", "txtmentalhealth_001": "Mental wellbeing", "txtalcoholandtobacco_001": "Alcohol and tobacco", "txtsleep_001": "Sleep", "txtsleep_guide_001": "Healthy sleep guide", "txtsleep_intro_001": "Continue to achieve between 7 and 9 hours of sleep for your good health.", "txtsleep_intro_002": "On average, people who sleep between 7 and 9 hours daily live healthier, longer lives. Speak with your healthcare provider if you feel your sleep patterns are negatively affecting your life.", "txttips_sleep_001": "Tips to help you sleep well", "txttips_sleep_content_001": "Use this guide for support in maintaining habits that promote good sleep.", "txtconsistent_001": "Be consistent", "txtconsistent_content_001": "Keep a consistent sleep routine by maintaining a nightly ritual, going to bed and waking around the same time every day.", "txtconsistent_content_002": "Keep a consistent bedtime and wake time routine. It's especially important to wake up at the same time every day, within an hour, even on weekends.", "txtsleepenvironment_001": "Set up your sleep environment", "txtsleepenvironment_content_001": "Set up your bedroom to be a place for rest. Keep it pleasant, tidy, dark, quiet and cool.", "txtexcercisereg_001": "Exercise regularly", "txtexcercisereg_content_001": "Exercise promotes good sleep. Continue your exercise routine!", "txtexcercisereg_content_002": "Give it a try and see how an exercise routine can help with your sleep. Gradually add 10 minutes of brisk walking (or other exercise) here and there, with the goal to reach 30 minutes most days.", "txtexcercisereg_content_003": "Keep up your exercise routine - it's a mood booster. People who exercise regularly experience less ups and downs and symptoms of anxiety and depression.", "txtexcercisereg_content_004": "Did you know that regular exercise has mental health benefits as well as physical health benefits? Exercise helps boost mood and people who meet the minimum amount experience less ups and downs and symptoms of anxiety and depression.", "txtexcercisereg_content_005": "For most pregnant women it's recommended to continue your current routine. However, some exercises may not be suitable during pregnancy. Be sure to ask your doctor or midwife what's safe for you.", "txtexcercisereg_content_006": "Gradually building up to 150 minutes of exercise every week may be good for you and your baby but first, ask your doctor or midwife if it's safe for you to  do so. Walking and swimming are often good options for pregnant women.", "txtexcercisereg_content_007": "Keep up your exercise routine - exercise helps to regulate blood sugar and maintain weight. Be sure to include two sessions of muscle strengthening exercises weekly and avoid sitting too much.", "txtexcercisereg_content_008": "One way you can improve your glucose level, feel better and reduce your risk for diabetes is through exercise.\nExercise helps the body better regulate blood glucose and helps to maintain a healthy weight. Perform muscle strengthening exercises at least twice a week. Be more active in general with a goal of 30 minutes a day, most days. And avoid sitting for long periods of time.", "txtexcercisereg_content_009": "Exercise helps the body better regulate blood glucose and reduces the risk for developing type 2 diabetes. Perform muscle strengthening exercises at least twice a week. Be more active in general with a goal of 30 minutes a day, most days. And avoid sitting for long periods of time.", "txtexcercisereg_content_010": "Keep up your exercise routine - exercise helps to manage blood pressure and maintain weight.", "txtexcercisereg_content_011": "Keep up your exercise regimen - exercise helps to regulate blood pressure and maintain weight.", "txtexcercisereg_content_012": "One way you can improve your blood pressure, feel better and reduce your risk for heart disease is through exercise. Be more active in general with a goal of 30 minutes a day, most days.", "txtexcercisereg_content_013": "Exercise helps the body maintain an optimal blood pressure and reduces the risk for heart disease and other medical conditions. Be more active in general with a goal of 30 minutes a day, most days.", "txtexcercisereg_content_014": "Keep up your exercise routine - exercise helps to maintain weight in addition to many other health benefits. For good health, aim for 30 minutes and for weight loss and weight loss maintenance, aim for 45 to 60 minutes, most days.", "txtexcercisereg_content_015": "Exercise alone doesn't usually result in weight loss but it does provide many health benefits like better control of blood pressure, blood sugar and cholesterol. Start with 10 minutes at a time. For weight loss and weight loss maintenance, aim for 45 to 60 minutes most days.", "txtexcercisereg_content_016": "Make a conscious effort to sit less to avoid high-calorie bingeing, burn calories, improve overall health, and help lower weight. Start with 10 minutes of physical activity at a time and build from there.", "txtexcercisereg_content_017": "Exercise has been shown to improve cholesterol levels. Continue with your routine!", "txtexcercisereg_content_018": "Exercise has been shown to improve cholesterol levels and help with weight control. Start with 10 minutes at a time with a goal of 30 minutes of physical activity every day, most days.", "txtroutine_001": "Keep a relaxing routine", "txtroutine_content_001": "Turn off technology at least one hour before bedtime. Light from electronics sends alerting signals to the brain and delays the release of the body's sleep signals. Instead, consider reading a book.", "txtwatchdrink_001": "Watch what you drink", "txtwatchdrink_content_001": "Make mid-afternoon your last coffee break. The caffeine in coffee acts as a stimulant which can keep you from falling asleep.", "txtwatchdrink_content_002": "Avoid drinking alcohol several hours before bed, and make mid-afternoon your last coffee break. Alcohol affects overall sleep quality while caffeine is a stimulant which can keep you from falling asleep.", "ctrl_btnstart_001": "Start", "txtactivitiescardsubtext_007": "Completed on %1$@", "txt_vitality_age_measure_001": "Vitality Age isa measure of how healthy you are relative to your actual age.", "ctrl_txtsaveandcompletelater_001": "Save and complete later", "ctrl_btnsubmitassessment_001": "Submit assessment", "txtdialogsaveandcompletelater_001": "Save and complete later?", "txtdialogbodysaveandcompletelater_001": "Your answers will be saved for you to come back later and complete at any time.", "txtrange_001": "Range %1$@ - %2$@", "txtpersonalisegoalsbody_002": "Your personalised feedback is based on global leading science and clinical expertise.", "txtpersonalisegoals_002": "We’re calculating your health results …", "healthassessment_txtdividersleep_001": "StartAre you getting the right amount of sleep? Studies show that sleep plays a big role in your everyday wellness and long-term health. Answer these questions about your sleep habits right now.", "healthassessment_txtdivideralcoholandtobacco_001": "Understanding your current alcohol or tobacco use is important to guide your overall health recommendations. Answer these questions about your habits right now.", "healthassessment_txtdividermentalhealth_001": "Let's check in with your current headspace. Answer these questions to reflect how you’ve been feeling lately – emotionally and mentally.", "healthassessment_txtdividerbiometrics_001": "StartIn this section, we’ll find out more about your health history and measurements. Answer these questions to the best of your ability.\n\nUnsure how to answer? No problem. You can either provide an estimate or select ‘I don’t know’ from the list of answers provided.", "healthassessment_txtdividernutrition_001": "StartReady to tell us about your diet? Answer these questions to give us more information on your usual eating habits.", "healthassessment_txtdividerphysicalactivity_002": "StartHow do you get active? This section will cover your exercise habits, workout intensity, and more. Answer these questions to reflect your exercise habits right now.", "txtsectionof_001": "SECTION %1$@ OF %2$@", "txtactivitiescardsubtext_002": "Submit test results and required proof from healthcare provider", "healthassessment_txtdividerphysicalactivity_001": "How do you get active? This section will cover your exercise habits, workout intensity, and more. Answer these questions to reflect your exercise habits right now.", "onboarding_modalactivated_txtnowletsgetyouconnected_001": "Now, let’s get you connected", "txtareyousure_001": "Are you sure?", "ctrl_btnyesconnectlater_001": "Yes, connect later", "txtareyousurecopy_001": "Connecting a Health app will allow you to immediately start tracking your activity to earn rewards. Not connecting now may impact device syncing and you may miss rewards.", "txtthesciencebehindyourhealthmeasures_001": "The science behind your health measures", "txtsciencesectiontext_001": "We determine your ‘Vitality Age’, ‘Health Priority’ and ‘Future Health’ based on the relationship between your controllable lifestyle behaviours (such as physical activity and healthy eating), metabolic outcomes (such as cholesterol, blood pressure and blood glucose), and the risk of death and disease.   Using the information you share, we combine our health and behavioural data insights with the world’s largest, country-specific disease database –IHME Global Burden of Disease – to calculate your health risks over time and your expected healthspan (number of years that you’ll live in good health).", "txtsciencesectiontext_002": "Your Vitality Age measures how healthy you are relative to your actual age. The difference between your Vitality Age and your actual age is your Vitality Age gap – the number of extra years you will live in good health compared to the average person (based on age, sex and geographical region).", "txtsciencesectiontext_003": "We’ve identified one health risk, that if improved, will have the biggest impact on your healthspan – the number of years you live in good health.", "txtsciencesectiontext_004": "Your Future Health is your total number of healthy years that you can add to your healthspan if you improved all your health risks.", "txtsciencereference_001": "References: Institute for Health Metrics Evaluation. Used with Permission. All rights reserved.", "txtvitalityhealthpriority_001": "Vitality Health Priority", "txtvitalityfuturehealth_001": "Vitality Future Health", "txtfooter_002": "While the Vitality programme is based on state-of-the-art science, it can’t replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "txtweight_guide_001": "Healthy weight guide", "txtweight_intro_001": "An optimal weight contributes to a longer and healthier life.", "txtweight_intro_002": "An optimal weight contributes to a longer and healthier life. Set a goal weight that is right for you and take action toward meeting it. For many people losing 5% to 7 % of their current weight is beneficial to their health.", "txttips_weight_001": "Tips to manage weight", "txttips_weight_content_001": "Use this guide to help maintain a weight that is right for you and promotes your health and wellness.", "txtrevieweating_001": "Review your eating habits", "txtrevieweating_content_001": "Maintaining good eating habits is often also good for weight management. Commit  to a lifestyle that includes healthy eating.", "txtrevieweating_content_002": "When losing weight, give these tips a try: Eat breakfast, be mindful of what you eat, eat with family and friends, plan ahead. Commit to a lifestyle that includes eating low-calorie, nutrient-dense foods.", "txtimproveyourskills_001": "Improve your skills", "txtimproveyourskills_content_001": "Go online, find an app or follow an expert on social media and learn some basic kitchen and food-prep skills. You could also take a cooking class. Learn how to shop for healthy foods, plan meals, and solve problems when it comes to eating out and social events.", "txtfindsocialsupport_001": "Find social support", "txtfindsocialsupport_content_001": "When you have peers who are supportive of healthy habits like exercise and healthy eating, you're more likely to maintain those habits.", "txtfindsocialsupport_content_002": "Many people see weight loss results when they have a partner or support group to help them along the way. Over half of people who have successfully kept off their weight participated in a program with peers to help lose weight.", "txtpregnancy_guide_001": "Healthy pregnancy", "txtpregnancy_intro_001": "Congratulations on your pregnancy. The most important thing you can do for your overall health, is focus on a healthy pregnancy.", "txttips_pregnancy_001": "Tips for a healthy you and baby", "txttips_pregnancy_content_001": "Review the tips below to support you and your baby's health.", "txtnutrition_content_001": "Focus on a variety of foods to meet you and your baby’s nutritional needs. Eating healthily during pregnancy helps your baby develop and grow. Your doctor may also recommend taking a prenatal vitamin, eating fish and seafood (which are low in mercury), limiting caffeine, and avoiding raw meat, fish and eggs or unpasteurized dairy products.", "txtnutrition_content_002": "Nutrition is especially important when pregnant. Eating healthily during pregnancy helps your baby develop and grow.\n\nEat a wide variety of foods to get all your nutrition. Your doctor may also recommend taking a prentatal vitamin. Speak with your doctor or dietitian if you have any special dietary needs so that you can come up with the best plan for you and your baby.", "txtvisitdrearlyandoften_001": "Visit your doctor early and often", "txtvisitdrearlyandoften_content_001": "Start with visiting a health care provider (doctor or midwife) as soon as you know you’re pregnant or think you may be. \nKeep all your appointments to ensure a healthy baby and pregnancy. Your health care provider will be aware of important tests and vaccinations that pregnant women should receive, and can help create an overall care and delivery plan with you.", "txtthinkbabysafety_001": "Think safety for you and your baby", "txtthinkbabysafety_content_001": "Protect yourself and your baby when pregnant and avoid activities that could cause injury to you or your baby, like contact sports, downhill skiing, sky diving, hot yoga or horseback riding. Always wear a seatbelt when in a motor vehicle.", "txtavoidharmful_001": "Avoid harmful substances", "txtavoidharmful_content_001": "For the health of you and your baby, avoid alcohol, cigarettes and street drugs.\n", "txtglucose_guide_001": "Healthy glucose guide", "txtglucose_intro_001": "Because your glucose is in a healthy range, you're at lower risk for developing type 2 diabetes and heart disease - excellent news.", "txtglucose_intro_002": "A recent blood glucose result of yours was in the prediabetes range. Please speak with your doctor about this measure as prediabetes puts you at risk for many conditions including diabetes and heart disease.", "txtglucose_intro_003": "A recent blood glucose result of yours was in the range of someone who has diabetes. Although, one test may not be enough to determine if you have diabetes, do know diabetes is a serious medical condition that requires medical care. We encourage you to seek your doctor's advice.", "txttips_glucose_001": "Tips to manage blood glucose", "txttips_glucose_content_001": "The healthy habits in this guide can help keep or bring blood glucose levels into range, reduce the risk of developing type 2 diabetes, and promote overall wellness.", "txtmindfulofaddedsugar_001": "Be mindful of added sugar", "txtmindfulofaddedsugar_content_001": "Cut back on sugary drinks as a way to improve your blood sugar. You could start by drinking only half your usual size sugary drink or start to replace your drinks with naturally flavored water or herbal tea. In fact, limiting all foods with added sugars (like desserts and candy) helps the body regulate blood sugar.", "txtmindfulofaddedsugar_content_002": "Having too many sugary drinks may put you at risk for high blood glucose levels. Start by drinking only half your usual size sugary drink or replace your drinks with naturally flavored water or herbal tea.", "txtmindfulofaddedsugar_content_003": "Continue to limit the number of sugary drinks you consume. But be mindful of foods with added sugars like desserts, candy and some breakfast cereals. When you limit eating foods with added sugars, you'll also help to keep blood sugar levels steady.", "txtfibrercihfoods_001": "Eat fibre rich foods", "txtfibrercihfoods_content_001": "Gradually add fibre-rich foods, like like non-starchy vegetables, beans, peas and whole grains, to your everyday meals to help control your blood glucose.", "txtfibrercihfoods_content_002": "Eating fibre helps to keep the body's blood sugar level steady. Consider gradually adding fibre-rich foods, like like non-starchy vegetables, beans, peas and whole grains, to your everyday meals.", "txtfibrercihfoods_content_003": "Eating fibre helps to keep the body's blood sugar level steady. Continue to eat fibre-rich foods like non-starchy vegetables, beans, peas and whole grains.", "btn_filter_by_001": "Filter by", "filter_category_01": "Category", "txtmembershipyear_001": "Membership year", "clear_all_filter_001": "Clear all", "apply_filter_001": "Apply", "txtDate_001": "Date", "txtactivity_001": "Activity", "txtaverageheartrate_001": "Average heart rate", "txtmaximumheartrate_001": "Maximum heart rate", "Duration": "Duration", "txtsource_001": "Source", "txtburnedcalories_001": "Burned calories", "txtactivitydetail_001": "Activity detail", "txttotal_001": "Total:", "txtstepcount_001": "Step count", "heart_rate_bpm_001": "%1$@ bpm", "categories_filtered_001": "Filter (%1$@)", "txtcurrent_001": "Current", "txtprevious_001": "Previous", "ctrl_btnfilter_001": "Filter", "txtkeepemailaddresspopulated_001": "Keep email address populated on login", "txtusefingerprinttologin_001": "Use <Fingerprint> to login", "txtloginemailaddress_001": "Login email address", "txtcreatenewpassword_001": "Create new password", "txtsavechanges_001": "Save changes...", "txtauthenticationrequired_001": "Authentication required", "txtvitalityoverallstatus_001": "%1$@ Vitality status", "dialog_body_changingemaillogin_001": "This updated email address will be used for both logging in as well as email communications.", "chip_BMI": "BMI", "chip_cashback_amount_001": "Get up to $240 cashback", "ctrl_btnorder_device_001": "Order Apple Watch", "txtmonthly_Apple_Watch_cashback_001": "Monthly Apple Watch cashback", "txtmonthly_Generic_GProduct_cashback_001": "Monthly product cashback", "ctrl_btnchoose_device_001": "Choose a device", "txtuptoXcashback_001": "UP TO $240 CASHBACK", "txt_purchase_device_001": "Purchase a device through <Vitality>", "chip_commitment_period_001": "24-month commitment", "get_active_001": "Get\nactive", "txtearncashback_001": "Earn\ncashback", "cashback_amount_001": "$2 cashback", "cashback_amount_002": "$5 cashback", "txtactivatedevicechallenge_001": "Order device to see your cashback history.", "previous_device_rewards_001": "Previous device rewards", "txtscored_001": "%1$@ % scored", "txtcorrectanswer_001": "Correct answer:", "txtchipnotachieved": "Not achieved", "txtenjoyrewardsandexclusivebenefitsjustforyou_001": "Enjoy rewards and exclusive benefits just for you. Earn coins and spend them on a variety of rewards in the mall.", "txtmonthlyrewards_001": "Monthly rewards", "txtstatusrewards_001": "Status rewards", "txtexclusivebenefits_001": "Exclusive benefits", "txtvitalitymemberslikeyou_001": "Vitality members like you, get these exclusive benefits.", "ctrl_btnuploadanotherfile_001": "Upload another file", "ha_section_Iconkey_94": "solid person-walking", "ha_section_Iconkey_95": "solid salad", "ha_section_Iconkey_96": "solid stethoscope", "ha_section_Iconkey_97": "solid head-side-heart", "ha_section_Iconkey_98": "solid wine-glass", "ha_section_Iconkey_99": "solid bed-front", "txt_apple_watch_001": "Get your next <Apple Watch> through <Vitality>", "txtpurchaseapplewatch_001": "Purchase\nApple Watch", "cashback_amount_003": "$10 cashback", "txtturnoffnotifications_001": "Turn %1$@ notifications", "txthaveaquestiongetintouch_001": "Have a question? Get in touch with us. ", "txtwebsite_001": "Website", "txtemail_001": "Email", "txtyoureabouttoleaveourapp_001": "You’re about to leave our app", "txtyouarebeingredirectedtowebsite_001": "You are being redirected to an external site", "txtyouarebeingredirectedtomakeacall_001": "You are being redirected to an external site", "txtyouarebeingredirectedtosendanemail_001": "You are being redirected to send an email.", "dialog_authenticationrequired_001": "An incorrect password has been entered, please try again", "dialog_entercurrentpassword_001": "Enter current password", "dialog_existingpassword_001": "Enter Existing Password", "dialog_weveupdatedyourpassword_001": "We’ve updated your password ", "apple_watch_breakout": "https://www.istore.co.za/", "aw_cashbackpts_1": "%1$@-%2$@ pts", "aw_cashbackpts_2": "%1$@-%2$@ pts", "aw_cashbackpts_3": "%1$@-%2$@ pts", "aw_cashbackpts_4": "%1$@+ pts", "genericfd_cashbackpts_1": "%1$@-%2$@ pts", "genericfd_cashbackpts_2": "%1$@-%2$@ pts", "genericfd_cashbackpts_3": "%1$@-%2$@ pts", "genericfd_cashbackpts_4": "%1$@+ pts", "aw_circlecontent_1": "$2", "aw_circlecontent_2": "$5", "aw_circlecontent_3": "$10", "aw_circlecontent_4": "$15", "genericfd_circlecontent_1": "$2", "genericfd_circlecontent_2": "$5", "genericfd_circlecontent_3": "$10", "genericfd_circlecontent_4": "$15", "aw_cashback_1": "$2 cashback", "aw_cashback_2": "$5 cashback", "aw_cashback_3": "$10 cashback", "aw_cashback_4": "$15 cashback", "genericfd_cashback_1": "$2 cashback", "genericfd_cashback_2": "$5 cashback", "genericfd_cashback_3": "$10 cashback", "genericfd_cashback_4": "$15 cashback", "txtearn_reward_001": "Earn\ncashback", "txtearn_reward_002": "Earn\ncashback", "chip_cashback_fd_001": "Get up to $240 cashback", "chip_commitment_period_002": "18-month commitment", "txtpurchasedevice_001": "Purchase\ndevice", "txtyourfirstgoalstartson_date_001": "Your first goal starts on %1$@", "txtuptoxperc_cashback_001": "GET UP TO %1$@ CASHBACK", "txtcashbackstartssoon_001": "%1$@ - %2$@", "device_invoice_number_001": "Invoice number", "device_purchase_date_001": "Purchase date", "device_purchase_amount_001": "Purchase amount", "txtchooseadevice_001": "Choose a device", "txtonedevicecashback_001": "You can only earn cashback reward for one device at a time.", "txttotal_002": "Total: %1$@", "cashback_effective_date_001": "%1$@ - %2$@", "cashback_months_completed_001": "Month %1$@ of %2$@", "device_order_information_001": "Order information", "cashback_history_001": "Cashback history", "monthly_device_cashback_001": "Monthly device cashback", "txtwonreward_001_2277": "You earned %1$@ coins for <onboarding>", "txtachieveyourgoalstoearncoins_001": "Achieve your goals to earn more coins.", "txtwonreward_002_2277": "Well done! %1$@ coins have been added to your <<mall>> for <onboarding>", "txtwonreward_004_2277": "Well done! You have won a <<x>> gift card for onboarding", "txtachieveyourgoalstoearngiftcards_001": "Achieve your goals to earn more gift cards.", "txt_vhc_disclaimer_001": "fitness-event-disclaimer", "txthowvitalityworks_001": "How Vitality works", "txtpointsandstatus_002": "Points and status", "txtcoinsandgiftcards_001": "Coins and gift cards", "txtmonthlygoalsandrewards_001": "Monthly goals and rewards", "ctrl_txtcontactus_001": "Contact us", "txtinformationaboutyourdevice_001": "Information about your device, account and this app will be automatically included in this report.", "txthowcanwehelpyou_001": "How can we help you?", "ctrl_btnupload_002": "Upload file (optional)", "ctrl_txtabout_001": "I’d love to give you feedback on my app experience", "txtexperiencingtechnicalissue_001": "I’m experiencing technical issues with my app", "txtneedhelpwithvitalioty_001": "I need help with the Vitality programme", "txtlabelfeedbacktype_001": "Feedback type", "txtalertfeedback_001": "<PERSON><PERSON><PERSON> submitted successfully", "txtdialogheaderfeedback_001": "Call <Insurer>", "txtdialogbodyfeedback_001": "Are you sure you want to start a call to the <Insurer> support centre?", "txt_onboarding_reward_imagepath_001": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox.png", "txt_onboarding3_header_vitalityactivefull": "The higher your status, the greater the <span class='primary h2 medium'>cashback on your premium</span>", "txt_onboarding3_subtext_vitalityactivefull": "Earn up to X annual cashback on your insurance premium.", "txt_onboarding4_header_vitalityactivefull": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>", "txt_onboarding4_subtext_vitalityactivefull": "Points earned from physical activity will contribute toward your weekly and monthly goals.", "txt_onboarding5_header_vitalityactivefull": "Achieve your weekly goals to earn <span class='primary h2 medium'>Medals</span> to spend in the <span class='primary h2 medium'>rewards mall</span>", "txt_onboarding5_subtext_vitalityactivefull": "Spend your Medals on a variety of available rewards", "txt_onboarding_reward_imagepath_vitalityactivefull": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "txt_onboarding3_header_vitalityactivelite": "The higher your status, the greater the <span class='primary h2 medium'>cashback on your premium</span>", "txt_onboarding3_subtext_vitalityactivelite": "Earn up to X annual cashback on your insurance premium.", "txt_onboarding4_header_vitalityactivelite": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>", "txt_onboarding4_subtext_vitalityactivelite": "Points earned from physical activity will contribute toward your weekly and monthly goals.", "txt_onboarding5_header_vitalityactivelite": "Achieve your weekly goals to earn <span class='primary h2 medium'>Medals</span> to spend in the <span class='primary h2 medium'>rewards mall</span>", "txt_onboarding5_subtext_vitalityactivelite": "Spend your Medals on a variety of available rewards", "txt_onboarding_reward_imagepath_vitalityactivelite": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "txt_onboarding3_header_individual": "The higher your status, the greater the <span class='primary h2 medium'>cashback on your premium</span>", "txt_onboarding3_subtext_individual": "Earn up to X annual cashback on your insurance premium.", "txt_onboarding4_header_individual": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>", "txt_onboarding4_subtext_individual": "Points earned from physical activity will contribute toward your daily, weekly and monthly goals.\n\nAnd every week, choose a lifestyle goal to form healthy habits.", "txt_onboarding5_header_individual": "Achieve your weekly goals to earn <span class='primary h2 medium'>Fidcoins</span> to spend in the <span class='primary h2 medium'>rewards mall</span>", "txt_onboarding5_subtext_individual": "Spend your Fidcoins on a variety of available rewards", "txt_onboarding_reward_imagepath_individual": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "txt_onboarding3_header_sme": "The higher your status, the more the <span class='primary h2 medium'>Fidcoins</span> you can earn", "txt_onboarding3_subtext_sme": "Earn <PERSON>ins to exchange for a variety of available rewards", "txt_onboarding4_header_sme": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>", "txt_onboarding4_subtext_sme": "Points earned from physical activity will contribute toward your daily, weekly and monthly goals.\n\nAnd every week, choose a lifestyle goal to form healthy habits.", "txt_onboarding5_header_sme": "Achieve your weekly goals to earn <span class='primary h2 medium'>Fidcoins</span> to spend in the <span class='primary h2 medium'>rewards mall</span>", "txt_onboarding5_subtext_sme": "Spend your Fidcoins on a variety of available rewards", "txt_onboarding_reward_imagepath_sme": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "txt_onboarding3_header_ind_sme": "The higher your status, the more <span class='primary h2 medium'>cashback on your premium</span> and <span class='primary h2 medium'>Fidcoins</span> you can earn", "txt_onboarding3_subtext_ind_sme": "Earn up to X annual cashback on your insurance premium\n\nEarn Fidcoins to exchange for a variety of available rewards", "txt_onboarding4_header_ind_sme": "Stay motivated with personalised goals", "txt_onboarding4_subtext_ind_sme": "Points earned from physical activity will contribute toward your daily, weekly and monthly goals.\n\nAnd every week, choose a lifestyle goal to form healthy habits.", "txt_onboarding5_header_ind_sme": "Achieve your weekly goals to earn <span class='primary h2 medium'>Fidcoins</span> to spend in the <span class='primary h2 medium'>rewards mall</span>", "txt_onboarding5_subtext_ind_sme": "Spend your Fidcoins on a variety of available rewards", "txt_onboarding_reward_imagepath_ind_sme": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "teamchallenges_extend_already_dialog_description_001": "Team challenges can only be extended once a week. Please check back next week to extend the challenge again.", "teamchallenges_extend_already_dialog_title_001": "Team challenge already extended this week", "teamchallenges_extend_button_001": "Extend team challenge", "teamchallenges_extend_dialog_message_001": "By confirming your team challenge will be extended for an additional six weeks.", "teamchallenges_extend_dialog_title_001": "Would you like to extend your team challenge?", "teamchallenges_extend_first_week_dialog_description_001": "Team challenge can only be extended in the 2nd week of the challenge. Please check back next week to extend the challenge.", "teamchallenges_extend_first_week_dialog_title_001": "Team challenge cannot yet be extended in this week", "teamchallenges_extend_success_001": "Team challenge successfully extended", "teamchallenges_manage_data_consent_title_6346": "Data consent and privacy", "teamchallenges_manage_leave_team_title_6347": "Leave team", "teamchallenges_manage_delete_team_title_6348": "Delete team", "teamchallenges_manage_createdby_title_6349": "Created by:", "teamchallenges_manage_team_invite_6350": "Team invite code:", "teamchallenges_manage_leave_messgae_6351": "Are you sure you want to leave this team?", "teamchallenges_manage_rejoin_team_title_6352": "<You can rejoin using your invitation code>", "teamchallenges_manage_cancel_title_6353": "Cancel", "teamchallenges_manage_leave_title_6354": "Leave", "teamchallenges_manage_delete_team_title_6355": "Are you sure you want to delete this team?", "teamchallenges_manage_undo_title_6356": "You cannot undo this action", "proof_attachments_delete_button_2349": "Delete", "teamchallenges_invite_team_sub_title_6336": "This week's", "teamchallenges_invite_team_main_title_6337": "Scoreboard", "teamchallenges_invite_team_progress_6343": "Last week's Progress", "teamchallenges_invite_team_manage_button_6344": "Manage Team", "VHR_Onboarding_title_7182": "How it works", "teamchallenges_invite_team_button_6335": "Invite members", "teamchallenges_chip_steps": "Steps", "teamchallenges_your_team_now_active": "Your team is now active for the next 6 weeks.", "teamchallenges_btn_got_it": "Got it", "txt_team_created_001": "Team created:", "chip_applewatch_cashback_amount_001": "Get up to $240 cashback", "chip_applewatch_commitment_period_001": "24-month commitment", "chip_garmin_cashback_fd_001": "Get up to $240 cashback", "chip_garmin_commitment_period_002": "24-month commitment", "chip_genp_cashback_amount_001": "Get up to 100% back", "chip_genpcommitment_period_001": "12-month commitment", "chip_fitbit_cashback_fd_001": "Get up to $240 cashback", "chip_fitbit_commitment_period_002": "24-month commitment", "chip_samsung_cashback_fd_001": "Get up to $240 cashback", "chip_samsung_commitment_period_002": "24-month commitment", "chip_polar_cashback_fd_001": "Get up to $240 cashback", "chip_polar_commitment_period_002": "24-month commitment", "txtpurchase_polar_device_001": "Purchase\nPolar device", "txtpurchase_samsung_device_001": "Purchase\nSamsung device", "txtpurchase_fitbit_device_001": "Purchase\nFitbit device", "txtpurchase_garmin_device_001": "Purchase\nGarmin device", "txtpurchase_genprod_001": "Purchase\ngenProd", "old_wheel_txtdialogbodyfeedback_001": "Are you sure you want to start a call to the <Insurer> support centre?", "old_wheel_txtswipequicklytospin_001": "Swipe quickly to spin or drag slowly to view rewards", "old_wheel_txttargetachieved_001": "Target Achieved %1$@", "old_wheel_ctrl_btnspinnow_001": "Spin now", "old_wheel_ctrl_btnparticipatingpartners_001": "Participating partners", "old_wheel_ctrl_btnswapforotherreward_001": "Swap for other reward", "old_wheel_txtswapforotherreward_001": "Swap for other reward", "old_wheel_ctrl_btnconfirmnewreward_001": "Confirm new reward", "old_wheel_txtswapforotherrewardfooter_001": "Please not that donation is not subject to issuance of deduction certificate from the donation today", "old_wheel_txtyourcurrentreward_001": "Your current reward", "old_wheel_txtwhatsonthewheel_001": "Participating partners", "old_wheel_txtmakeselection_001": "Make a selection and enjoy!", "old_wheel_txtthewheelistakingawhiletoload_001": "The wheel is taking a while to load...", "old_wheel_txtwouldyouliketocontinuetowaitortrylater_001": "Would you like to continue to wait or try later? ", "old_wheel_ctrl_btnwait_001": "Wait", "old_wheel_ctrl_btntrylater_001": "Try later", "old_wheel_txtthewheelistemporarilyunavailable_001": "The wheel is temporarily unavailable", "old_wheel_dialog_body_": "We're working on it to get it fixed as soon as we can. Look out for your free wheel spin that will be available on your home screen.", "super_roulette_txtwaytogoyouearneda_superwheelspin_001": "Way to go, you earned a Super Wheelspin!", "super_roulette_txtswipequicklytospin_001": "Swipe the spinner below or tap the spin now button to get your reward", "super_roulette_ctrl_txtwhatsonthewheel_001": "What is a Super Wheelspin?", "super_roulette_dialog_hd": "What is a Super Wheelspin?", "super_roulette_dialog_body": "This week is a Super week meaning that for this week you can get even better rewards! You never know when you'll get a Super week, it is a surprise and happens at random.", "txtyourdeviceisonitsway_apple_001": "Your Apple Watch will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.apple.com' class='internalLinkFootnote'>Apple online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.", "txtyourdeviceisonitsway_garmin_001": "Your Garmin will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.garmin.com' class='internalLinkFootnote'>Garmin online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.", "txtyourdeviceisonitsway_fitbit_001": "Your Fitbit will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.fitbit.com/global/us/home' class='internalLinkFootnote'>Fitbit online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.", "txtyourdeviceisonitsway_samsung_001": "Your Samsung will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.samsung.com' class='internalLinkFootnote'>Samsung online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.", "txtyourdeviceisonitsway_polar_001": "Your Polar will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.polar.com/za' class='internalLinkFootnote'>Polar online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.", "teamchallenges_team_enddate_title_001": "Team end date:", "teamchallenges_team_created_001": "Your team has been created!", "teamchallenges_watch_team_progress_001": "Watch your team’s progress towards their goal within the 6-week challenge!", "teamchallenges_successfully_joined_team_001": "You have successfully joined", "teamchallenges_reached_the_maximum_capacity_001": "Your team has reached the maximum capacity of six members.", "txt2factorverificationbody_001": "Choose either text or email as your preferred method to verify your login. We will also notify you via email of any new devices used to log into your account.", "txt2factorverificationcopy_002": "Every time you log into your Vitality account with a new device or a device you have not logged in with for a while, we will request a verification code that will be sent to you. \n\nWe will email you to notify you that a new device has logged into your account.", "txt2factorverificationenabled_001": "2 factor verification enabled", "txt2factorverificationenabled_002": "You can change your security settings and preferences at any time by navigating to your profile in the app", "txt2factorverificationheader_001": "Add extra security to your account with 2 factor verification", "ctrL_btnenable_001": "Enable", "ctrL_btnnothanksmaybelater_001": "No thanks, maybe later", "txtpleaseselectyourmethodofverification_001": "Please select your method of verification", "txtverifywithemail_001": "Verify with email", "txtverifywithemail_002": "The verification email will be sent to the email you used to login", "txtverifywithemailbody_002": "An email with the verification code will be sent to the below email address", "txtverifywithtext_001": "Verify with text", "txtverifywithtextbody_001": "Please enter your phone number", "ctrL_btnselect_001": "Select", "txtphonenumber_001": "Phone number", "ctr__btnsendverificationcode_001": "Send verification code", "ctr_btnemail_001": "Email", "ctrL_btnresendverificationcode_001": "Enter verification code", "txtenterverificationcode_001": "Please enter the verification code sent to %1$@", "txtenterverificationcode_002": "Resend verification code", "ctrL_btnsubmit_001": "Submit", "ctrL_btndone_001": "Done", "txtyouhaveenteredthewrongcodepleasetryagain_001": "%1$@ attempts remaining", "txtyouhaveenteredthewrongcodepleasetryagain_002": "You have entered the wrong code, please try again", "ctrL_btncancel_001": "Cancel", "ctrL_btntryagain_001": "Try again", "ctrL_btnlogout_001": "Logout", "ctrL_btnresetpassword_001": "Reset password", "txtsomethinghasgonewrong_001": "Something has gone wrong", "txtsomethinghasgonewrong_002": "Our system is not responding at the moment which means that some functionality may not work at the moment.\n\nWe apologise for the inconvenience. Please try again later.", "txtincorrectcode_001": "Incorrect code", "txtincorrectcode_002": "The verification code you provided is incorrect. Please try again.", "ctrL_btnok_001": "OK", "txtcodeexpired_001": "Code expired", "txtcodeexpired_002": "The verification code you provided is no longer valid. Please use a new code.", "ctrL_btnsendnewcode_001": "Send new code", "txtuse2faverification_001": "Use 2-step verification", "txtupdate2faverification_001": "Update 2-step verification method or details", "txtcurrentmethod_001": "Current method: %1$@", "txtchoosemethod_001": "Choose method", "tc_txtphysicalactivitygoal_001": "Active challenge friends", "tc_txtgetproof_001": "Participate in a maximum of 5 different teams", "tc_txtautotracked_001": "Automatically tracked through a connected app ", "tc_ctrl_btnselectgoal_001": "Join a team", "tc_ctrl_btn_001": "Create a team", "tc_txthowtocomplete_001": "How it works", "tc_txttakeatwominutemovementbreakeverywakinghour_001": "Participate in multiple teams", "tc_txttakeatwominutemovementbreakeverywakinghour_002": "You can now be a member of 5 different teams by joining or creating teams of your own.\n\nTo join a team, accept an invitation by entering the unique code sent to you.\nYou can also create your own team and invite up to 5 friends to join.\n\nThe team challenge will automatically end after 6 weeks whereafter you can create or join a new team.", "tc_txttakeatwominutemovementbreakeverywakinghour_002_1": "If you have a code:", "tc_txttakeatwominutemovementbreakeverywakinghour_002_2": "Tapping \"Join a team\" and entering the unique code, you can join the invited team.", "tc_txttakeatwominutemovementbreakeverywakinghour_002_3": "If you don't have a code:", "tc_txttakeatwominutemovementbreakeverywakinghour_002_4": "Tapping \"Create a team\" and entering your own team name, you can create a new team and invite up to 5 friends to join", "tc_txtcheckintoyourgoalthesameday_002": "Watch the progress of your team members achieving their physical activity goal, and view your teams scoreboards.", "tc_txtcheckintoyourgoalthesameday_001": "Complete the active challenge with your teams", "tc_leaving_a_team_001": "Leaving a team", "tc_leaving_a_team_002": "Members and the team owner can leave a team at any time during the 6-week period and can rejoin the same team if the team hasn’t reached the maximum number of members.", "tc_txtwhyitsimportant_001": "Why it's important", "tc_wlg_healthcheck_txbodytip_001": "<Exercising with others boosts your mental health, creates community connections and helps you to stay motivated.>", "tc_txthowtocomplete_002": "Your teams", "tc_txtsubmitresultsandproofexplanationcopy_001": "Please enter the team code sent by the inviter.", "tc_label_001": "Enter code", "tc_ctrl_btnnext_001": "Next", "tc_dialog_hd_001": "Team at capacity", "tc_dialog_body_001": "The team you’re trying to join is at maximum capacity. Please join another team or create a new team.", "tc_dialog_hd_002": "Incorrect code", "tc_dialog_body_002": "The code you entered is incorrect. Please try again.", "tc_btn_Agree": "Agree", "tc_btn_Disagree": "Disagree", "tc_txthdunknwnerr_001": "Are you sure?", "tc_txtbdunknwnerr_002": "By disagreeing, you’ll be unable to join a team.", "tc_ctrl_btncancel_001": "No, go back", "tc_ctrl_btnyeslogout_001": "Yes, disagree", "tc_txt_001": "Create team name", "tc_txtsubmitresultsandproofexplanationcopy_002": "Create a name for your team.", "tc_label_002": "Name of team", "tc_ctrl_btnnext_002": "Create team", "tc_txtbdunknwnerr_003": "By disagreeing, you’ll be unable to create a team.", "txtaccountlocked_001": "Account locked", "txtaccountlockedcopy_001": "Your account has been locked as incorrect authentication code entered too many times   \n\nPlease reset your password and try again.", "ctrl_btnactivatereward_001": "Activate reward", "txtmonthlyhealthyfoodcashback_001": "Monthly healthy food cashback", "txt_requiredactivity_001": "Required activity", "txtpurchasehealthy_food_001": "Purchase\nhealthy food", "txtearn_hf_reward_001": "Earn\ncashback", "txtearn_hf_reward_002": "Earn status\npoints", "txtexercise_001": "Physical activity", "txtpointsperday_001": "50-200 Points per day", "txtmanual_gym_checkin": "Manual gym check-in", "ctrl_btngymcheckin_001": "Gym check-in", "ctrl_btnconnectanappordevice_001": "Connect an app or device", "hf_footer_email_001": "<EMAIL>", "txt_hf_footer_001": "If you need assistance, please contact us at", "chip_cashback_HF_001": "Get up to 25% cashback", "highest_cashback_achieved": "Highest cashback achieved", "cashback_date_range_001": "%1$@ - %2$@", "txtxpointsmonth_001": "points this month", "txtcashbackmonth_001": "cashback this month", "cashback_calc_day_001": "Cashback will be calculated at the <day> of the next month.", "total_cashback_002": "Total cashback", "Total_cashback_earned_001": "earned so far out of %1$@", "txtyourprogress_001": "Month %1$@ of %2$@", "levels_cashbackpts_range": "%1$@-%2$@ pts", "levels_cashbackpts_range_last": "%1$@+ pts", "healthy_food_complete_hc_001": "Complete the Health Check", "Supermaxi_membership_001": "Active Supermaxi membership required", "hf_onboarding_level_points_004": "4,500+ pts", "hf_onboarding_level_points_003": "3,000-4,499 pts", "hf_onboarding_level_points_002": "2,000-2,999 pts", "hf_onboarding_level_points_001": "0-1,999 pts", "hf_onboarding_cycle_cashback_004": "25%", "hf_onboarding_cycle_cashback_003": "15%", "hf_onboarding_cycle_cashback_002": "10%", "hf_onboarding_cycle_cashback_001": "5%", "hf_onboarding_cashback_004": "25% cashback", "hf_onboarding_cashback_003": "15% cashback", "hf_onboarding_cashback_002": "10% cashback", "hf_onboarding_cashback_001": "5% cashback", "hf_visit_website": "Visit website", "question_input_hint_102503": "Years", "question_input_hint_102452": "per week", "question_input_hint_100111": "Centimetres (cm)", "question_input_hint_100105": "INCH", "question_input_hint_100102": "kg", "question_input_hint_100109": "LB", "question_input_hint_102476": "st & lb", "question_input_hint_100113": "mmol/L", "question_input_hint_100118": "mg/dL", "question_input_hint_100123": "mg/dL", "question_input_hint_100112": "mmHg", "question_input_hint_102501": "mmol/mol", "question_input_hint_100114": "%", "question_input_hint_101627": "mL/kg/min", "question_input_hint_101626": "mmol/L", "question_input_hint_100120": "mg/dL", "question_input_hint_101505": "Hours", "question_input_hint_100127": "mmol/L", "question_input_hint_100128": "mg/dL", "question_input_hint_height_ft": "Ft", "question_input_hint_height_in": "In", "question_input_hint_height_cm": "Cm", "question_input_hint_420000001": "mmol/L", "question_input_hint_310000075": "mmol/L", "question_input_hint_410000001": "mmol/L", "healtassessment_modalsuccess_txthealthassessmentcomplete_003": "We're calculating your health results. Your personalised feedback will be available in the Health tab shortly\n\nYour points may not reflect immediately.", "txtidontknow_001": "I don’t know", "txtsection": "SECTION", "txtrange": "Range", "txttimeforyourfirstspin_001": "Take your first spin", "txtswiptetospin_002": "You’re off to a great start. Swipe to spin for your reward.", "tracking_screen_header_001": "Monthly %1$@ goal", "txtxcoinsmonth_001": "coins this month", "txtcasback_001": "cashback", "total_coins_earned_001": "coins earned so far out of %1$@", "txtapplewatch_001": "Apple Watch", "txt_reward_activation_time_001": "Your healthy food reward may take 2-3 hours to be activated.", "txt_reward_hf_starts_soon_001": "Your healthy food cashback reward starts soon", "tc_how_it_works_one": "You can now be a member of 5 different teams by joining or creating teams of your own.", "tc_how_it_works_two": "To join a team, accept an invitation by entering the unique code sent to you.", "tc_how_it_works_three": "You can also create your own team and invite up to 5 friends to join.", "tc_how_it_works_four": "The team challenge will automatically end after 6 weeks where after you can create or join a new team.", "txtmentalwellbeingassessment_001": "Mental health assessment", "txtlowrisk_001": "Low risk", "txtstayconnectedtofriendsandfamily_001": "Stay mentally fit", "mentalwellbeing_assessment_txtblowrisk_001": "Life can be tough but you’re handling it well.", "txtmoderaterisk_001": "Moderate risk", "txtgetsupportnow_001": "Reach out for help", "mentalwellbeing_assessment_txtbmoderaterisk_001": "You may be experiencing symptoms of depression and/or anxiety that requires attention. We recommend you reach out to a healthcare provider to discuss your results.", "txthighrisk_001": "High risk", "txtconsultyourhealthcareprovider_001": "Consult a healthcare provider", "mentalwellbeing_assessment_txtbhighrisk_001": "Reach out to a healthcare provider right away to discuss your mental health status and results.", "mha_section_Iconkey_118": "solid head-side-heart", "mha_section_Iconkey_119": "solid cloud-drizzle", "mha_section_Iconkey_120": "solid alarm-clock", "txtearnpoints_001": "Earn points", "txtfeedsheadergoals_001": "Build healthy habits", "txtassessments_001": "Assessments", "txtprevention_001": "Prevention", "ctrl_txt_onlysendreport_001": "Only send a report when asked by an agent to do so in order for them to assist you further. These reports are temporarily stored and deleted regularly to keep your data safe", "ctrl_txt_haveyoubeenaskedtosend_001": "Have you been asked to send a trouble shooting report?", "ctrl_txt_sendreport_001": "Send report", "ctrl_txt_sendatroubleshootingreport_001": "Send a trouble shooting report", "ctrl_txt_onlysendareport_001": "Only send a report when you are asked to send technical data report for Google Fit or Samsung", "txtareyousureyouwanttoclosethesurvey_001": "Are you sure you want to exit this assessment?", "txt_closesurvey_001": "This assessment is very short. If you exit now, you'll need to start over.", "ctrl_txtcancel_001": "Cancel", "ctrl_txtyes_001": "Yes", "ctrl_btncontinuetofeedback_001": "View results", "vhc_submission_screen_date_of_activity_date_format": "dt_format_date_short_month_full_year", "dt_format_date_short_month_full_year": "dd <PERSON><PERSON> yyyy", "txtbronze_001": "Bronze", "txtbronze_subtext_001": "0% discount on premium", "txtsilver_subtext_001": "5% discount on premium", "txtgold_subtext_001": "10% discount on premium", "txtplatinum_subtext_001": "15% discount on premium", "pag_history_header_date_format": "dt_format_short_month_full_year", "pag_history_list_week_date_format": "dt_format_date_short_month", "wlg_history_header_date_format": "dt_format_short_month_full_year", "wlg_history_list_week_date_format": "dt_format_date_short_month", "dt_format_short_month_full_year": "MMM yyyy", "dt_format_date_short_month": "dd <PERSON><PERSON>", "how_to_earn_points_steps_number_format": "num_format_non_decimal_number", "how_to_earn_points_points_number_format": "num_format_non_decimal_number", "num_format_non_decimal_number": "#,##,000", "txtbronze_subtext_002 ": "Earn 0 FidCoins", "txtsilver_subtext_002": "Earn 1,000 FidCoins", "txtgold_subtext_002": "Earn 2,000 FidCoins", "txtplatinum_subtext_002": "Earn 3,000 FidCoins", "txtblue_subtext_001": "2% increase on premium", "txtmentalwellbeingasessmentcomplete_001": "Great work on completing your mental health assessment", "txtVHC_Submissions_Date_001": "Your activity must have been completed in the current membership year or up to three months before your membership started.", "txtVHC_Submissions_Date_002": "Your activity must have been completed in your current or previous membership year.", "txtavailabledevices_001": "Available devices", "txtonlyonedevicecashback_001": "Only one device cashback can be activated at a time.", "pending_calculation_001": "Calculation pending", "txt_uptoperc_cashback_001": "Get up to %1$@ cashback", "txt_ptstostatus_001": "Get up to 3,600 points towards status", "txtdaterange_001": "300 points twice a membership year, three months apart", "txthowtoearnsubtext_001": "Enjoy these benefits just for being a Vitality member.", "txthowtoearnsubtext_004": "Achieve your weekly goals and get a spin on the wheel to earn to earn coins and spend them on a variety of rewards in the mall.", "txthowtoearnsubtext_005": "Achieve your weekly goals and get credits to spend on a variety of products or experiences.", "txtcheckups_001": "Check-ups", "txt_pag_earn_maximum": "Earn a maximum of <<24 000>> points towards your Vitality status per membership year.", "txt_pag_no_maximum": "There is no maximum for points counting towards your weekly physical activity and monthly cash back goals.", "txt_hf_paused_001": "To continue earning cashback on healthy food complete your health check.", "txt_automatic_gym_note": "<Scan the code at your gym to sync with your linked health app and allow 24 hours for Vitality to add your points.>", "txt_reward_pendactivation_time_001": "Your healthy food reward may take 2-3 hours to be activated. Check back soon.", "txtrewardsmall_001": "Rewards mall", "txt_hf_purchase_fresh_001": "Purchase fresh fruit and vegetables at Supermaxi", "ctrl_txt_pts_001": " pts", "ctrl_txt_yourcurrentmembershipyear_001": "Your current membership year", "ctrl_txt_totalpointsearned_001": "Total points earned", "ctrl_txt_daysleft_001": " Days left ", "ctrl_txt_statusrewards_001": " %1$@% status rewards", "ctrl_txt_yourrewardlevel_001": "Your reward level", "ctrl_txt_discounton_001": "%1$@%% discount on %1$@%", "ctrl_txt_activitiestoearnpts_001": "Activities to earn points", "ctrl_txt_keepearningpts_001": "Keep earning points towards your status!", "ctrl_txt_thehealthieryouare_001": "The healthier you are, the higher your status.", "ctrl_txt_relatedfaqs_001": "Related FAQs", "txtotheractivitiestext_001": "Other Activities", "pg_txt_title_001": "Pokémon GO", "pg_calendar_day_002": "Earn promo code", "pg_btnactivatereward_003": "Activate reward", "pg_txthowitworks_004": "How it works", "pg_txtheaderactivatebenifit_005": "Activate the benefit", "pg_txtactivatebenifitdesc_line1_005": "In addition to the existing weekly reward spin, each time you complete your weekly physical activity, you will receive a promo code to get a surprise item from the Pokémon Go app.", "pg_txtactivatebenifitdesc_line2_005": "To activate this benefit you must have weekly physical goal activity goal activated and the Pokémon Go app already installed.", "pg_txtheaderletexercise_006": "Let’s exercise", "pg_txtdescletexercise_006": "Let’s exercise to achieve your weekly physical activity rewards.", "pg_txtheaderletexercise_007": "Get rewards", "pg_txtdescletexercise_line1_007": "Once you complete your weekly goal of physical activity, you can get a Promo code to exchange for items that can be used on Pokémon Go.", "pg_txtdescletexercise_line2_007": "Watch out for cars, other pedestrians and obstacles when you play Pokémon Go and do not play while driving.", "pg_txtheaderpromocodes_008": "Using the Promo Codes", "pg_txtdescpromocodes_line1_008": "After completing your weekly physical activity goal, you will have 14 days to claim the promo code within Vitality app.", "pg_txtdescpromocodes_line2_008": "Once claimed, you have until the end of the next month to get your surprise item.", "pg_txtdescpromocodes_line3_008": "To get your surprise item, copy your promo code from the Vitality app and enter it into the Pokémon Go app.", "pg_txtcardtitleweeklyphyactgoal_009": "Weekly physical acitivity goal", "pg_txtcardtitleRelatedfaqs_010": "Related FAQs", "pg_txt_notinstalldialog_hd_011": "Install Pokémon Go", "pg_txt_notinstalldialog_body_012": "You will need to have the Pokémon Go app installed to activate this benefit.", "pg_txtheaderactivatedgoal_013": "Your Pokémon GO reward is activated", "pg_txtsubtitleactivategoal": "Your reward will begin on %1$@", "pg_btngotit_014": "Got it", "pg_txtyourfirstgoalstartsondate_016": "Your challenge starts on %1$@", "pg_txtcardtitlearcsupitem_017": "Archived surprise items", "pg_txtarchivedgiftcards_018": "Archived items", "pg_txtnogiftcardsarchived_019": "No archived items", "pg_txtnogiftcardsarchiveddesc_020": "Expired items will automatically appear here.", "pg_btnarchive_021": "Archive", "pg_txtitemswillshowhere_022": "Your items will show here.", "pg_txtclaimsurpriseitem_023": "Claim surprise item", "pg_txtrevealyourpromocode_024": "Reveal your promo code", "pg_txtbrandName_025": "Goal achieved: %1$@", "pg_btnrevealcode_026": "Reveal code", "pg_txtusethecodetoredeem_027": "Use this code to redeem your reward", "pg_txtdiscountcode_028": "Discount code", "pg_btncopy_029": "Copy", "pg_btnvisitwebsite_030": "Go to Pokémon GO", "pg_btnarchivegiftcard_031": "Archive surprise item", "pg_txtarchivedgiftcards_032": "Archived items", "pg_txtaechivesurpriseitemtitle_033": "Surprise item", "pg_txtexpiesInDays_034": "Expires in %1$@ days", "pg_txtexpies_035": "Expires: %1$@", "pg_txtheadererror_036": "<Re<PERSON> temporarily unavailable>", "pg_txterrordescription_037": " Something went wrong. We're working on it to get it fixed as soon as we can.   Thanks for your patience.", "pg_txtredeemcodehelp_038": "<Reveal the code you will be able to use to redeem your reward>", "pg_txtanynotefromsupplier_039": "<Any notes from the suppliers will be shown here.>", "pg_txtheadersurprise_040": "Your Pokémon GO surprise items", "pg_txtsubheadersurprise_041": "Achieve your weekly active challenge to earn your Pokémon GO surprise items!", "pg_btninstall_042": "Install", "pg_btncancel_043": "Cancel", "vj_pageHeaderH2_v1": "Vitality Journey", "vj_pointsTracker_v1": "collect medals", "vj_singleContentLine_v1": "Automatically tracked through a connected app", "vj_ctrl_btnselectgoal_001": "participate", "vj_txthowtocomplete_001": "how it works", "vj_txttakeatwominutemovementbreakeverywakinghour_001": "Aim to virtually cross the country from Okinawa to Hokkaido", "vj_txttakeatwominutemovementbreakeverywakinghour_002": "On the map, start from Okinawa and aim for Hokkaido by virtually walking each prefecture based on your device data and stride length. The distance required to traverse the entire country is 2,500km.", "vj_sectionHeaderH5_v1": "Collect a medal for each prefecture you complete", "vj_paragraphText_v1": "Collect all 47 medals by completing prefectures. Each medal is unique and associated with the region.", "vj_txtcheckintoyourgoalthesameday_001": "Use a supported app or device to track your distance data automatically", "vj_txtcheckintoyourgoalthesameday_002": "Connect an app or device to track your steps and distance data.", "vj_txtcheckintoyourgoalthesameday_003": "About stride length", "vj_txtcheckintoyourgoalthesameday_004": "Calculate distance by multiplying the step count based on the stride length entered. e.g.) If the stride length is 79cm, it will be calculated as 10,000 steps x 79cm = 7.9km.", "vj_ctrl_txtappsanddevices_001": "Apps and devices", "vj_ctrl_txtaboutphysicalactivitygoal_001": "Related FAQs", "vj_txtsetstridelength_001": "Set stride length", "vj_txtsubmitresultsandproofexplanationcopy_001": "Please enter your stride length.", "vj_txtpointsmaytakeupto24hourstoreflect_001_1": "Distance is calculated by multiplying the number of steps based on your stride length.", "vj_txtpointsmaytakeupto24hourstoreflect_001_2": "Eg. If your stride length is 79cm, distance is calculated as follows: 10,000 steps x 79cm = 7,9km.", "vj_txtpointsmaytakeupto24hourstoreflect_001_3": "If you update your stride length, previously synced data won’t be affected.", "vj_label_": "Stride length", "vj_ctrl_btnnext_001": "Save and continue", "vj_txtdisconnected_001": "Stride length successfully set", "vj_supportText_": "You need to provide a valid stride length.Valid range %1$@ - %2$@ cm", "vj_txtgetreadytostartmoving_001": "Get ready to walk the map of Japan!", "vj_txtyourfirststartgoal_001": "Collect all 47 medals to complete your journey.", "vj_ctrl_btngotit_001": "Let’s go", "vj_txtautotracked_001": "%1$@ cm stride length", "vj_comboHd1": "Current progress", "vj_txtpointsreflectnote_001": "%1$@ km to complete prefecture.", "vj_comboHd2": "Total progress", "vj_comboHd3": "Map of Japan", "vj_txt_001": "kms completed", "vj_txtaccountdetails_001": "Medals", "vj_ctrl_txthowitworks_001": "How it works", "vj_txtbodyhowtocomplete_001": "Select a medal to view more detail", "vj_sectionHd1": "Kyushu & Okinawa", "vj_sectionHd2": "Shikoku", "vj_txt_002": "Vitality Journey instagram page", "vj_txtmilestoneachieved_001": "Walk %1$@ more prefectures and %2$@ km to earn this medal", "vj_txtmilestoneachieved_002": "Walk %1$@ km more to earn this medal", "vj_success": "Well done on reaching %1$@", "vj_success_sub_text": "You’ve just collected this medal and 9 more!", "vj_ctrl_btn_001": "View medal", "vj_physicalactivity_modalactivated_txtactivatedgoal_002": "You’ve done it! You’ve walked the map of Japan!", "vj_physicalactivity_modalactivated_txtyourfirststartgoal_002": "Well done on collecting all 47 medals.", "vj_ctrl_btngotit_002": "Got it", "vj_ctrl_btn_002": "Achieved", "vj_txtwhatsonthewheel_001": "Your stride length is %1$@ cm", "vj_dialog_body_": "Stride length is calculated using your height. You can save this as your stride length or manually set it up.", "vj_label_text": "Save and continue", "vj_dialog_btn1_": "Set up manually", "vj_dialog_btn1_1": "Cancel", "ctrl_txt_youarestatus_001": "You are on %1$@ status and need %2$@ points to reach %3$@.", "ctrl_txt_yourmembershipyearreset_001": "Your membership year resets annually based on the date you joined Vitality.", "ctrl_txt_youhavedaysleft_001": "You have %1$@ days left to earn points and progress to %2$@ Vitality status within your current membership year.", "ctrl_txt_moreonvitalitystatus_001": "More on Vitality status", "ctrl_txt_atthestartofeverymonth_001": "At the start of every membership year, your points reset to zero.", "ctrl_txt_achievethehighestvitalitystatus_001": "Achieve the highest Vitality status to maximise your %1$@.", "ctrl_txt_whyitsimportant_001": "Why it’s important?", "ctrl_txt_yourvitalitystatusisameasure_001": "Your Vitality status is a measure of your health. The healthier you are, the higher your Vitality status - the greater your rewards.", "ctrl_txt_howstatusworks_001": "How status works", "ctrl_txt_howvitalitystatusworks_001": "How Vitality status works", "ctrl_txt_duringthistime_001": "During this time, you have a specific number of days left to earn more points. By doing so, you can increase your Vitality status from Bronze to Platinum within the same membership year.", "ctrl_txt_atthestartofeachmembershipyear_001": "At the start of each membership year, your points reset to zero and your Vitality status will carry over. You will also keep your rewards level you earned and it will display as a check mark on your status tracker.", "ctrl_txt_maintainorearnhigherrewards_001": "Maintain or earn a higher rewards level to continue enjoying great rewards.", "ctrl_txt_yourvitalitystatusmeasure_001": "Your Vitality status is a measure of your health. As you make healthier choices, your Vitality status increases – unlocking greater rewards and a healthier, happier you.", "txtrewards2047": "CHOOSE REWARD", "txtrewards2048": "EARN 1 SPIN", "txtrewards2049": "EARN 1 SPIN", "txtrewards2050": "EARN 200 COINS", "txtrewards2051": "EARN 200 COINS", "iconrewards2047": "solid gift", "iconrewards2048": "regular ferris-wheel", "iconrewards2049": "regular ferris-wheel", "iconrewards2050": "solid coins", "iconrewards2051": "solid coins", "txt_no_history_001": "No history available", "txt_hf_cashback_001": "Healthy food cashback", "txt_hf_history_avail_001": "Your cashback history will be available from the 8th of the next month.", "txt_no_cashback_earned_001": "NO CASHBACK EARNED", "txt_perc_cashback_spend_001": "%1$@% CASHBACK ON SPEND %2$@", "txt_earned_cashback_001": "Cashback earned", "tc_no_history_available": "No history available", "tc_last_week_empty_list_msg": "This is where you will be able to view your team's progress last week.", "tc_active_challenge_starts_next_monday": "Active challenge starts next Monday", "tc_goals_met": "Goals met", "tc_points_earned": "points earned", "tc_unknown_error": "Unknown error", "tc_error_occurred_try_again": "An unknown error occurred. Please try again.", "tc_Check_out_this_link": "Check out this link!", "tc_you": "(you)", "dt_format_day_date_short_month": "E<PERSON>, dd <PERSON><PERSON>", "dt_format_day_date_short_month_full_year": "E<PERSON>, dd MMM yyyy", "txt_maintainpoinstatus_001": "You need %1$@ points to maintain your %2$@ status from the last membership year", "question_input_hint_102453": "Feet (ft) & inches (in)", "txt_systolic": "Systolic", "txt_dialostolic": "Diastolic", "txt_feet": "Feet", "txt_inch": "Inch", "txt_range_001": "Range %1$@ - %2$@", "apps_devices_sync_date_format": "dt_format_date_short_month_full_year_twelve_hours_mins", "dt_format_date_short_month_full_year_twenty_four_hours_mins": "dd MMM yyyy , HH:mm", "dt_format_date_short_month_full_year_twelve_hours_mins": "dd MMM yyyy , hh:mm a", "txtbody_maxfile_006": "Upload up to 5 attachments", "txt_points_limit": "%1$@ points limit", "txt_points_limit_description_001": "Status points limit for physical activity per membership year.", "dialog_body_changingpassword_001": "Please change your password to 12 or more characters and change your email address again. Your password is incorrect if your password already complies with the new criteria.", "dialog_hd_changepassword_001": "You will need to change your password under the new criteria.", "dialog_btn2_reenterpassword_001": "Re-enter Password", "dialog_btn1_changepassword_001": "Change password", "txtblue_subtext_001_individual": "2% increase on premium", "txtblue_subtext_001_sme": "2% increase on premium", "txtbronze_subtext_001_individual": "0% discount on premium", "txtbronze_subtext_002_individual": "0% discount on premium", "txtbronze_subtext_001_sme": "Earn 0 FidCoins", "txtbronze_subtext_002_sme": "Earn 0 FidCoins", "txtbronze_subtext_001_ind_sme": "0% discount on premium", "txtbronze_subtext_002_ind_sme": "Earn 0 FidCoins", "txtsilver_subtext_001_individual": "5% discount on premium", "txtsilver_subtext_002_individual": "5% discount on premium", "txtsilver_subtext_001_sme": "Earn 1,000 FidCoins", "txtsilver_subtext_002_sme": "Earn 1,000 FidCoins", "txtsilver_subtext_001_ind_sme": "5% discount on premium", "txtsilver_subtext_002_ind_sme": "Earn 1,000 FidCoins", "txtgold_subtext_001_individual": "10% discount on premium", "txtgold_subtext_002_individual": "10% discount on premium", "txtgold_subtext_001_sme": "Earn 2,000 FidCoins", "txtgold_subtext_002_sme": "Earn 2,000 FidCoins", "txtgold_subtext_001_ind_sme": "10% discount on premium", "txtgold_subtext_002_ind_sme": "Earn 2,000 FidCoins", "txtplatinum_subtext_001_individual": "15% discount on premium", "txtplatinum_subtext_002_individual": "15% discount on premium", "txtplatinum_subtext_001_sme": "Earn 3,000 FidCoins", "txtplatinum_subtext_002_sme": "Earn 3,000 FidCoins", "txtplatinum_subtext_001_ind_sme": "15% discount on premium", "txtplatinum_subtext_002_ind_sme": "Earn 3,000 FidCoins", "link_hf_supermaxi_001": "https://www.supermaxi.com/actualizacion-datos", "txt_waiting_001": "Sorry, this page is taking a while to load.", "txt_try_again_001": "Please try again later.", "tc_Sticker_history": "Sticker history", "tc__only_the_10_latest_stickers_will_be_displayed": "Only the 10 latest stickers will be displayed.", "tc_sent_by": "Sent by", "tc_send_sticker": "Send sticker", "tc_sticker_sent": "<PERSON><PERSON> sent", "two_factor_txtinvalidnumber_001": "Please enter a valid phone number", "two_factor_txtmethodemail": "email", "two_factor_txtmethodtext": "text", "txtgooglefitunabletoconnect_001": "Google Fit unable to connect", "txtgooglefitunabletoconnectcopy_001": "If you do not grant permissions for\nphysical activity and vital signs, Google Fit\nwill not be able to connect. Please enable\nthese permissions to continue.", "ctrl_btncancelconnection_001": "Cancel connection", "ctrl_btnopensettings_001": "Open settings", "txtpermissionrequired_001": "Permission required", "txtpermissionrequiredcopy_006": "Vitality requires permissions to access\nalarms and reminders to ensure end-of-\nday data sync can occur.", "ctrl_btnskipfornow_001": "<PERSON><PERSON>, for now", "txtpermissionrequiredcopy_005": "Without these permissions not all data will\nsync with Vitality. Please grant Vitality permission to access alarms and\nreminders.", "txtgooglefitwillbedisconnected_001": "Google Fit will be\n disconnected", "txtgooglefitwillbedisconnectedcopy_001": "If you do not grant the required\npermissions, Google Fit will be\ndisconnected.", "ctrl_btnback_001": "Back", "txtpermissionrequiredcopy_001": "Your permissions were manually disabled.\nVitality requires permissions to access\nalarms and reminders.Without these\npermissions not all data will sync with\n<PERSON><PERSON>.", "txtpermissionrequiredcopy_002": "Vitality requires permissions to access\nalarms and reminders. Without these\npermissions not all data will sync with\nVitality.", "btn_viewreawards_001": "View rewards", "txtwelcometoanewmembershipyear_001": "Welcome to a new membership year!", "txtyourvitalitypointshavebeenreset_001": "Your Vitality points have been reset. Start earning points to maintain your previous year’s status and rewards in this membership year. Or reach a higher status for bigger rewards!", "txtstatus_silver": "Way to go!\nYou've achieved Silver Vitality status.", "txtstatus_gold": "Way to go!\nYou've achieved Gold Vitality status.", "txtstatus_platinum": "Way to go!\nYou've achieved Platinum Vitality status.", "txtstatus_bronze": "Way to go!\nYou've achieved Bronze Vitality status.", "txtstatus_new_status": "You can now enjoy new status-based rewards. Check them out now or view them in your Rewards tab.", "ctrl_btnviewrewards_001": "View status details", "question_text_box_hint_10034": "Waist circumference", "question_text_box_hint_10033": "Weight", "question_text_box_hint_10040": "Total cholesterol", "question_text_box_hint_10042": "LDL cholesterol", "question_text_box_hint_10045": "HbA1c (A1C)", "question_text_box_hint_10013": "VO2 max", "question_text_box_hint_10046": "Fasting glucose", "question_text_box_hint_10059": "Servings", "question_text_box_hint_10211": "Years", "question_text_box_hint_10063": "Hours per day", "question_text_box_hint_10062": "Years", "question_input_hint_100103": "m", "question_input_hint_100117": "ft.inch", "question_input_hint_100122": "mmol/l", "question_input_hint_100125": "mmol/l", "question_input_hint_100129": "mmHg", "question_text_box_hint_10032": "Cm", "txtwhatsappchat_001": "Whatsapp chat", "txtchatdialogsubtext_002": "This will open the Whatsapp application to connect with our customer support service.", "txtcontactnumber_001": "Contact Number", "ctrl_btnemail_001": "Email", "ctrl_btnsendfeedback_001": "Send Feedback", "ctrl_btnstartwhatsappchat_001": "Start WhatsApp chat", "txt_foot_note": "%1$@ points earned last year", "ctrl_txt_onlysendareport_ios_001": "Only send a report when you are asked to send technical data report for Apple Health", "vj_cm": "cm", "txt_profile_header_points_001": "POINTS", "ctrl_btncontactnumber_001": "Contact Number", "earn_max_coins_001": "Earn up to 18,000 coins", "earn_max_coins_term_001": "Earn up to 18,000 coins with a 18-month commitment period.", "txtpointstowardsgoal_001": "%1$@ points towards goals", "txtgoal_achieved_001": "Goal achieved: %1$@", "txtpointstowardsgoal_002": "Points towards goals", "txttowardsgoalbreakdown_001": " %1$@ towards %2$@", "txt_membership_name_vitalityactivefull": "Vitality Active Full", "txt_membership_name_vitalityactivelite": "Vitality Active Lite", "txt_membership_name_individual": "Individual Plan", "txt_membership_name_sme": "SME Plan", "txt_membership_name_ind_sme": "Individual & SME Plan", "txtpointstowardsstatus_001": "%1$@ points towards status", "hc_askchatbot": "<PERSON>", "hc_faqs": "FAQs", "hc_new_bulletin_board": "New bulletin board (new information)", "hc_video_tutorials_on_app_operation": "Video tutorials on app operation", "hc_Vitality_special_site": "Vitality special site", "hc_users_guide_to_benefits": "User’s guide to benefits", "hc_terms_and_conditions": "Terms and conditions", "hc_apply_for_steps_points": "Apply for steps points", "hc_send_inquiry_form": "Send inquiry form", "txtwerehavingtroubleconnectingyou_001": "We're having trouble connecting you. Please try again later or check your internet connection.", "txtunabletoconnecttopartnerapp_001": "Unable to connect to %1$@", "txtissueconnectingtopartnerapp_001": "There was an issue connecting to %1$@. Please try again later.", "txtwhatsapp": "WhatsApp", "oldwheel_txt15creditshavebeendepositedinyourcygmall_001": "Thank you for your donation to ", "txttime005": "Takes 5 minutes to complete", "book_now": "Book Now", "watch_videos": "Watch Videos", "txtmailapplication": "mail application", "txtalertcouldntprocessyourgiftcardselection_001": "Sorry, we couldn't process your gift card selection. Choose your gift card later.", "txt_invalidcodeerror_001": "Invalid code", "vj_update_stride_length": "Update stride length", "vj_you_done_it": "You've done it!\nYou've walked the map of Japan!", "vj_well_done": "Well done on reaching %1$@", "vj_just_collected": "You've just collected this medal and %1$@ more!", "vj_next_destination": "Next destination: ", "vj_km": "km", "txt_nopointsearningactivity_002": "No points earning activity", "txt_points_002": "POINTS", "txt_day_001": "day", "txt_days_001": "days", "txt_uploaderror_001": "Larger than 5MB", "txt_mobilenumber_001": "Mobile Number", "txt_message_001": "Message", "txt_typehere_001": "Type here", "txt_cannottbeempty_001": "Can not be empty", "txt_feedback_submission_failure_001": "Unable to submit the feedback, Please try again", "txt_feedback_001": "<PERSON><PERSON><PERSON>", "txtpoints_009": "points towards status", "txt_monthly_hf_cashback_001": "Monthly healthy food goal", "txt_progress_001": "Your progress", "txt_earned_sofar_001": "earned so far", "txt_no_cashback_earned_002": "No cashback earned", "txt_your_insurance_reward_001": "Your insurance reward", "txt_unlock_cashback_001": "To unlock this benefit, you need to submit all of your results in the health check.", "txtinvalidquickguidedata": "Invalid quick guide data", "txthealthpartners": "Health Partners", "txttroubleshootingreportsent_001": "Trouble shooting report sent", "txttroubleshootingreportsent_002": "Trouble shooting report failed", "txt_label_off": "Off", "txt_label_on": "On", "ha_tracking_date_format": "dt_format_date_short_month_full_year", "pag_history_list_day_date_format": "dt_format_day_date_short_month", "pag_tracking_header_date_format": "dt_format_day_date_short_month_full_year", "pag_activation_modal_started_date_format": "dt_format_day_date_short_month_full_year", "txt_biometric_note_001": "Please note: Any biometric registered on your device under device settings can be used to login to the Generali Vitality App. Generali Vitality does not receive any biometric data.", "mha_tracking_next_available_date": "dt_format_date_short_month_full_year", "appdevices_strava_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_withings_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_fitbit_subtitle": "<span class ='base900'>Tracks from mobile / wearable device</span>", "appdevices_garmin_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_softbank_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_polar_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_suunto_subtitle": "<span class ='base900'>Requires wearable device</span>", "appdevices_omron_subtitle": "<span class ='base900'> Require fitness device <.span>", "appdevices_19_subtitle": "<span class ='base900'> If you don’t have a wearable device. </span>", "txtupdateyourapp_001": "Update your %1$@ app", "txtsoftupdate_001": "We’ve enhanced our app. Update to the latest version to enjoy an improved experience.", "txtforcedupdate_001": "We've enhanced our app. Update to the latest version to continue.", "txtblacklisting_001": "Unfortunately your current version of the app is outdated and no longer supported. You will need to update it in order to continue.", "ctrl_btnlater_001": "Later", "ctrl_btnupdatenow_001": "Update now", "ctrl_btncloseapp_001": "Close app", "vhc_date_format": "dt_format_date_short_month_full_year", "tc_error_email_max_length": "Maximum %1$@ character limit reached", "txt_we_updated_your_biometric": "We’ve updated your %1$@", "chip_cashbacklite_fd_001": "Get up to $240 cashback", "levels_cashbackptslite_range": "%1$@-%2$@ pts", "levels_cashbackptslite_range_last": "%1$@+ pts", "genericfd_cashbacklite_1": "$2 cashback", "genericfd_cashbacklite_2": "$5 cashback", "genericfd_cashbacklite_3": "$10 cashback", "genericfd_cashbacklite_4": "$15 cashback", "genericfd_circlecontentlite_1": "$2", "genericfd_circlecontentlite_2": "$5", "genericfd_circlecontentlite_3": "$10", "genericfd_circlecontentlite_4": "$15", "txtuptoXcashbacklite_001": "UP TO $240 CASHBACK", "txtuptoXcashback_genp_001": "UP TO 100% CASHBACK", "txt_generic_prod_001": "Get your next cashback product through asr Touch Webshop", "tfa_dialog_btn2_leave": "Leave", "tfa_dialog_btn2_retry": "Retry", "tfa_dialog_code_attempts_content": "You have entered the wrong code, please try again", "tfa_dialog_code_attempts_title": "%1$@ attempts remaining", "tfa_dialog_disable_content": "You will need to confirm your password or biometrics to turn off 2-step verification.", "tfa_dialog_disable_title": "Disable 2-step verification", "tfa_dialog_incorrect_password_content": "The password you've entered is incorrect. You have %1$@ attempts left before your account is locked.", "tfa_dialog_incorrect_password_title": "Incorrect password", "tfa_dialog_unsaved_changes_content": "Leaving this page will discard any changes you have made.", "tfa_dialog_unsaved_changes_title": "You have unsaved changes", "txtsummary": "Summary", "txt_calculating": "CALCULATING...", "txtquestioncount_001": "QUESTION %1$@ OF %2$@", "txtpacemanagementheader_myhealth_001": "View your health score", "txtpacemanagementsubtext_myhealth_001": "And get personalised tips on how to get healthier", "txtpacemanagementheader_activityes_002": "Get help setting your own target", "txtpacemanagementsubtext_activityes_002": "Choose your own status points target here", "chip_cashbacklite_amount_001": "Get up to 50% cashback", "gab_btncollapsebarcode_001": "Collapse barcode", "gab_btnexpandbarcode_001": "Expand barcode", "gab_txtfullname_001": "Full name", "gab_txtgymactivationbarcode_001": "Gym activation barcode", "gab_txtpartyid_001": "Party ID", "txtuptoxperc_cashbacklite_001": "GET UP TO %1$@ CASHBACK", "Total_cashbacklite_earned_001": "earned so far out of %1$@", "total_coinslite_earned_001": "coins earned so far out of %1$@", "gds_txt_stamping_title_001": "Ready to stamp", "gds_txt_stamping_desc_001": "Hold your phone against the stamp", "gds_txt_stamping_desc_002": "Points may not reflect immediately", "gds_txt_register_title_001": "We're activating your gym digital stamp", "gds_txt_register_title_002": "Gym digital stamp successfully activated", "gds_txt_register_desc_001": "Please wait patiently and do not close the app", "gds_txt_register_desc_002": "Check-in at a participating gym by getting your mobile phone stamped to earn 60 Vitality points.", "gds_btn_register_001": "Activate gym digital stamp", "gds_btn_register_002": "Gym digital stamp", "gds_txt_error_title_001": "Gym digital stamp could not activate", "gds_txt_error_desc_001": "Error occured. Please try again later", "appdevices_18_title": "Samsung Health", "appdevices_19_title": "Apple Health", "appdevices_39_title": "Google Fit", "appdevices_98_title": "Health Connect", "appdevices_98_subtitle": "<span class ='base900'>Tracks from a connected app</span>", "appdevices_18_subtitle": "<span class ='base900'>Tracks from mobile / wearable device</span>", "appdevices_39_subtitle": "<span class ='negative'> Discontinued from 1 Jul 2025</span>", "ExternalLinkReference": "www.google.com", "txt_feeds_assessments_key_001": "Assessments", "txt_feeds_prevention_key_001": "Prevention", "txt_feeds_healthcheck_key_001": "Health check", "expedia_apply_code": "Apply code", "expedia_booking_cancelled": "Cancelled", "expedia_booking_confirmed": "Confirmed", "expedia_booking_detail": "Booking detail", "expedia_booking_detail_blue_status": "Blue status", "expedia_booking_detail_booking_status": "Booking status", "expedia_booking_detail_bronze_status": "Bronze status", "expedia_booking_detail_date_booked": "Date booked", "expedia_booking_detail_discount_applied": "%1$@% discount applied", "expedia_booking_detail_discount_value": "Discount value", "expedia_booking_detail_gold_status": "Gold status", "expedia_booking_detail_hotel_stay": "Hotel stay", "expedia_booking_detail_itinerary_number": "Itinerary number", "expedia_booking_detail_no_status": "No status", "expedia_booking_detail_platinum_status": "Platinum status", "expedia_booking_detail_silver_status": "Silver status", "expedia_booking_history": "Booking history", "expedia_checkout": "Go to checkout", "expedia_code_copy": "Copy", "expedia_code_discount_code": "Discount code", "expedia_code_error_txt": "We're having trouble fetching your discount code. Please try again later or check your internet connection.", "expedia_code_expires": "Expires %1$@", "expedia_code_footnote_to_remember": "Remember to tap on “use a coupon, credit or promotion code” when you check-out on the Expedia website, and apply your discount code. \\nThis discount code will not work for Expedia app bookings.", "expedia_code_percent_discount": "%1$@% discount", "expedia_code_title": "Expedia", "expedia_code_to_redeem_reward": "Use this code to redeem your reward", "expedia_code_visit_website": "Visit website", "expedia_data_privacy_disagree_dialog_message": "<By disagreeing, you’ll be unable to proceed.>", "expedia_landing_available_until": "Available until <%1$@>", "expedia_landing_book_button": "Book on Expedia", "expedia_landing_bookings_per_year": "<%1$@> bookings available per membership year", "expedia_landing_discount_codes_used": "discount codes used", "expedia_landing_landing_tap_to_book": "Book through the Vitality app by tapping the button below", "expedia_landing_percent_discount": "%1$@% DISCOUNT", "expedia_landing_title": "Get discounted hotel bookings", "expedia_reserve_hotel": "Reserve hotel", "expedia_rewards_expedia_discount": "%1$@% Expedia discount", "expedia_rewards_expedia_nights_stay": "When you book %1$@ nights stay through the Vitality app", "expedia_you_saved_on_hotel": "You saved on hotel bookings! You will be able to enjoy your Expedia reward again in your new membership year.", "expedia_bookings_used": "<%1$@> out of %2$@ bookings used", "expedia_code_copied": "Code Copied", "txtexpedia_001": "Expedia", "txtdiscount_001": "%1$@% discount", "txtusethiscodetoredeem_001": "Use this code to redeem your reward", "txtremembertotaponusecoupon_001": "Remember to tap on “use a coupon, credit or promotion code” when you check-out on the Expedia website, and apply your discount code.", "txtdiscountcodewillnotworkfoxexpedia_001": "This discount code will not work for Expedia app bookings.", "txtbookonexpedia_001": "Book on Expedia", "txtcoins_002": "COINS", "txtgiftcard_002": "GIFT CARD", "txt_hf_AppExpFeedback_001": "App Experience Feedback", "txt_hf_GeneralFeedback_001": "General <PERSON>", "txt_hf_TechnicalFeedback_001": "Technical Feedback", "ctrl_btnviewdiscountcode_001": "View discount code", "txtreservehotel_001": "Reserve hotel", "txtgotocheckout_001": "Go to checkout", "txtapplycode_001": "Apply code", "txtthisweeksprogress_001": "This week's progress", "txtnopointsearnedthisweek_001": "No points earned this week", "txtouchid": "Touch id", "txfingerprint": "Finger print", "txtface": "Face", "txtfaceid": "Face id", "txtunknown": "Unknown", "gab_btnviewgymactivationbarcode_001": "View activation barcode", "txtstreaksandmilestones_001": "Streaks and milestones", "txtnew_001": "New", "txtgoalstreaks_001": "Goal streaks", "txtnomilestones_001": "No milestone\nachieved", "txtweeks_001": "weeks", "txtrecentmilestone_001": "recent milestone", "txtcurrentstreak_001": "current streak", "txt_participating_gyms": "Participating gyms", "txtpointsmaynotreflect_immediately_snv_001": "Your points may not reflect immediately. ", "txtpointsmaynotreflect_immediately_vhc_001": "Your points may not reflect immediately. Points will be allocated to the membership year that the activity was completed.", "txtphysicalactivitygoalstreaks_001": "Physical activity goal streaks", "txtmilestone_001": "milestone", "txtmilestones_001": "milestones", "txtachieved_001": "achieved", "txthowgoalstreakswork_001": "How goal streaks work", "txtnextmilestones_001": "Achieve %1$@ more weekly physical activity goals consecutively to reach your next milestone: a %2$@-week goal streak.", "txtallmilestones_001": "Well done! You have reached all of the milestones.", "txthitmilestones_001": "Achieve %1$@ more weekly physical activity goals to hit this milestone!", "txtreachedmilestones_001": "Congratulations!\nYou’ve reached a new milestone", "txtachievinggoalstreak_001": "Well done on achieving a %1$@-week goal streak.", "txtmilestones_002": "Share your milestone", "txtsharemilestones_001": "Activate your <span class='internalLinkPSmall'>physical activity goal</span> to start earning streaks!", "txtmilestonesachieved_001": "Milestone achieved:", "txtmilestoneheader_001": "week goal streak", "txtlongeststreak_001": "longest streak", "txtachievedstreakmilestones_001": "You achieved the\n%1$@ week goal streak milestone.", "txtcompletemilestones_001": "Complete %1$@ consecutive physical activity goals in a row", "txtbottomsheetcontent_001": "Achieve your weekly physical activity goals consecutively to earn goal streaks. If you miss a goal, your streak will end.\n\nYour longest goal streak is the highest number of consecutive goals that you achieve.\n\nUnlock and collect milestones based on your goal streaks.", "txtrewards2045": "<PERSON>arn 10 Medals", "txtrewards2046": "<PERSON><PERSON><PERSON> 5 Medals", "iconrewards2046": "solid coins", "iconrewards2045": "solid coins", "txtcouldntupdatepulldown_001": "Couldn’t update, pull down to refresh", "txt_archive_gift_cards": "Archive Gift Cards", "txt_archive_popup_description": "Manage your gift cards with the archive feature. Move your used cards to archive folder by clicking the \"Archive\" button.\nArchived items will remain in the archived gift cards section.\nStarbucks and Lawson Gift cards will be automatically archived once redeemed.", "vcrm_app_bar_title_001": "Data sharing or storage", "txt_showmore_001": "Show more", "txt_showless_001": "Show less", "txthealthycholesterol_intro_002": "There are steps you can take to better manage and reduce your cholesterol and in turn reduce your risk for heart disease.", "txthealthguidetipstext_005": "Continue to watch your weight to help maintain healthy cholesterol levels. Having a high weight is linked to poor cholesterol levels.", "txthealthycholesterol_intro_003": "It is important to find out your cholesterol numbers. If you have elevated cholesterol, you can take steps to manage it and reduce your risk of health problems.", "snv_ctrl_btnsubmitanotheractivity_001": "Add another", "snv_txtdelete_001": "Delete", "txt_heart_rate_condition_002": "%1$@+ min at %2$@%", "txtShortcutLinksHeading": "Take a shortcut", "txtifyoudonthavetheapp_001": "If you don't have the app, you'll be redirected to your device's app store to download it.", "ctrl_btnlogintotataaia_001": "Log in to <Tata AIA Consumer App> to continue", "txtenablesharingpermissions_001": "Enable sharing permissions", "txtdialogbodysharepermisionsinsettings_001": "In order to upload photos, allow <Vitality> to access photos in your device settings.", "ctrl_txtsgotosettings_001": "Go to Settings", "txtallowcameraaccess_001": "Allow camera access", "txtdialogbodycamearapermisionsinsettings_001": "In order to upload photos, allow <Vitality> to access to your camera in your device settings.", "txt_activity_date_for_membership_year": "Activity date is valid for current or previous membership year.", "txt_google_account_data_permission_001": "Your google account and data permissions are required to successfully link Vitality to Google Fit.", "txtlanguage_001": "Language", "old_wheel_ctrl_btnspinlater_001": "Spin later", "old_wheel_txt_doitlater_header_001": "Active challenge starts", "old_wheel_txt_doitlater_header_002": "Mon, %1$@", "old_wheel_txt_doitlater_title_001": "Get a weekly target", "old_wheel_txt_doitlater_title_002": "Get active", "old_wheel_txt_doitlater_title_003": "Get rewarded", "old_wheel_txt_doitlater_desc_001": "Achieve weekly physical activity goals to increase your current streak", "old_wheel_txt_doitlater_desc_002": "Reach streak targets to unlock and collect milestones", "old_wheel_txt_doitlater_desc_003": "Each week when you meet the target, you’ll receive a spin on the wheel for a chance to win a gift card from one of our Vitality Active Rewards partners.\n\nActivate your Pokemon GO reward to also win a Pokemon GO surprise item every week.", "vg_dc_challenge_not_started": "Challenge couldn't start. Please try again.", "vg_dc_commitment_period": "%1$@-month commitment period", "vg_dc_garmin_startchallenge_note": "Your Garmin will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.garmin.com' class='internalLinkFootnote'>Garmin online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.\n\n\nTap the “Start challenge” button within 60 days from the date of purchase. Once your device arrives, connect it to the Vitality app. Sync a workout to Vitality within 90 days from the date of purchase to activate your goal and start earning points and rewards for getting active.", "vg_dc_polar_startchallenge_note": "Your Polar will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.polar.com/za' class='internalLinkFootnote'>Polar online support</a>\nOnce your device arrives, connect it to the Vitality app to earn points and rewards for getting active.\n\n\nTap the “Start challenge” button within 60 days from the date of purchase. Once your device arrives, connect it to the Vitality app. Sync a workout to Vitality within 90 days from the date of purchase to activate your goal and start earning points and rewards for getting active.", "vg_dc_apple_startchallenge_note": "Your Apple Watch will be delivered soon. If you experience any issues with your device or delivery, contact <a href='https://www.apple.com' class='internalLinkFootnote'>Apple online support</a>.", "vg_dc_link_device_text": "Go to Health App", "vg_dc_startchallenge_idle_btn": "Start Challenge", "vg_dc_startchallenge_progress_btn": "Starting Challenge", "vg_dc_pending_page_header": "Monthly %1$@ cashback", "vg_dc_purchase_date_text": "Purchase date: %1$@", "vg_dc_coins_text": "GET UP TO %1$@ COINS", "ctrl_btnsave_001": "Save", "profile_language_txtheader_001": "Language selection", "profile_language_paragraph_001": "Select your preferred language for the app. Once you save the new selection, the Vitality app will restart automatically, and the new language will take effect when you return.", "txt_languageSelection_01": "Dutch", "txt_languageSelection_02": "English", "digital_stamp_txt_vitality_points_description": "Vitality points may not be awarded immediately. If your points take longer than 5 days to reflect please contact support.", "digital_stamp_txt_privacy_policy_description": "SLI may provide its partners with the member's personal data (including the name, the birth date, the gender, and the Vitality membership number) as needed, to allow the partners to confirm that the member's purchase of the product or the service is eligible for the partner's benefit. In addition, SLI may receive the information from its partners about the member's usage of the partner's benefit, to provide the member with the information about the SLI's operations, and to improve the quality of the member services.(*).Please refer to our official website <a href='https://www.sumitomolife.co.jp/privacy/' class='textLinkBlue breakoutLinkPMedium' >(https://www.sumitomolife.co.jp/privacy/)</a> for more details and latest information regarding our handling of personal information.(*) This includes disseminating advertisements and so forth according to customers’ needs based on the analysis of their transaction history, website visits history as well as information acquired from group companies etc.", "ctrl_txt_credential_breakout": "Market credentials breakout", "txt_contract_details_001": "Contract Details", "androidStoreUrl": "market://details?id=%1$@", "iosStoreUrl": "https://apps.apple.com/app/id%1$@", "txt_participating_gym_automatic_gym_note": "<Scan the code at your gym to sync with your linked health app and allow 24 hours for Vitality to add your points.>", "old_wheel_txt_earn": "Earn a", "old_wheel_txt_super_wheel_spin": "Super Wheelspin", "old_wheel_txt_this_week": "this week", "old_wheel_txt_getactive_title": "Get active", "old_wheel_txt_getactive_desc": "Complete your physical activities to reach your weekly points target.", "old_wheel_txt_reach_your_target_title": "Reach your target", "old_wheel_txt_reach_your_target_desc": "Achieve your active challenge goal to earn a Super Wheelspin.", "old_wheel_txt_rarn_super_wheelspin_title": "Earn a Super Wheelspin", "old_wheel_txt_rarn_super_wheelspin_desc": "You have <30 days> to spin the super wheel before it expires.", "txtgetyourinsurancediscount_001": "Get your insurance discount", "txtgetyourinsurancediscountcopy_001": "Start by unlocking your Bronze discount by reaching 2,000 points. <span class='internalLinkFootnote'>Learn more</span>", "old_wheel_txtsuperwheelspinsubtext_001": "Meet this week’s active challenge to earn a Super Wheelspin!", "inactive_membership_date_format": "dd <PERSON><PERSON> yyyy", "txtstartchallenge_001": "Start\nchallenge", "txt_linkdevice_001": "Link\ndevice and\nsync data", "vg_registration_pref_email_toggle_title": "Communication Preferences", "vg_registration_pref_email_toggle_description": "Email communication\nEnable email communication to get information about Vitality.\n", "device_cashback_effective_from_date_format": "dt_format_date_short_month_full_year", "device_cashback_effective_to_date_format": "dt_format_date_short_month_full_year", "partner_description_223": " ", "partner_description_2029": "アクティブチャレンジの週間目標を達成すると、『ポケモン GO』アプリ内で使用できる道具がゲットできます!", "partner_description_2147": "アクティブチャレンジの週間目標を達成すると、ファミマル ペットボトルお茶各種 各600mlを獲得できます。\n※Vitalityアプリのルーレット機能により、スターバックス、ローソン、ファミリーマートいずれかの商品を獲得できます。", "partner_description_2141": "週間目標の達成で獲得した各種ドリンクチケット等を寄付に交換することで、住友生命からあしなが育英会に寄付を行います。\nあしなが育英会は、病気や災害などで親を亡くした子どもたちや、障がいなどで親が十分に働けない家庭の子どもたちを、奨学金、教育支援、心のケアで支える団体です。", "partner_description_2069": "週間目標の達成で獲得した各種ドリンクチケット等を寄付に交換することで、住友生命から日本対がん協会に寄付を行います。\n日本対がん協会は「がんで苦しむ人や悲しむ人をなくしたい」を胸に、1958年から民間の立場で、がん予防やがん患者支援活動、正しい知識の普及啓発に取り組んでいます。", "partner_description_2143": "週間目標の達成で獲得した各種ドリンクチケット等を寄付に交換することで、住友生命から日本赤十字社に寄付を行います。\n日本赤十字社は、苦しんでいる人を救うため、災害で被災した方への医療救護や物資などの配付、復興支援等の活動を行っています。", "partner_description_2142": "週間目標の達成で獲得した各種ドリンクチケット等を寄付に交換することで、住友生命からWWF(世界自然保護基金）ジャパンに寄付を行います。\nWWFは、地球上の生物多様性の保全と、人の暮らしが自然環境や野生生物に与えている負荷の軽減を柱として活動しています。", "unable_to_link_user": "Unable to link user to %1$@", "pg_txtallocatedsurpriseitemtitle_044": "Surprise item", "old_wheel_ctrl_btnconfirm_001": "Confirm your ticket", "old_wheel_ctrl_btnvisitwebsite_001": "Show tickets (go to giftee site)", "vg_dc_aw_landing_previous_cycle_text": "For your previous cycle", "vg_dc_aw_landing_previous_cycle_body": "You are eligible to start a new <Active challenge> cycle.", "ctrl_btnuploadfile_002": "Upload file", "txt_login_biometric": "Biometrics", "txtgympartners_001": "Gym partners", "txtexerciseathome_001": "Exercise At Home", "txthealthscreening_001": "Health Screening", "txthealthyfood_001": "Healthy Food", "txthealthygear_001": "Healthy Gear", "txtfitnessdevices_001": "Fitness Devices", "txtyourdeviceishasbeendelivered_001": "Your device has been delivered. Connect it to the Vitality app via the Health app.", "txtyourfirstgoalstartson_001": "Once your device arrives, connect it to the Vitality app through the Health app to start earning points for tracking physical activity.", "tc_maximumteams_error_001": "<You’re participating in the maximum number of teams. To create a new team or join another, you will need to leave an existing team.>", "tc_sticker_history": "Sticker history", "tc_txtnodata_001": "No data", "tc_btngotit": "Got it", "pag_fitbit_reminder_dialog_title": "Connect your mobile phone to Fitbit", "pag_fitbit_reminder_dialog_body": "To track steps from your mobile phone, you must add your phone as a connection in the Fitbit app.", "pag_fitbit_reminder_dialog_button_do_no_show_again": "Do not show again", "txtupdateyouros_001": "Update needed", "txtupdateyouros_002": "Update required", "txtsoftosupdate_001": "Your current OS version is outdated. Update to %1$@ to enjoy the best  %2$@ experience.", "txtforcedosupdate_001": "Your current OS version is outdated and %1$@ can't run on it. Please update to %2$@ to continue using this app.", "ctrl_btnupdatelater_001": "Update later", "tc_team_members": "Team Members", "tc_extend": "Extend", "vj_format_date_compact": "dd <PERSON><PERSON> yyyy", "ha_metric_20_result_value_1": "Well-managed", "ha_metric_20_result_value_3": "Stressed", "urlReinstatedMem": "Pls click here to access rewards", "chip_sleep": "Sleep", "chip_meditation": "Meditation", "txtsupportedapporwearableapp": "Supported app or Wearable app", "txthealthconnect": "Health Connect", "txt_app_iphone_001": "Apple Health", "txt_app_android_001": "Samsung Health", "txt_device_001": "Fitbit", "txt_help_center_forms": "Forms", "onboarding_modalactivated_txtthebestway_001": "The best way to experience Vitality is by connecting your health app or wearable device to reward you for your healthy activities.\nConnection will take 2 minutes.", "onboarding_modalactivated_txtthebestway_002": "Link your wearable device to get rewarded for healthy activities. \nNo wearable device? Use %1$@ or %2$@ on your phone to track steps.\nConnection will take 2 minutes.", "pag_fitbit_not_install_dialog_title": "Looks like you do not have Fitbit installed", "pag_fitbit_not_install_dialog_body": "Connecting Fitbit will allow you to begin tracking your activity immediately and get started earning rewards.\nOnce you download and activate Fitbit, please return to Vitality to complete your account setup.", "pag_fitbit_not_install_dialog_button_skip": "SKIP", "pag_fitbit_not_install_dialog_button_download": "DOWNLOAD", "onboarding_modalactivated_txtthebestway_001_ios": "The best way to experience Vitality is by connecting your health app or wearable device to reward you for your healthy activities.\nConnection will take 2 minutes.", "vj_txt_bonus_001": "Bonus", "partner_description_2345": "Takealot is the leading ecommerce retailer in South Africa and one of the largest, most innovative ecommerce retailers on the African continent. Takealot gives you endless options in rewards so that you can select the reward of your choice when you earn a gift card by achieving your Vitality goals.", "txtgotohealthconnect": "Go to Health Connect", "file_extension_manipulated_error": "File extension manipulated", "file_format_recognise_error": "File format not recognised. Please try again", "file_upload_error": "Error in Uploading File", "file_unable_upload_error": "Unable to upload file", "txtconnectyourmobilephonetofitbit": "Connect your mobile phone to Fitbit", "txttrackstepsfromyourmobilephone": "To track steps from your mobile phone, you must add your phone as a connection in the Fitbit app.", "txtmaybelater": "Maybe later", "txtgotofitbit": "Go to Fitbit", "txtinsurername": "insurer name", "expedia_off_001": "%1$@%% OFF", "tc_chip_steps": "Steps", "status_detail_modal_date_format": "dt_format_date_short_month_full_year", "vj_connection_error": "Connection Error", "vj_cancel": "Cancel", "vj_try_again": "Try Again", "vj_error_message": "A connection error has occurred. Please make sure you're connected to a wifi or a cellular network and try again.", "teamchallenges_team_left_success": "Team Left successfully", "teamchallenges_team_deleted_success": "Team Deleted successfully", "txt_username_001": "Username", "txt_forgot_username_001": "Forgot username?", "txt_contact_us_001": "Contact us here.", "txt_privacy_policy_001": "Privacy policy is required", "txt_privacy_policy_cannot_continue_001": "You are unable to continue until you agree to the privacy policy.", "txt_terms_and_conditions_required_001": "Terms and Conditions are required", "txtsupportedapp_001": "Supported app", "txtsleeptrackingdevice_001": "Sleep tracking device", "txtinsurer_001": "<Insurer Vitality>", "txtfirstdaynextdaycheckingoal_002": "Come back tomorrow to view your tracked sleep.", "ha_section_Iconkey_121": "solid person-walking", "txt_youearnedyourfirstpoints_001": "You earned your first %1$@ points!", "txt_morewaystoearn_001": "More ways to earn are just around the corner.", "txt_heart_rate_condition_003": "%1$@ - %3$@ min at %2$@%", "dialog_hd_asg": "Share your data", "dialog_body_asg": "Allow your %1$@ app to share your sleep data with us. This way, we can track your progress and reward you. See instructions in ‘How it works’..", "txtlinkafitnessdevice_001": "Connect your device", "txtbodydeviceapplink_002": "Share your sleep data with us to achieve your weekly lifestyle goals and get rewarded.", "milestone_list_config_001": "2,5,7,10,20,50,75,100,200,300", "txtautotrackeddevice_001": "Automatically tracked through a supported device", "txtunlockthisreward_001": "Unlock this reward", "txtunlocktvitalityhealth_001": "Complete the Vitality Health Review to unlock this reward for your membership year.", "txtvitalityhealthreview_001": "Vitality Health Review", "txtcodesrevealed_001": "codes revealed", "txtincreaserewardstatus_001": "Increase reward status", "txtincreasecodevalue_001": "Increase code value", "txtgetamonthlycode_001": "Reveal code and use it", "txtdiscount15_001": "15% discount", "txtdiscount20_001": "20% discount", "txtdiscount25_001": "25% discount", "txtdiscount30_001": "30% discount", "txtsmartscale_001": "Smart scale", "txt_type_username_password_001": "Select login preference", "txt_select_login_pref_001": "Type in my username and password", "txtbminote_001": "BMI is a required result for your Health check. Points earned for Health check results are awarded once per membership period. \nYou can automatically sync your BMI result from your Smart scale.", "ctrl_update_001": "Update", "txt_bmi_condition_001": "First BMI result", "txt_bmi_condition_002": "BMI range 18.5 to 24.9", "txt_contactus_description_security": "Please note, for your security we are unable to reset your password over email. Please use the Change Password feature on the Your Account page of the JH Vitality website.", "txt_ourdetails_001": "Our details", "txt_telephone_001": "Telephone", "txt_footer_contactus_001": "While the John Hancock Vitality program is based on state-of-the-art science, it can’t replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "txt_contactus_availability_001": "Monday – Friday 8 AM EST – 6 PM EST", "txtadidas_001": "adidas", "txtrevealcodeby_001": "Reveal code by: %1$@", "txtcodereveal_001": "Code revealed: %1$@", "txtcouponcode_001": "Coupon code", "txtarchive_001": "Archive", "ctrl_btngotoadidas_001": "Go to adidas", "txtcopied_001": "<PERSON>pied", "txtyourcantbedisplayederror_001": "Your code can’t be displayed at this time.\nPlease try again later.", "ctrl_btngoback_001": "Go back", "txtadidasstatuscoupon_5": "15% off\nBlue Status coupon code", "txtadidasstatuscoupon_1": "20% off\nBronze Status coupon code", "txtadidasstatuscoupon_2": "25% off\nSliver Status coupon code", "txtadidasstatuscoupon_3": "30% off\nGold Status coupon code", "txtadidasstatuscoupon_4": "30% off\nPlatinum Status coupon code", "txtAdidasStatusRewardDisclaimerstatus_5": "Limited time offer- expires %1$@. Offer good for 15% off select full price articles at adidas.com/us. Enter code at checkout to apply discount up to $1,000. One user per customer. Offer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App. Valid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "txtAdidasStatusRewardDisclaimerstatus_4": "Limited time offer- expires %1$@. Offer good for 30% off select full price articles at adidas.com/us. Enter code at checkout to apply discount up to $1,000. One user per customer. Offer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App. Valid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "txtAdidasStatusRewardDisclaimerstatus_3": "Limited time offer- expires %1$@. Offer good for 30% off select full price articles at adidas.com/us. Enter code at checkout to apply discount up to $1,000. One user per customer. Offer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App. Valid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "txtAdidasStatusRewardDisclaimerstatus_2": "Limited time offer- expires %1$@. Offer good for 25% off select full price articles at adidas.com/us. Enter code at checkout to apply discount up to $1,000. One user per customer. Offer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App. Valid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "txtAdidasStatusRewardDisclaimerstatus_1": "Limited time offer- expires %1$@. Offer good for 20% off select full price articles at adidas.com/us. Enter code at checkout to apply discount up to $1,000. One user per customer. Offer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App. Valid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "txtweek_001": "week", "txt_vitalityid_001": "Vitality ID", "txtunsafedevice_detected_title_001": "Unsafe device detected", "txtunsafedevice_detected_description_001": "Your device appears to be unsafe, and so for security reasons %1$@ cannot be loaded.", "txtunsafedevice_detected_ok_001": "OK", "txt_btn_segment_picker_active_001": "Active", "txt_btn_segment_picker_archived_001": "Archived", "txtyourrevealedcodesswillappearhere_001": "Your revealed codes will display here.", "txtyoucurrentlyhavenoarchivedcodes_001": "You currently have no archived codes.", "ctrl_redeem_benefit": "Redeem benefit", "txt_prenuvolanding_footernote": "Disclaimer: The Prenuvo scan should not be considered as a replacement to any standard clinical guidelines-based screening for specific types of cancer or other health conditions. The Prenuvo scan is subject to the completion of an MRI safety assessment as well as a medical intake review by Prenuvo. Please contact your own healthcare provider with any specific questions regarding your health or medical conditions.\n\nAs with any medical test, there are limitations, which make it impossible for the Prenuvo scan to detect all malignancies and disease conditions. It is also possible that there may be findings that upon further diagnostic testing are not significant or might not require immediate medical intervention. Please see Prenuvo’s website for additional information about the scan.\n\nThere may be additional costs for diagnostic testing. You are fully responsible for any costs related to the scan or additional diagnostic testing. There is no coordination between <PERSON> and any health benefits you may receive from an insurance policy, health plan, or any other wellness programs you may be enrolled in.\n\n<PERSON> does not receive individual scan results from Prenuvo, nor does it know which customers have requested or taken the scan. For purposes of research, validation of assumptions and evaluation of future health and wellness offerings, <PERSON> may receive aggregated and deidentified data from Prenuvo from time to time. Your results will have no impact on your current life insurance coverage. Like other medical tests you elect, a decision to take the Prenuvo scan, and the results of the scan, may need to be disclosed in an application for insurance coverage, unless prohibited by law and thus could affect your access to future insurance coverage and the price of such coverage.\n\n<PERSON> is not an affiliate of Prenuvo. The Prenuvo scan is developed and administered by Prenuvo and its affiliated professional entities. <PERSON> does not provide medical advice or guidance on further diagnostic testing or treatment following the Prenuvo scan, is not involved in the design or administration of the Prenuvo scan and is not responsible for the accuracy or performance of the Prenuvo scan.\n\nAt this time, <PERSON> <PERSON> does not expect that it will be required to report the value of the Prenuvo test discount to the IRS as income to you. This material does not constitute tax or legal advice and neither <PERSON> <PERSON> nor any of its agents, employees or registered representatives are in the business of offering such advice. You should consult your own tax professional.\n\nDiscounted access to the Prenuvo scan through the John Hancock Vitality Program is not currently available in all states. Prenuvo offers limited locations for scans. Visit Prenuvo’s website for current locations. Discount does not apply to locations outside of the U.S or the Boston location as it's fully dedicated to a research study that doesn't allow any promotional pricing.\n\nVitality is the provider of the John Hancock Vitality Program in connection with policies issued by John Hancock. John Hancock Vitality Program rewards and discounts are available only to the person insured under the eligible life insurance policy, may vary based on the type of insurance policy purchased and the state where the policy was issued, are subject to change and are not guaranteed to remain the same for the life of the policy. The offer of discounted access to the Prenuvo scan is also subject to change.\n\nInsurance products issued by: John Hancock Life Insurance Company (U.S.A.), Boston, MA 02116.", "txt_completevhrtounlock_001": "Complete the Vitality Health Review to unlock", "txt_prenuvolanding_enabler_001": "SAVE $500 (20%) on SCAN cost", "txt_prenuvo_001": "Prenuvo", "txt_prenuvodiscount_001": "$500 (20%) off scan", "txt_usevitalityidtoredeem_001": "Use your Vitality ID to redeem your benefit", "txt_gotoprenuvo_001": "Go to Prenuvo", "txt_btn_segment_picker_available_001": "Available", "your_codes_001": "Your codes", "sharestreaks_details_001": "I’ve reached my %1$@-week physical activity goal streak! ", "btn_sharenow_001": "Share now", "txt_confirmbyuploadingsubmission_001": "Confirm that you’ve completed an activity by uploading your submission form as proof.", "txt_diabetes_support": "Diabetes support", "ActivityCategoryLabeltxt_85": "Health Check", "adidas_reward_landing_enabler_1": "UP TO 30% DISCOUNT", "adidas_reward_landing_enabler_2": "12 discount codes per membership year", "adidas_reward_landing_enabler_3": "Complete the Vitality Health Review to unlock", "sr_revealcode_partnername_1337": "adidas", "sr_revealcode_by_1337": "Reveal code by: %1$@", "sr_code_revealed_on_1337": "Code revealed: %1$@", "sr_revealcode_cta_subtext_1337": "Use this code to redeem your reward", "sr_revealcode_cta_text_1337": "Reveal code", "sr_revealcode_breakout_cta_text_1337": "Go to adidas", "sr_code_display_1337": "Discount code", "sr_code_copy_1337": "Copy", "sr_code_copied_1337": "<PERSON>pied", "sr_code_archive_1337": "Archive", "sr_code_archived_1337": "Archived", "sr_revealcode_discountdetail_5_1337": "15% off\nBlue Status coupon code", "sr_revealcode_discountdetail_1_1337": "20% off\nBronze Status coupon code", "sr_revealcode_discountdetail_2_1337": "25% off\nSliver Status coupon code", "sr_revealcode_discountdetail_3_1337": "30% off\nGold Status coupon code", "sr_revealcode_discountdetail_4_1337": "30% off\nPlatinum Status coupon code", "sr_revealcode_reward_disclaimer_5_1337": "Limited time offer- expires %1$@.\n \nOffer good for 15% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "sr_revealcode_reward_disclaimer_4_1337": "Limited time offer- expires %1$@.\n \nOffer good for 30% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "sr_revealcode_reward_disclaimer_3_1337": "Limited time offer- expires %1$@.\n \nOffer good for 30% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "sr_revealcode_reward_disclaimer_2_1337": "Limited time offer- expires %1$@.\n \nOffer good for 25% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "sr_revealcode_reward_disclaimer_1_1337": "Limited time offer- expires %1$@.\n \nOffer good for 20% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law.", "ActivityCategoryIconKey_85": "shared/common/icons/font_awesome/solid/syringe.jpg", "txt_learn_more_about_the_rules_001": "Learn more about the rules", "txt_about_this_reward_001": "Rules and policies", "ha_section_Iconkey_129": "solid person-walking", "txt_types_001": "Types", "txt_prenuvodiscount_instructions_001": "Once on Prenuvo site, click 'Request test' and follow instructions.", "rewardpartner_prenuvo_url": "https://prenuvo.com/lp/john-hancock-and-prenuvo", "txt_functionhealth_discount_001": "Access Special pricing on 100+ lab tests", "txt_functionhealth_001": "Function Health", "txt_functionhealthdiscount_instructions_001": "In addition to your Vitality ID, your special pricing code is: <span class='footnoteBold italic'>98FJHSGYTUIFQK</span>", "txt_gotofunctionhealth_001": "Go to Function Health", "rewardpartner_functionhealth_url": "https://www.functionhealth.com/c/john-hancock-vitality-members", "txt_completed_appare_1337": "You saved on adidas apparel! You will be able to enjoy adidas reward again in your new membership year.", "txtheader_YouAndVitality_001": "YOU & Vitality", "txtsubtext_YouAndVitality_001": "See your progress across all Vitality programs", "status_rewards_status_1_subtitle_1337": "20% discount", "status_rewards_status_1_title_1337": "Bronze", "status_rewards_status_2_subtitle_1337": "25% discount", "status_rewards_status_2_title_1337": "Silver", "status_rewards_status_3_subtitle_1337": "30% discount", "status_rewards_status_3_title_1337": "Gold", "status_rewards_status_4_subtitle_1337": "30% discount", "status_rewards_status_4_title_1337": "Platinum", "status_rewards_status_5_subtitle_1337": "15% discount", "status_rewards_status_5_title_1337": "Blue", "txt_nutrisenselanding_enabler_001": "SAVE $500 (20%) on SCAN cost", "txt_nutrisenselanding_footernote": "*compared to a 1-month no commitment membership, which retails at $320 as of April 2025.PLEASE NOTE: Services under the Nutrisense membership are provided by Nutrisense directly to John <PERSON> Vitality member. <PERSON> is not an affiliate of Nutrisense, is not responsible or liable for the Nutrisense services and does not receive individual data from Nutrisense with respect to any <PERSON> Vitality member in this program. For clarity:\n\n<PERSON> does not provide medical advice, is not involved in the services provided by Nutrisense and is not responsible for the accuracy or performance of such services. <PERSON> is not responsible for any issues, damages, or losses arising from the use of Nutrisense or other third-party technology, products, or services, including the continuous glucose monitors. The use of Nutrisense or any other third-party technology is at your own risk, and <PERSON> makes no warranties or representations, express or implied, regarding the functionality, reliability, or security of such technology. Any concerns or issues related to Nutrisense or third-party technology should be directed to the respective third-party provider.\n\nThere is no coordination between <PERSON> and any health benefits you may receive from an insurance policy, health plan, or any other wellness programs you may be enrolled in.\n\n<PERSON> does not receive individual data from Nutrisense,  nor does it know which customers have obtained Nutrisense membership. For purposes of research, validation of assumptions and evaluation of future health and wellness offerings, <PERSON> may receive aggregated and deidentified data from Nutrisense from time to time.\n\nNUTRISENSE IS NOT A DIAGNOSTIC OR MEDICAL SERVICE; NOT A 911 SERVICE: Nutrisense and their professional associates will not provide any information related to the diagnosis, cure, mitigation, prevention, or treatment of any disease or medical condition of the body. Nutrisenseâ€™s services are not a substitute for medical care, medical advice, and/or a detailed discussion with your primary care physician or other licensed provider. If you desire such information, please consult your healthcare provider. Please see Nutrisense's website for additional information. If you are having a medical emergency, please contact 911 or your health care professional.\n\nDISCOUNTED PROGRAM ELIGIBILITY: The Nutrisense program described above is offered at the specified rates only to registered John Hancock Vitality PLUS members who have completed the Vitality Health Review (VHR) for the current program year. Eligibility is also subject to Nutrisenseâ€™s own requirements and is subject to change. Nutrisense only offers its membership to individuals in the United States. Access to the Nutrisense discount through the John Hancock Vitality Program is not currently available for policies issued in Guam, New Mexico, New York, North Dakota, Puerto Rico, and Vermont. At this time, John Hancock does not expect that it will be required to report the value of the Nutrisense membership discount to the IRS as income to the insured. This material does not constitute tax or legal advice and neither John Hancock nor any of its agents, employees or registered representatives are in the business of offering such advice. You should consult Your own tax professional. PLEASE NOTE: The offer of discounted access to the Nutrisense membership is subject to change.", "txt_functionhealth_enabler_001": "SAVE $100 on your first year", "txt_functionhealth_footernote": "1. Access to special pricing is only applicable to the first-year membership fee. At the end of the first year of membership, fees for a renewal of your membership will automatically be charged to the credit card on file with Function Health unless the member cancels the membership directly with Function.\\n\\n2. Your results will have no impact on your current life insurance coverage. That said, like other medical tests you elect, a decision to take one or more lab tests made available through Function Health, and the results of those lab tests, may need to be disclosed in an application for insurance coverage, unless prohibited by law, and thus could affect your access to future insurance coverage and the price of such coverage.\\n\\nFunction Health does not offer medical advice, laboratory services, a diagnosis, medical treatment, or any form of medical opinion, through their services or otherwise. Function Health’s services are not a substitute for medical care, medical advice, and/or a detailed discussion with your primary care physician or other licensed provider. All laboratory and medical services are provided by independent third parties of Function Health. If you have any questions regarding any laboratory results or other information that you access through Function Health, you should discuss those questions with a primary care physician or other licensed provider. Please see Function Health's website for additional information.\\n\\nThere may be additional costs for diagnostic testing. Function Health offers access to add-on tests not covered by the basic membership fee. There is no coordination between <PERSON> and any health benefits you may receive from an insurance policy, health plan, or any other wellness programs you may be enrolled in.\\n\\n<PERSON>oh<PERSON> does not receive individual results from Function Health, nor does it know which customers have taken the lab tests. For purposes of research, validation of assumptions and evaluation of future health and wellness offerings, <PERSON> may receive aggregated and deidentified data from Function Health from time to time.\\n\\nJohn Hancock is not an affiliate of Function Health. Services related to the lab tests are administered by Function Health and its third-party providers. John Hancock does not provide medical advice, is not involved in the services provided by Function Health and is not responsible for the accuracy or performance of such services.\\n\\nAccess to special pricing for the Function Health membership through the John Hancock Vitality Program is not currently available for policies issued in Guam, New Mexico, New York and Puerto Rico. Lab visits through Function Health are not currently available in Hawaii, Rhode Island, and Puerto Rico. In New York and New Jersey, Quest Diagnostics requires an additional estimated $500 annual lab test charge payable directly to the laboratory at time of service.\\n\\nAccess to special pricing for the Function Health membership is only available to registered John Hancock Vitality PLUS members who have completed the Vitality Health Review (VHR) for the current program year. Must be 18 years or older. Function Health requires members to have an active US address and phone number, and to accept Function Health's Terms of Service.\\n\\nThe access to special pricing towards the Function Health membership is subject to change.\\n\\nAt this time, John Hancock does not expect that it will be required to report the value of the Function Health membership subsidy to the IRS as income to the insured. This material does not constitute tax or legal advice and neither John Hancock nor any of its agents, employees or registered representatives are in the business of offering such advice. You should consult your own tax professional.\\n\\nVitality is the provider of the John Hancock Vitality Program in connection with policies issued by John Hancock. John Hancock Vitality Program rewards and discounts are available only to the person insured under the eligible life insurance policy, may vary based on the type of insurance policy purchased and the state where the policy was issued, are subject to change and are not guaranteed to remain the same for the life of the policy. To be eligible to earn rewards and discounts by participating in the Vitality program, the insured must register for Vitality and in most instances also complete the Vitality Health Review (VHR).\\n\\nInsurance products issued by: John Hancock Life Insurance Company (U.S.A.), Boston, MA 02116.", "txt_nutrisense_redeem_weblink_001": "https://www.nutrisense.io/go/john-hancock?promoCode=VitalityAccess2025", "txt_maintain_your_healthy_lifestyle_001": "Maintain your healthy lifestyle", "txt_health_priority_grey_block_001": "Great work! All your health metrics are within the target range. Continue your healthy habits to enjoy more years of good health.", "txt_health_priority_intro_004": "The most important thing you can do for your health is to maintain your healthy lifestyle and complete your preventive screenings regularly.", "txt_tip_body_001": "Focus on a whole food diet. Choose unprocessed foods and continue to eat a variety of vegetables, wholegrains, nuts and seeds.", "txt_tip_body_002": "Track your workouts to maintain a healthy balance of cardio and strength exercises.", "txt_tip_body_003": "Dedicate time to sustain your healthy mental wellbeing by practicing mindfulness or enjoying a hobby.", "partner_description_113600": "This reward is not applicable for Takaful membership holders\n\nGet 50% discount on your next movie!\n\neasytickets lets you buy movie tickets in their partner cinema in the simplest, easiest and the smartest way without the unnecessary glitz. Search, choose and buy movie tickets. It can’t get easier than this.\n\neasytickets reaches around 100 screens in the 4 major cities. easytickets also offers information about upcoming movies, show timings, movie trailers & reviews. On achieving your weekly target, you can choose easytickets as your reward coupon and get to watch a movie for a 50% discount. In fact, If you work hard on your weekly targets for two consecutive weeks, then your loved one / friend / partner will also be able to watch the movie for 50% discount. Book your tickets in advance and watch the next block buster movie for a 50% discount. Enjoy!", "archived_codes_001": "Archived codes", "txt_no_archived_codes_for_year_001": "You have no codes available for your current membership year.", "txt_no_archived_codes_for_year_copy_001": "You will be able to claim new codes in your next membership year", "txt_you_currently_have_no_codes_001": "You currently have no codes", "txt_you_currently_have_no_codes_copy_001": "Your codes will appear here.", "txt_no_archived_codes_001": "You have no archived codes.", "txt_no_archived_codes_copy_001": "Your archived codes will appear here.", "txt_oura_enabler_001": "Save %1$@ off Oura Ring", "txt_oura_footernote": "Oura Membership required for access to all features - 1 month included for new members, $5.99 USD/month afterwards - and to earn Vitality Points with the Oura Ring. The Oura Membership renews automatically each month until canceled.\n\nThe Oura Ring offer cannot be combined with other offers from Oura. Oura Ring products and services are not medical devices, and are not intended to mitigate, prevent, treat, cure or diagnose any disease or condition. If you have any concerns about your health, please consult your doctor.\n\nVitality members can earn Vitality Points for Meditation and Healthy Sleep with the Oura Ring. iOS users can also earn Vitality Points for workouts through heart rate, steps and Active Calories.", "rewardpartner_oura_discount_url": "https://ouraring.com/discount/%1$@?utm_source=johnh<PERSON><PERSON>&utm_medium=partner", "txt_activefitdirect_001": "Active&Fit Direct", "txt_afd_enabler_001": "Access 1,000s of gyms for $28/month with $0 enrollment", "txt_afd_footernote": "The Active&Fit Direct benefit is not available in Puerto Rico or Guam.\n\n*$28 enrollment fees waived for standard and premium gyms 10/1/24 12:01 a.m. - 11/30/24 11:59 p.m. PT.\n\nCosts for premium exercise studios exceed $28/mo. plus applicable enrollment fees and taxes. Fees vary based on premium fitness studios selected.\n\nThe Active&Fit Direct™ program is provided by American Specialty Health Fitness, Inc., a subsidiary of ASH. Active&Fit Direct and the Active&Fit Direct logos are trademarks of ASH and used with permission herein. Other names or logos may be trademarks of their respective owners. Standard fitness center and premium studio participation varies by location and is subject to change. On-demand workout videos are subject to change. ASH reserves the right to modify any aspect of the Active&Fit Direct Program (including, without limitation, the Enrollment Fee(s), the Monthly Fee(s), any future Annual Maintenance fees, and/or the Introductory Period) at any time per the terms and conditions. If ASH modifies a fee or makes a material change to the Active&Fit Direct Program, ASH will provide you with less than 30 days’ notice prior to the effective date of the change. ASH may discontinue the Program at any time upon advance written notice.", "txtwehavealittleproblemcopy_001": "Please note you can't use a space at the beginning or end of your password.", "txtwehavealittleproblem_001": "We have a little problem", "ActivityCategoryLabeltxt_87": "Safe Driving", "ActivityCategoryIconKey_87": "shared/common/icons/font_awesome/solid/car.jpg", "txt_afd_redeem_weblink_001": "https://www.activeandfitdirect.com/fitness/AF400157JO", "sr_archive_item_1337": "Archive item", "sr_view_terms_1_1337": "View ", "sr_view_terms_2_1337": "T’s & C’s", "sr_terms_condition_1337": "Terms and Conditions", "txt_reward_code_copied": "Code copied", "txtcodesuccessfullyarchived_001": "Code successfully archived", "txtcodessuccessfullyarchived_001": "Codes successfully archived", "txtSafeDriving_001": "Safe driving", "txtsubmitproof_002": "Submit proof", "sd_submitProof_text_001": "Verify your safe driving status with the Allstate Drivewise program by submitting proof.", "txtDateNote_001": "Your activity must have been completed in the last 90 days", "txtPrenatalCare_001": "Prenatal care", "txtsubmitproof_003": "Submit completed form and one of the two supporting documents", "txtdownloadform_001": "Download Prenatal Care form", "pc_submitProof_text_001": "Confirm that you've completed your prenatal care by uploading completed form and one of the supporting documents.", "sr_code_detail_discount_detail_1337": "%1$@% off\ncoupon code", "sr_code_detail_dynamic_terms_1337": "Limited time offer- expires %1$@.\n \nOffer good for %2$@% off select full price articles at adidas.com/us.\n \nEnter code at checkout to apply discount up to $1,000. One user per customer.\n \nOffer cannot be combined with other offers. Offer is not valid on prior purchases. Exclusions apply, including but not limited to: 4D, Select Ultraboost, Pharrell x adidas, Disney, Marvel Footwear, adizero adios pro, Human Made, Select Outdoor including five ten footwear, freehikers, cold rdy apparel, limited edition Originals including Select Stan Smith, Select Superstar, Select Campus, Select Gazelle, Select Samba, Sale, Gift Cards and purchases on the Confirmed App.\n \nValid on domestic U.S. orders only. adidas reserves the right to change terms and conditions, substitute offer of equal or greater value and end offer at any time without notice. Void where prohibited or restricted by law. ", "txt_whoop_footernote": "Vitality is the provider of the John Hancock Vitality Program in connection with policies issued by <PERSON>. John <PERSON> Vitality Program rewards and discounts are available only to the person insured under the eligible life insurance policy, may vary based on the type of insurance policy purchased and the state where the policy was issued, are subject to change and are not guaranteed to remain the same for the life of the policy. \nTo be eligible to earn rewards and discounts by participating in the Vitality program,  the insured must register for Vitality and in most instances also complete the Vitality Healthy Review (VHR). \nWHOOP is not a sponsor of the John Hancock Vitality Program or otherwise affiliated with John Hancock or Vitality. The logos and other identifying marks attached are trademarks of and owned by WHOOP.\nYou must have an up-to-date WHOOP membership and use an iOS device to earn Vitality points with WHOOP. The Whoop discount through the John Hancock Vitality Program is not currently available in New York. All WHOOP memberships automatically renew as an annual membership at the then current price. Additional fees for shipping WHOOP band or apparel and accessories and tax are dependent on location. Free WHOOP trial memberships are not available in Puerto Rico or Guam.", "txt_doyouhavewhoopmembership_001": "Do you have an existing WHOOP membership?", "txt_pleaseletusknow_001": "Please let us know so we can set things up correctly.", "txt_iamnewtowhoop_001": "I am new to WHOOP", "txt_ihaveexistingwhoop_001": "I have an existing WHOOP membership", "txt_proceed_001": "Proceed", "txt_usepromojohnhancock_001": "Use the promo-code JOHNHANCOCK", "txt_usethiscodeatcheckout_001": "Use this code at checkout to receive a 30% discount on your membership renewal", "imagepath_dms_whoopdevice": "shared/images/journey/Whoop/Whoop.png", "txt_highlightfeatures_001": "Highlight features:", "txt_learnmoreaboutwhoop_001": "Learn more about WHOOP", "txt_redeemwhoopmembership_001": "Redeem WHOOP membership", "txt_whoop_feature_001": "14+ day battery", "txt_whoop_feature_002": "Sleep, Strain, & Recovery insights", "txt_whoop_feature_003": "V02 Max & heart rate zones", "txt_whoop_feature_004": "Women's hormonal insights", "txt_whoop_feature_005": "Additional features depending on membership tier from real-time stress monitor to on-demand AFib detection", "txt_whoopmembership_001": "WHOOP MEMBERSHIP", "weblink_learnmore_whoop": "https://shop.whoop.com/en-us/index.html", "weblink_redeemmembership_whoop": "https://join.whoop.com/us/en/johnhancock", "weblink_renewmembership_whoop": "https://app.whoop.com/membership/extensions/?pc=JOHNHANCOCK", "txt_burned_calories_details_001": "Earn points for calories burned during your workout. \\nWorkout calories are recorded by compatible fitness devices and apps other than Apple watch.", "txt_proofnote_sd_001": " We’ll only accept the following proof:", "txt_proofnote_sd_002": "Allstate Drivewise cash back reward email or", "txt_proofnote_sd_003": "a recent bill from Allstate showing your safe driving discount", "txt_proofnote_pc_001": "We’ll only accept the following proof to confirm you’ve completed your prenatal care:", "txt_proofnote_pc_002": "completed Prenatal Care form and", "txt_proofnote_pc_003": "one of the following supporting documents:\n-an Explanation of Benefits (EOB) indicating the\ndates and services rendered for prenatal visits\n-an official record from your physician’s office verifying check ups", "txt_proofnote_pc_004": "-an Explanation of Benefits (EOB) indicating theâ€¨ dates and services rendered for prenatal visits", "txt_proofnote_pc_005": "-an official record from your physician's office verifying check ups", "txt_diabetes_hba1c_001": "HbA1c (A1c)", "txtproofnote_pc_001": "Weâ€™ll only accept the following proof to confirm youâ€™ve completed your prenatal care:", "txt_grail_enabler_001": "SAVE %1$@ ON TEST COST", "txt_grail_footernote": "Galleri is a blood test from Grail designed to screen for cancer. The Galleri test does not detect all cancers nor does it diagnose cancer. A test result of â€œCancer Signal Detectedâ€ requires confirmatory diagnostic evaluation by medically established procedures (e.g., imaging) to confirm cancer. There may be additional costs for such diagnostic testing. Rx only. It is intended to be used in addition to, and not replace, other cancer screening tests recommended by a healthcare provider. A negative test result does not rule out cancer and in particular the sensitivity rate for detecting cancer in the early stages is lower than for later stages. A false positive or false negative result can occur and results should be discussed with a healthcare provider. Test eligibility is subject to certain qualification requirements, including age and certain health risk factors. There is no coordination between any health benefits you may receive from an insurance policy, health plan, or any other wellness programs you may be enrolled in.\n<PERSON> is not an affiliate of GRAIL. The Galleri test is manufactured and distributed by GRAIL. <PERSON> does not provide medical advice, is not involved in the design or manufacture of the Galleri test and is not responsible for the accuracy or performance of the Galleri test. Galleri is not a test to confirm or rule out genetic or other conditions that may indicate a predisposition to cancer. The Galleri test is currently not approved by the U.S. Food and Drug Administration.\nLike other medical tests you elect, a decision to take the <PERSON><PERSON><PERSON> test, and the results of the test, may need to be disclosed in an application for insurance coverage, and so could affect your access to future insurance coverage and the price of such coverage.", "txt_grail_age_eligible_001": "Only members over 40 years old are eligible", "txt_grail_001": "Multi-cancer early detection test", "txt_graildiscount_001": "%1$@ off test cost", "txt_accessid_001": "Access ID", "txt_useaccessidtoredeem_001": "Use this unique Access ID redeem your benefit", "txt_graildiscount_instructions_001": "Once on Galleri site click “Request test” and follow instructions.", "txt_gotograil_001": "Go to Galleri", "rewardpartner_grail_url": "https://www.galleri.com/jhvaccess?utm_source=jh-vitality&utm_medium=website&utm_content=jhvaccess-vitality-website-link&utm_campaign=life-insurance-john-hancock-vitality-jul23", "txt_grail_freeTest_001": "Free test", "ActivityCategoryLabeltxt_93": "Healthy Food", "ActivityCategoryIconKey_93": "shared/common/icons/font_awesome/solid/apple-whole.jpg", "txthealthprioritygreyblock_001": "Great work! All your health metrics are within the target range. Continue your healthy habits to enjoy more years of good health.", "txthealthpriorityintro_004": "The most important thing you can do for your health is to maintain your healthy lifestyle and complete your preventive screenings regularly.", "txttipbody_001": "Focus on a whole food diet. Choose unprocessed foods and continue to eat a variety of vegetables, wholegrains, nuts and seeds.", "txttipbody_002": "Track your workouts to maintain a healthy balance of cardio and strength exercises.", "txttipbody_003": "Dedicate time to sustain your healthy mental wellbeing by practicing mindfulness or enjoying a hobby.", "hc_text_participating_partners": "Get one free Vitality health check per year at any of these participating partners.", "hc_text_schedule_screening": "Schedule screening", "hc_text_reperio": "Reperio", "hc_text_quest_diagnostics": "Quest Diagnostics", "earn_points_active_calories_001": "Based on personal target(light workout)", "earn_points_active_calories_002": "Based on personal target(standard workout)", "earn_points_active_calories_003": "Based on personal target(advance workout)", "earn_points_active_calories_details": "Active calories are the calories you burn throughout the day, not just during a workout.\\n\\nShare your Active Energy and weight data with us to get a personalised target and start earning points.", "sr_revealcode_partner_store_url_1337": "https://www.adidas.com/us", "dt_format_short_day_month_full_year": "dd/MM/yyyy", "vna_expedia_contentnote": "It may take up to 10 minutes to reflect a recent booking, and up to a week to reflect a cancelled booking.", "txt_onboarding_reward_imagepath_freemium": "shared/images/journey/onboarding/onboarding_tutorial_screens/mainImage5_giftbox_coins.png", "txt_onboarding3_header_freemium": "The higher your status, the greater the <span class='primary h2 medium'>cashback on your premium</span>.", "txt_onboarding3_subtext_freemium": "Earn up to X annual cashback on your insurance premium.", "txt_onboarding4_header_freemium": "Stay motivated with personalised <span class='primary h2 medium'>goals</span>.", "txt_onboarding4_subtext_freemium": "Points earned from physical activity will contribute toward your daily, weekly and monthly goals.\n\nAnd every week, choose a lifestyle goal to form healthy habits.", "txt_onboarding5_header_freemium": "Achieve your weekly goals to earn <span class='primary h2 medium'>Credits</span> to spend in the <span class='primary h2 medium'>Touch webshop</span>", "txt_onboarding5_subtext_freemium": "Spend your credits on a variety of available rewards", "txtbronze_subtext_001_freemium": "0% discount on premium", "txtsilver_subtext_001_freemium": "5% discount on premium", "txtgold_subtext_001_freemium": "10% discount on premium", "txtglucose_intro_004": "Having a healthy blood glucose value is important. This number helps determine risk for prediabetes, diabetes, and other related conditions. You can improve your blood sugar and its risks through lifestyle changes, which makes it especially important to know your health results. Speak with your healthcare provider to find out more.", "txt_check_in_at_the_gym": "Check-in at the gym", "txt_manually_submit_a_workout": "Manually submit a workout", "txt_submit_workout": "Submit workout", "txt_gym_check_in": "Gym check-in", "txt_steps_description": "The longer you walk, the more points you'll earn. Calculate your steps by multiplying the number of miles you walked by 2,000.", "earn_points_steps_001": "4,000 steps (light workout)", "earn_points_steps_002": "8,000 steps (standard workout)", "earn_points_steps_003": "12,000 steps (advanced workout)", "earn_points_heart_rate_001": "10 min at 60% (light workout)", "earn_points_heart_rate_002": "20 min at 60% (standard workout)", "earn_points_heart_rate_003": "30 min at 60% (advanced workout)", "earn_points_workout_calories_001": "50 calories (light workout)", "earn_points_workout_calories_002": "100 calories (standard workout)", "earn_points_workout_calories_003": "150 calories (advanced workout)", "txtplatinum_subtext_001_freemium": "15% discount on premium", "txt_headspace_enabler_001": "FREE %1$@ year subscription to Headspace", "txt_headspace_footernote": "The John Hancock Vitality Program collects individual usage data for program evaluation. The data is considered confidential PII and use is governed by the Headspace Privacy Policy. By signing up for Headspace, you agree to share your data with the John Hancock Vitality Program.", "txt_bookinglastupdatedon_001": "Booking last updated on", "txt_payoutdate_enabler_001": "1 YEAR FREE MEMBERSHIP", "txt_maintainplatinum_001": "Maintain Platinum status for 3 years in a row", "txt_streakprogress_001": "Streak progress", "txt_achieveplatinumvhr_001": "Achieve Platinum Vitality status and complete your Vitality health review for 3 years in a row to unlock this reward.", "txt_maintainplatinum_vhr_001": "Maintain your Platinum status and health review streaks to keep earning a new reward each year.", "txt_yearofstreak_001": "Year %1$@ of streak %2$@", "txt_free_oneyearmembership_001": "Free one-year membership", "txt_submit_a_photo_of_your_workout": "Submit a photo of  your workout.", "txt_at_home_workout": "At home workout (standard  workout)", "txt_manually_submit_a_workout_title": "Manually submit a workout", "txt_manually_submit_a_workout_desc": "At home workouts or a gym workouts which are not recorded using an app or device require submitting proof manually", "vhc_alt_header_txtpoints_001": "%1$@ POINTS MAX", "vhc_alt_submenu_txtpoints_001": "UP TO %1$@ POINTS", "vhc_txt_max_points_for_submission_type_001": "%1$@ points max for %2$@ per %3$@", "vhc_txt_max_points_earn_for_activity": "Maximum status points earned from activities", "vhc_txt_max_points_earn_for_health_check_submission": "Maximum points earned for health check submissions", "headspace_breakout_url_01": "https://work.headspace.com/johnhancockvitality/join", "txtunlockthisreward_002": "Unlock this reward", "txtactivateyourhealthyfood_001": "Activate your HealthyFood program by ordering your Healthy Savings card", "txt_hf_appexperiencefeedback_001": "App Experience Feedback", "txt_hf_autoapplied_001": "Automatically applied at checkout", "txt_hf_cashbabck_calculated_001": "Cashback will be calculated at the end of the month.", "txt_hf_discount_001": "Get 5% discount on qualifying items", "txt_hf_enabler_001": "up to $50 in savings per month", "txt_hf_footernote": "While the John Hancock Vitality program is based on state-of-the-art science, it can't replace the advice of a healthcare provider. We encourage you to speak with a provider about your health results. We value your privacy and will keep your information secure.", "txt_rep_enabler_001": "Get free at-home health screening kit", "txt_que_enabler_001": "Schedule free in-person health screening", "ActivityCategoryLabeltxt_96": "HealthyMind", "ActivityCategoryIconKey_96": "shared/common/icons/font_awesome/solid/apple-whole.jpg", "ctrl_btnactivateprogram_001": "Activate program", "jh_hc_cotinine_earn_points": "Earn %1$@ points for a negative test result"}