# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.148] - 2025-08-20

* [SFSTRY0118720][<PERSON><PERSON><PERSON>][Points History - Navigation to points history is showing blank screen ] (#165)
* [SFSTRY0118720][<PERSON><PERSON><PERSON>][Points History - Navigation to points history is showing blank screen ]

* [SFSTRY0118720][<PERSON><PERSON>nberg][Points History - Navigation to points history is showing blank screen ] 

## [v0.0.147] - 2025-08-06

* [SFSTRY0117758][<PERSON><PERSON><PERSON>][Points History - Apply filter functionality is failing] (#164)
* [SFSTRY0117758][<PERSON><PERSON><PERSON>][Points History - pply filter functionality is failing]

* [SFSTRY0117758][<PERSON><PERSON><PERSON>][Points History - Apply filter functionality is failing] 

## [v0.0.146] - 2025-07-29

* [SFSTRY0117084][G<PERSON>nberg][Points History - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] (#163) 

## [v0.0.145] - 2025-07-24

* [SFSTRY0113090][<PERSON><PERSON><PERSON>][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2] (#162) 

## [v0.0.144] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1][Health sdk] (#161) 

## [v0.0.143] - 2025-07-23

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadStateException on PointsHistoryGraphCubit 

## [v0.0.142] - 2025-07-23

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] (#159)
* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1][Health sdk] 

## [v0.0.141] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#157)
* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check]

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] 

## [v0.0.140] - 2025-06-04

* [SFSTRY0107635] - [DevOps] Validate Quality Gates for PR Validation on most stable Module Apps. 

## [v0.0.139] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.139-alpha+1] - 2025-05-29

* [SFSTRY0107635][DevOps] Validate Quality Gates for PR Validation on most stable Module Apps. 

## [v0.0.138] - 2025-05-22

* [SFSTRY0110952][Gutenberg][Points History -[T-104][GTB-DW] Points history section in Profile has too much side padding] (#153) 

## [v0.0.137] - 2025-05-20

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed cache flow and done cleanup] (#151)
* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed provision of PointsEntry]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed provision of PointsEntry]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed cache flow and done cleanup]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Removed stale code and updated podfile ]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixing iOS files]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixing iOS files]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Temp global model ref]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Cleanup and test case fixed]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Cleanup and test case fixed]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Optimized the flow of stream listen, removed unnecessary checks]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Merged 1 commit]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Resolved imports]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Resolved imports]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization ( streaming model)][Fixed test cases]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Fixed reported issues by Gaurav]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Fixed filter issue]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Bumped up the sdk version]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Updated version] 

## [v0.0.136] - 2025-05-05

* [SFSTRY0109316] - Support additional points categories for jh (#152)
* [SFSTRY0109316] - Support additional points categories for jh

* [SFSTRY0109316] - Support additional points categories for jh 

## [v0.0.135] - 2025-04-14

* [DFCT0045449] - module login issue fixed 

## [v0.0.134] - 2025-04-10

* [SFSTRY0108702][Gutenberg][Points History - Points History Monthly wise drop down is not resetting to default when applied filters are cleared] (#149) 

## [v0.0.133] - 2025-03-27

* [SFSTRY0106361] - As the DevOps team requested I am raising a new PR again because the journey pipeline is failing due to my previous PR commit message containing an apostrophe in members (#148)
Co-authored-by: p01sumalaths <<EMAIL>> 

## [v0.0.132] - 2025-03-26

* [SFSTRY0106361] - The member's points history in the app does not display all months 

## [v0.0.131] - 2025-03-20

* [SFSTRY0104177] - Publishing the sdk flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.130] - 2025-03-20

* [SFSTRY0105932][Gutenberg][Points History - [FLTUpgrade] Journey module updates related to flutter 3.24.5 upgrade] (#146) 

## [v0.0.129] - 2025-03-06

* [SFSTRY0104889][Gutenberg][Points History - UI New Changes:

## [v0.0.148] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] (#165)
* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ]

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v0.0.147] - 2025-08-06

* [SFSTRY0117758][Gutenberg][Points History - Apply filter functionality is failing] (#164)
* [SFSTRY0117758][Gutenberg][Points History - pply filter functionality is failing]

* [SFSTRY0117758][Gutenberg][Points History - Apply filter functionality is failing] 

## [v0.0.146] - 2025-07-29

* [SFSTRY0117084][Gutenberg][Points History - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] (#163) 

## [v0.0.145] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2] (#162) 

## [v0.0.144] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1][Health sdk] (#161) 

## [v0.0.143] - 2025-07-23

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadStateException on PointsHistoryGraphCubit 

## [v0.0.142] - 2025-07-23

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] (#159)
* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1][Health sdk] 

## [v0.0.141] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#157)
* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check]

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] 

## [v0.0.140] - 2025-06-04

* [SFSTRY0107635] - [DevOps] Validate Quality Gates for PR Validation on most stable Module Apps. 

## [v0.0.139] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.139-alpha+1] - 2025-05-29

* [SFSTRY0107635][DevOps] Validate Quality Gates for PR Validation on most stable Module Apps. 

## [v0.0.138] - 2025-05-22

* [SFSTRY0110952][Gutenberg][Points History -[T-104][GTB-DW] Points history section in Profile has too much side padding] (#153) 

## [v0.0.137] - 2025-05-20

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed cache flow and done cleanup] (#151)
* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed provision of PointsEntry]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed provision of PointsEntry]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixed cache flow and done cleanup]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Removed stale code and updated podfile ]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixing iOS files]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Fixing iOS files]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Temp global model ref]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Cleanup and test case fixed]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Cleanup and test case fixed]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Optimized the flow of stream listen, removed unnecessary checks]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Merged 1 commit]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Resolved imports]

* [SFSTRY0107975][Gutenberg][Points History - Cached data manipulation to fetch the latest data and optimizing the performance][Resolved imports]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization ( streaming model)][Fixed test cases]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Fixed reported issues by Gaurav]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Fixed filter issue]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Bumped up the sdk version]

* [SFSTRY0107973][Gutenberg][Points History - Fetching data at the time of module initialization][Updated version] 

## [v0.0.136] - 2025-05-05

* [SFSTRY0109316] - Support additional points categories for jh (#152)
* [SFSTRY0109316] - Support additional points categories for jh

* [SFSTRY0109316] - Support additional points categories for jh 

## [v0.0.135] - 2025-04-14

* [DFCT0045449] - module login issue fixed 

## [v0.0.134] - 2025-04-10

* [SFSTRY0108702][Gutenberg][Points History - Points History Monthly wise drop down is not resetting to default when applied filters are cleared] (#149) 

## [v0.0.133] - 2025-03-27

* [SFSTRY0106361] - As the DevOps team requested I am raising a new PR again because the journey pipeline is failing due to my previous PR commit message containing an apostrophe in members (#148)
Co-authored-by: p01sumalaths <<EMAIL>> 

## [v0.0.132] - 2025-03-26

* [SFSTRY0106361] - The member's points history in the app does not display all months 

## [v0.0.131] - 2025-03-20

* [SFSTRY0104177] - Publishing the sdk flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.130] - 2025-03-20

* [SFSTRY0105932][Gutenberg][Points History - [FLTUpgrade] Journey module updates related to flutter 3.24.5 upgrade] (#146)  Semantic label issue in Points History] (#143) 

## [v0.0.128] - 2025-03-03

* [SFSTRY0094038][Gutenberg][Points History - [FLTUpgrade] Journey Modules Update] 

## [v0.0.127] - 2025-02-14

* [SFSTRY0088058] - test cases fixes

* [SFSTRY0094553] - test cases added with 82.6%

* [SFSTRY0088058] - test cases fixes

* [SFSTRY0088058] - test cases fixes]

* [[SFSTRY0088058] - Error handling changes.

* [SFSTRY0094553] - dynatrace event changes.

* [SFSTRY0094553] - dynatrace event changes. 

## [v0.0.126] - 2025-02-07

* [DFCT0039531] - add app_settings as temporary fix to bitrise fail

* [DFCT0039531] - add app_settings for temporary fix; enable unit tests

* [DFCT0039531] - remove http_manager; test disabled unit tests

* [DFCT0039531] - move http_manager dependencies to override only.

* [DFCT0039531] - include http_manager dependency

* [DFCT0039531] - update framework-contracts

* [DFCT0039531] - points history period offset extension 

## [v0.0.125] - 2025-01-30

* [DFCT0043316] Points monitor showing 'App unavailable'
improve sorting and fixes the date parsing issue due to locale 

## [v0.0.124] - 2025-01-23

* [SFSTRY0100540][MMF][used VGBackButton; enabled mmf on filters modal sheet] (#139)
* [SFSTRY0100540] - used VGBackButton; enabled mmf on filters modal sheet

* [DFCT0043173] - While adding points through VSP, Points History graph not getting update 

## [v0.0.123] - 2025-01-21

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.122] - 2025-01-17

* [SFSTRY0097321] - rename the files and variables to more appropriate name

* [SFSTRY0097321] - use extensions to prevent affecting other markets

* [SFSTRY0097321] - added the keys for event keys and new funtion to use those keys 

## [v0.0.121] - 2025-01-10

* [SFSTRY0095820][Gutenberg][Points History - userPreferenceProvider changes]

* [SFSTRY0095820][Gutenberg][Coins to fuelbar - userPreferenceProvider changes] 

## [v0.0.120] - 2024-12-27

* [SFSTRY0094091][DFCT0042269][[T-31][GTB ECU] Reactivate - OLD membership ID is still displayed on the Profile screen upon Membership reactivation.] 

## [v0.0.119] - 2024-12-24

* [SFSTRY0095820][INJECTION][SECURITY - LOW][iOS][Android] VIT5994371- Sensitive Information Stored in Plain Text 

## [v0.0.118] - 2024-11-28

* [SFSTRY0097962] - didUpdateWidget added in VGPointsHistoryGraphWidget (#132)
* [SFSTRY0097962] - didUpdateWidget added in VGPointsHistoryGraphWidget

* [SFSTRY0097962] - didUpdateWidget added in VGPointsHistoryGraphWidget

---------

Co-authored-by: Sravan Kumar <<EMAIL>> 

## [v0.0.117] - 2024-11-25

* [DFCT0041147] Filter is not working
fix an exception when points is formatted with "." 

## [v0.0.116] - 2024-11-18

* [SFSTRY0094771] - removed serect properties file

* [SFSTRY0094771] - minor fixes

* [SFSTRY0094771] - steps range change fixes 

## [v0.0.115] - 2024-11-13

* [DFCT0037037][MEX] - Add advanced screening key to filter categories 

## [v0.0.114] - 2024-11-08

* [SFSTRY0096758] - wrap routes with VGInheritedTopLevelWidget 

## [v0.0.113] - 2024-11-07

* Sfstry0094088 dfct0039173 (#126)
* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value is not consistent across the app]

* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value is not consistent across the app] 

## [v0.0.112] - 2024-11-01

* [DFCT0040190][DFCT0039524] - Add checking for duplicate month and year 

## [v0.0.111] - 2024-10-28

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines. 

## [v0.0.28] - 2024-03-13

* [SFSTRY0073337] [Engineering - GTB]Add a readme/changelog file and update it with latest commits during every new release tag. 1st test. 
