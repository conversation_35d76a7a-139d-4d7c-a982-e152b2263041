// ignore_for_file: depend_on_referenced_packages

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
import 'package:platform_core/platform_core.dart';
import 'package:points_history/model/points_entries_detail_model.dart';
import 'package:points_history/models/points_history_detail.dart';
import 'package:points_history/utils/constants.dart';
import 'package:points_history/utils/custom_exception.dart';
import 'package:points_history/utils/extensions.dart';
import 'package:points_history/utils/image_content_keys.dart';
import 'package:points_history/utils/localization_helpers.dart';
import 'package:points_history/utils/route_config_key.dart';
import 'package:points_history/views/bloc/points_history_details/points_history_details_cubit.dart';
import 'package:points_history/views/bloc/points_history_details/points_history_details_state.dart';
import 'package:points_history/widgets/custom_text_widget.dart';
import 'package:points_history/widgets/points_history_error.dart';
import 'package:v1_global_models_flutter/src/shared_models/points_history.dart' ;


// ignore: must_be_immutable

class PointsHistoryDetailsPage extends StatelessWidget {
  final PointsEntry? pointsEntry;

  PointsHistoryDetailsPage({super.key, required this.pointsEntry});

  final _cubit = VGPointsHistoryDetailsCubit();

  @override
  Widget build(BuildContext context) {
    var localization =
        VGLocator.get<PlatformCore>().resourceProvider.localization(context);
    final isLargeText = MediaQuery.of(context).textScaler.scale(1.5) >= 2.0;
    final l18Helper = LocalizationHelpers(localization: localization!);
    return BlocProvider(
      create: (providerContext) =>
          _cubit..getAll(pointsEntry, localization, context: context),
      child: VGColoredStatusBar(
        brightness: Brightness.light,
        color: VGApplicationTheme.colors.base0,
        child: Scaffold(
            appBar: AppBar(
              backgroundColor: VGApplicationTheme.colors.base0,
              bottomOpacity: 0.0,
              elevation: 0.0,
              scrolledUnderElevation: 0,
              leading: const VGBackButton(
                onTap: VGNavigator.pop,
                backIcon: FontAwesomeIcons.arrowLeft,
              ),
            ),
            body: BlocConsumer<VGPointsHistoryDetailsCubit,
                    VGPointsHistoryDetailsState>(
                listener:
                    (BuildContext context, VGPointsHistoryDetailsState state) {
              if (state.error is CustomException) {
                int? errorCode = (state.error as CustomException).errorCode;
                String? errorMessage =
                    (state.error as CustomException).errorMessage;

                if (errorCode == ErrorConstants.networkError ||
                    errorCode == ErrorConstants.authenticationFailCode400 ||
                    errorCode == ErrorConstants.hostResolveFailed &&
                        errorMessage != null) {
                  // //This is require because don't have option to come back if data not loaded
                  //Navigator.pop(context);
                  final dialogData =
                      l18Helper.getDialogDataByError(errorMessage);
                  context.showDialogFromDialogData(dialogData);
                } else {
                  String path = PreferenceUtils().getGlobalRouteConfig()[
                      RouteConfigKey.vgDownTimeScreen.name];
                  VGNavigator.pushReplacementNamed(path, extraParameters: {
                    "title": VGResourceBundleKey.txthdapperror_001.key,
                    "bodyText": VGResourceBundleKey.txtbodyapperror_001.key,
                    "imageName": ImageContentKeys.downTimeImage,
                    "buttonName": VGResourceBundleKey.ctrl_btngotit_001.key,
                    "onButtonPressed": () async {
                      VGNavigator.pop();
                    }
                  });
                }
              }
            }, builder: (context, state) {
              if (state.isLoading) {
                return const Center(
                  child: VGCircularLoadingIndicator(),
                );
              }
              if (state.error != null) {
                return const VGPointsHistoryErrorView();
              }
              if (state.details != null) {
                return _createBody(state.details!, localization, isLargeText: isLargeText);
              }
              return Container();
            })),
      ),
    );
  }

  Widget _createBody(
      PointHistoryDetail detail, ResourceBundleLocalization? localization, {bool isLargeText = false}) {
    return Container(
      color: VGApplicationTheme.colors.base0,
      child: ListView.builder(
          itemCount: detail.items.length + 1,
          itemBuilder: (context, index) {
            if (index == 0) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.all(16),
                    child: CustomTextWidget(
                      VGResourceBundleKey.txtactivitydetail_001.key,
                      style: VGApplicationTheme.typography.h3,
                    ),
                  ),
                  Container(
                      decoration: BoxDecoration(
                          borderRadius:
                              const BorderRadius.all(Radius.circular(4)),
                          color: VGApplicationTheme.colors.blueGrey),
                      margin: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 24),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          isLargeText?
                          Column(
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: VGApplicationTheme.colors.base0,
                                ),
                                child: Center(
                                  child: VGSemantics(
                                    semanticsProperties: SemanticsProperties(
                                      label: '${FontAwesomeIcons.solidStar}',
                                    ),
                                    child: FaIcon(
                                      FontAwesomeIcons.solidStar,
                                      semanticLabel: "Icon: SolidStar",
                                      color:
                                      VGApplicationTheme.colors.statusGold,
                                      size: 11,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox.fromSize(
                                size: const Size(8, 1),
                              ),
                              CustomTextWidget(
                                detail.header,
                                style: VGApplicationTheme.typography.pSmallBold,
                              )
                            ],
                          ):
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: VGApplicationTheme.colors.base0,
                                ),
                                child: Center(
                                  child: VGSemantics(
                                    semanticsProperties: SemanticsProperties(
                                      label: '${FontAwesomeIcons.solidStar}',
                                    ),
                                    child: FaIcon(
                                      FontAwesomeIcons.solidStar,
                                      semanticLabel: "Icon: SolidStar",
                                      color:
                                          VGApplicationTheme.colors.statusGold,
                                      size: 11,
                                    ),
                                  ),
                                ),
                              ),
                              SizedBox.fromSize(
                                size: const Size(8, 1),
                              ),
                              CustomTextWidget(
                                detail.header,
                                style: VGApplicationTheme.typography.pSmallBold,
                              )
                            ],
                          ),
                          (detail.note?.isEmpty == false)
                              ? Column(
                                  children: [
                                    SizedBox.fromSize(
                                      size: const Size(1, 12),
                                    ),
                                    CustomTextWidget(
                                      detail.note ?? "",
                                      style:
                                          VGApplicationTheme.typography.pSmall,
                                    )
                                  ],
                                )
                              : const SizedBox.shrink()
                        ],
                      )),
                  _pointsTowardsGoalWidget(detail, localization, isLargeText),
                  (detail.pointsTowardsGoalItems != null &&
                          detail.pointsTowardsGoalItems!.isNotEmpty)
                      ? const VGListSpacer()
                      : const SizedBox.shrink(),
                ],
              );
            } else {
              final detailItem = detail.items[index - 1];
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.only(top: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CustomTextWidget(
                      semanticsLabel: "${detailItem.title}_$index",
                      detailItem.title,
                      style: VGApplicationTheme.typography.pMedium,
                    ),
                    SizedBox.fromSize(
                      size: const Size(1, 4),
                    ),
                    CustomTextWidget(
                      semanticsLabel: "${detailItem.subtitle}_$index",
                      detailItem.subtitle,
                      style: VGApplicationTheme.typography.pSmall,
                    ),
                    SizedBox.fromSize(
                      size: const Size(1, 16),
                    ),
                    Divider(
                      height: 1,
                      color: VGApplicationTheme.colors.base300,
                    )
                  ],
                ),
              );
            }
          }),
    );
  }

  Widget _pointsTowardsGoalWidget(PointHistoryDetail pointHistoryDetail,
      ResourceBundleLocalization? localization, bool isLargeText) {
    return (pointHistoryDetail.pointsTowardsGoalItems != null &&
            pointHistoryDetail.pointsTowardsGoalItems!.isNotEmpty)
        ? Container(
            margin:
                const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomTextWidget(
                  semanticsLabel: "txtpointstowardsgoal_002",
                  VGResourceBundleKey.txtpointstowardsgoal_002.key,
                  style: VGApplicationTheme.typography.pMedium,
                ),
                const SizedBox(
                  height: 4,
                ),
                for (var item in pointHistoryDetail.pointsTowardsGoalItems!)
                  Container(
                    margin: const EdgeInsets.only(top: 8),
                    child: isLargeText
                        ? Column(
                            children: getPointsTowardsGoalWidgets(
                              localization,
                              item,
                              isLargeText,
                            ),
                          )
                        : Row(
                            children: getPointsTowardsGoalWidgets(
                              localization,
                              item,
                              isLargeText,
                            ),
                          ),
                  ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }

  List<Widget> getPointsTowardsGoalWidgets(
      ResourceBundleLocalization? localization,
      PointsTowardsGoalDetailItem item,
      bool isLargeText) {
    return [
      IconButton(
        constraints: BoxConstraints.tight(const Size(20, 20)),
        padding: EdgeInsets.zero,
        onPressed: () {},
        icon: FaIcon(
          semanticLabel: 'Icon: goalTracker_${item.goalKey}',
          PointEntryDetailCategoryExtension.getIconDataByKeys(item.goalKey),
          color: VGApplicationTheme.colors.primary500,
          size: 16,
        ),
      ),
      const SizedBox(
        width: 8,
      ),
      CustomTextWidget(
        semanticsLabel: 'Text: goalTracker_${item.goalKey}',
        localization!.valueOf(
            params: [item.pointContributing.toString(), item.goalName],
            key: VGResourceBundleKey.txttowardsgoalbreakdown_001.key),
        style: VGApplicationTheme.typography.pSmall,
      ),
    ];
  }
}
