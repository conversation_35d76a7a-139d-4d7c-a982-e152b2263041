name: "points_history"
description: "This project is used for points history"
version: 0.0.148
homepage: https://github.com/discovery-ltd/v1-points-history-flutter
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  json_annotation: ^4.4.0
  flutter_modular: ^5.0.3
  mockito: ^5.4.0
  flutter_bloc: ^8.1.3
  go_router: ^10.2.0
  get_it: ^7.6.4
  injectable: ^2.3.1
  intl: ^0.18.1
  collection: ^1.17.1
  flutter_svg: ^2.0.9
  freezed_annotation: ^2.4.1
  bloc: ^8.1.2
  analyzer: ^5.13.0
  equatable: ^2.0.0
dependency_overrides:
  http: 1.1.0
  intl: ^0.19.0
  flutter_secure_storage: ^8.0.0
  dynatrace_flutter_plugin: ^3.287.2
  meta: ^1.11.0
  app_settings: 5.1.1
  flutter_inappwebview: 6.1.5
  visibility_detector: ^0.4.0+2
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.270
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.562
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.74
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.152
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.21
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: master
  goals_points_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-goals-points-services-micro-service-sdk-flutter.git
      path: goals_points_services_micro_service_sdk
      ref: main
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.20
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.481
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.12
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.15
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.12
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.13
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.35
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.19
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.41
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.243
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.25
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.356
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.27
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.21
  preferences_migration:
    git:
      url: **************:discovery-ltd/v1-preferences-migration-flutter.git
      path: preferences_migration
      ref: 0.0.49
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
  injectable_generator: ^2.4.1
  freezed: 2.5.2
  # analyzer: ^6.4.1
  meta: ^1.11.0
flutter:
  uses-material-design: true
  assets:
    - assets/config/
    - assets/images/
