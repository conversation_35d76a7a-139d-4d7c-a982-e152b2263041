import 'package:flutter_test/flutter_test.dart';
import 'package:rewards_journey/src/common/models/status_badge_info.dart';
import 'package:rewards_journey/src/features/partner_detail/bloc/reward_partner_detail_bloc.dart';

void main() {
  const initialState = RewardPartnerDetailState();
  const loadingState = RewardPartnerDetailState(isStatusLoading: true);
  final statusBadgeInfo = [
    const StatusBadgeInfo(
        typeKey: 1,
        sortOrder: 1,
        isStatusActive: false,
        isLocked: false,
        statusName: "Blue"),
    const StatusBadgeInfo(
        typeKey: 1,
        sortOrder: 2,
        isStatusActive: false,
        isLocked: false,
        statusName: "Bronze"),
    const StatusBadgeInfo(
        typeKey: 1,
        sortOrder: 3,
        isStatusActive: true,
        isLocked: false,
        statusName: "Silver"),
    const StatusBadgeInfo(
        typeKey: 3,
        sortOrder: 4,
        isStatusActive: false,
        isLocked: true,
        statusName: "Gold")
  ];
  final loadedState = RewardPartnerDetailState(
      listOfStatuses: statusBadgeInfo, currentStatusTypeKey: 5);
  const errorState = RewardPartnerDetailState(
    statusErrorMessage: "errorMessage",
  );

  test('states extending for RewardPartnerDetailState', () {
    expect(initialState, isA<RewardPartnerDetailState>());
    expect(loadingState, isA<RewardPartnerDetailState>());
    expect(errorState, isA<RewardPartnerDetailState>());
    expect(loadedState, isA<RewardPartnerDetailState>());

    expect(errorState.props.length, equals(12));
    expect(
      loadedState.props,
      [statusBadgeInfo, '', 5, false, null, [], false, null, [], false, null, null],
    );
  });
}
