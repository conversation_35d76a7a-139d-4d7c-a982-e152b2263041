import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:platform_core/platform_core.dart';
import 'package:rewards_journey/src/common/widgets/widgets.dart';
import 'package:rewards_journey/src/features/partner_detail/bloc/reward_partner_detail_bloc.dart';

class RewardRevealCodeWidget extends StatelessWidget {
  const RewardRevealCodeWidget({
    required this.rewardKey,
    required this.allocationRewardKey,
    super.key,
  });

  final String rewardKey;
  final String allocationRewardKey;

  @override
  Widget build(BuildContext context) {
    final state = context.watch<RewardPartnerDetailBloc>().state;
    if (state.isRevealCodesLoading) {
      return const Center(child: VGCircularLoadingIndicator());
    } else if (state.revealCodesErrorMessage != null) {
      return const SizedBox.shrink(); // or show error UI
    } else if (state.isRevealCodesLoading == false &&
        state.revealCodesErrorMessage == null) {
      final totalAvailableRevealCode =
          (state.awardedRewards.first.rewardValue.quantity ?? 0).toInt();

      final revelCodeCount = state.awardedRevealCodes.isEmpty
          ? 0
          : state.awardedRevealCodes.length;

      return Container(
        color: VGApplicationTheme.colors.base0,
        child: IntrinsicHeight(
          child: Padding(
            padding: const EdgeInsets.only(right: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    CustomTextWidget(
                      "$revelCodeCount",
                      style: VGApplicationTheme.typography.dh1,
                      textAlign: TextAlign.start,
                      semanticsLabel: "current-revealCodeCount",
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 10.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CustomTextWidget(
                            "/",
                            style: VGApplicationTheme.typography.h2,
                            textAlign: TextAlign.start,
                          ),
                          CustomTextWidget(
                            "$totalAvailableRevealCode",
                            style: VGApplicationTheme.typography.h2,
                            textAlign: TextAlign.start,
                            semanticsLabel:
                                VGResourceBundleKey.txtcodesrevealed_001.key,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                CustomTextWidget(
                  VGResourceBundleKey.txtcodesrevealed_001.key,
                  style: VGApplicationTheme.typography.pMedium,
                ),
              ],
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
