part of 'resources.dart';

abstract class Images {
  static const insuranceRewardsDir =
      "shared/images/journey/rewards/insurance_rewards";
  static const String downTimeImage =
      "shared/images/common/error_screens/tools.png";
  static const allVariationsLogo =
      '$insuranceRewardsDir/logo_all-variations.png';
  // bronze
  static const bronzeStatusLocked =
      '$insuranceRewardsDir/bronze_status_locked.png';
  static const bronzeStatusActive =
      '$insuranceRewardsDir/bronze_status_active.png';
  // silver
  static const silverStatusLocked =
      '$insuranceRewardsDir/silver_status_locked.png';
  static const silverStatusActive =
      '$insuranceRewardsDir/silver_status_active.png';
  // gold
  static const goldStatusLocked = '$insuranceRewardsDir/gold_status_locked.png';
  static const goldStatusActive = '$insuranceRewardsDir/gold_status_active.png';
  // platinum
  static const platinumStatusLocked =
      '$insuranceRewardsDir/platinum_status_locked.png';
  static const platinumStatusActive =
      '$insuranceRewardsDir/platinum_status_active.png';
  // blue
  static const blueStatusActive = '$insuranceRewardsDir/blue_status_active.png';
  static const blueStatusLocked = '$insuranceRewardsDir/blue_status_locked.png';

  //blue lock icon
  static const statusIndicatorLocked = '$insuranceRewardsDir/circle-check.png';
  //expedia PartnerLogo
  static const expediaPartnerLogo =
      'shared/images/journey/rewards/expedia_passthrough/partnerlogo.png';
  static const adidasPartnerLogo =
      'shared/images/journey/rewards/status_rewards/adidas/partnerlogo.png';
  static const adidasLandingCompletedIllustrationImage =
      'shared/images/journey/rewards/status_rewards/adidas/status_based_landing_completed_image_1336.png';
  static const discountCode =
      'shared/images/journey/rewards/status_rewards/adidas/discountcode.png';
}
