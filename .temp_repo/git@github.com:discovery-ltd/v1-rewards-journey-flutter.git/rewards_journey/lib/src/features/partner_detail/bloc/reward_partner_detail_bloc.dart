// ignore_for_file: implementation_imports

import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:platform_core/platform_core.dart';
import 'package:rewards_journey/src/common/models/status_badge_info.dart';
import 'package:rewards_journey/src/common/repository/reward_status_detail_repo.dart';
import 'package:rewards_journey/src/common/utils/enumeration.dart';
import 'package:rewards_journey/src/features/partner_detail/domain/repository/reward_partner_landing_repository.dart';
import 'package:v1_journey_commons_flutter/src/utils/exception_severity_enum.dart';
import 'package:v2_manage_rewards_micro_service_sdk/model/getAwardedRewardByPartyId/AwardedReward.dart';

part 'reward_partner_detail_event.dart';

part 'reward_partner_detail_state.dart';

class RewardPartnerDetailBloc
    extends Bloc<RewardPartnerDetailEvent, RewardPartnerDetailState> {
  final RewardStatusDetailRepo rewardStatusDetailRepo;

  RewardPartnerDetailBloc(this.rewardStatusDetailRepo)
      : super(const RewardPartnerDetailState()) {
    on<GetStatusByMembershipIdEvent>(_onGetStatusByMembershipIdEvent);
    on<GetAwardedRewardByPartyIdEvent>(_onGetAwardedRewardsByPartyId);
    on<GetAwardedRewardByPartyIdByRewardKeyEvent>(
        _onGetAwardedRewardsByPartyIdByRewardKey);
  }

  Future<void> _onGetStatusByMembershipIdEvent(
    GetStatusByMembershipIdEvent event,
    Emitter<RewardPartnerDetailState> emit,
  ) async {
    emit(state.copyWith(isStatusLoading: true, statusErrorMessage: null));
    try {
      var planName = '';
      // adding try catch to prevent the app from crashing and show the default screen for central.
      try {
        ActivePlanResolverRepository repository =
            VGLocator.get<ActivePlanResolverRepository>();
        ActivePlanModel activePlan = await repository.getActivePlan();
        planName = activePlan.planId.toLowerCase();
      } catch (e) {
        //do nothing.
      }
      final response = await rewardStatusDetailRepo
          .getPointsToMaintainOrHigherStatusMethod();

      final int currentStatusTypeKey =
          response.firstWhere((e) => e.isStatusActive).typeKey;

      if (response.isNotEmpty) {
        emit(
          state.copyWith(
            listOfStatuses: response,
            planName: planName,
            currentStatusTypeKey: currentStatusTypeKey,
            isStatusLoading: false,
            statusErrorMessage: null,
          ),
        );
      } else {
        emit(
          state.copyWith(isStatusLoading: false),
        );
      }
    } catch (e) {
      if (e is NoConnectionFailure) {
        uiEventDelegate?.logException(
          exceptionType: e.errorType.toString(),
          exceptionMessage: e.message.toString(),
          customUserTag:
              'Rewards-RewardPartnerDetailBloc: onGetStatusByMembershipIdEvent',
          severity: ExceptionSeverity.High.name,
          stackTrace: e.stackTrace.toString(),
        );
      } else {
        uiEventDelegate?.logException(
          exceptionType: e.runtimeType.toString(),
          exceptionMessage: e.toString(),
          customUserTag:
              'Rewards-RewardPartnerDetailBloc: onGetStatusByMembershipIdEvent',
          severity: ExceptionSeverity.High.name,
        );
      }
      emit(
        state.copyWith(
          isStatusLoading: false,
          statusErrorMessage: e.toString(),
        ),
      );
    }
  }

  Future<void> _onGetAwardedRewardsByPartyId(
    GetAwardedRewardByPartyIdEvent event,
    Emitter<RewardPartnerDetailState> emit,
  ) async {
    emit(state.copyWith(isRewardsLoading: true));
    try {
      final repository = VGLocator.get<RewardPartnerLandingRepository>(
        instanceName: 'rewardsModule',
      );
      var awardedRewards = await repository.getAwardedRewardByPartyId(
        allocationRewardKey: event.allocationRewardKey,
      );

      // recompute filter
      final filter = (awardedRewards?.isEmpty ?? true)
          ? Eligibility.notEligible.name
          : (awardedRewards?.first.rewardValue.quantity ==
                  state.awardedRevealCodes.length)
              ? Eligibility.completed.name
              : Eligibility.eligible.name;

      emit(state.copyWith(
        isRewardsLoading: false,
        awardedRewards: awardedRewards ?? [],
        stateFilter: filter,
      ));
    } catch (e) {
      emit(state.copyWith(
          isRewardsLoading: false, rewardsErrorMessage: e.toString()));
    }
  }

  Future<void> _onGetAwardedRewardsByPartyIdByRewardKey(
    GetAwardedRewardByPartyIdByRewardKeyEvent event,
    Emitter<RewardPartnerDetailState> emit,
  ) async {
    emit(state.copyWith(isRevealCodesLoading: true));
    try {
      final repository = VGLocator.get<RewardPartnerLandingRepository>(
        instanceName: 'rewardsModule',
      );
      var awardedRevealCodes = await repository.getAwardedRewardByPartyId(
        rewardKey: event.rewardKey,
      );

      // recompute filter
      final filter = state.awardedRewards.isEmpty
          ? Eligibility.notEligible.name
          : (state.awardedRewards.first.rewardValue.quantity ==
                  awardedRevealCodes?.length)
              ? Eligibility.completed.name
              : Eligibility.eligible.name;

      emit(
        state.copyWith(
          isRevealCodesLoading: false,
          awardedRevealCodes: awardedRevealCodes ?? [],
          stateFilter: filter,
        ),
      );
    } catch (e) {
      emit(state.copyWith(
          isRevealCodesLoading: false, revealCodesErrorMessage: e.toString()));
    }
  }
}
