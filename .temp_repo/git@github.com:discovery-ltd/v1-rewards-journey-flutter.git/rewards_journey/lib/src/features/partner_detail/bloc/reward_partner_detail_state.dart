part of 'reward_partner_detail_bloc.dart';

class RewardPartnerDetailState extends Equatable {
  final List<StatusBadgeInfo> listOfStatuses;
  final String planName;
  final int currentStatusTypeKey;
  final bool isStatusLoading;
  final String? statusErrorMessage;

  final List<AwardedReward> awardedRewards;
  final bool isRewardsLoading;
  final String? rewardsErrorMessage;

  final List<AwardedReward> awardedRevealCodes;
  final bool isRevealCodesLoading;
  final String? revealCodesErrorMessage;
  final int? lastUpdated;

  final String? stateFilter;

  const RewardPartnerDetailState({
    this.listOfStatuses = const [],
    this.planName = '',
    this.currentStatusTypeKey = 0,
    this.isStatusLoading = false,
    this.statusErrorMessage,
    this.awardedRewards = const [],
    this.isRewardsLoading = false,
    this.rewardsErrorMessage,
    this.awardedRevealCodes = const [],
    this.isRevealCodesLoading = false,
    this.revealCodesErrorMessage,
    this.lastUpdated,
    this.stateFilter,
  });

  RewardPartnerDetailState copyWith({
    List<StatusBadgeInfo>? listOfStatuses,
    String? planName,
    int? currentStatusTypeKey,
    bool? isStatusLoading,
    String? statusErrorMessage,
    List<AwardedReward>? awardedRewards,
    bool? isRewardsLoading,
    String? rewardsErrorMessage,
    List<AwardedReward>? awardedRevealCodes,
    bool? isRevealCodesLoading,
    String? revealCodesErrorMessage,
    String? stateFilter,
  }) {
    return RewardPartnerDetailState(
      listOfStatuses: listOfStatuses ?? this.listOfStatuses,
      planName: planName ?? this.planName,
      currentStatusTypeKey: currentStatusTypeKey ?? this.currentStatusTypeKey,
      isStatusLoading: isStatusLoading ?? this.isStatusLoading,
      statusErrorMessage: statusErrorMessage ?? this.statusErrorMessage,
      awardedRewards: awardedRewards ?? this.awardedRewards,
      isRewardsLoading: isRewardsLoading ?? this.isRewardsLoading,
      rewardsErrorMessage: rewardsErrorMessage ?? this.rewardsErrorMessage,
      awardedRevealCodes: awardedRevealCodes ?? this.awardedRevealCodes,
      isRevealCodesLoading: isRevealCodesLoading ?? this.isRevealCodesLoading,
      revealCodesErrorMessage:
          revealCodesErrorMessage ?? this.revealCodesErrorMessage,
      stateFilter: stateFilter ?? this.stateFilter,
    );
  }

  @override
  List<Object?> get props => [
        listOfStatuses,
        planName,
        currentStatusTypeKey,
        isStatusLoading,
        statusErrorMessage,
        awardedRewards,
        isRewardsLoading,
        rewardsErrorMessage,
        awardedRevealCodes,
        isRevealCodesLoading,
        revealCodesErrorMessage,
        stateFilter
      ];
}
