import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
import 'package:json_content_render/json_content_render.dart';
import 'package:platform_core/platform_core.dart';
import 'package:rewards_journey/src/common/repository/reward_status_detail_repo.dart';
import 'package:rewards_journey/src/common/utils/text_resolver_extension.dart';
import 'package:rewards_journey/src/common/values/constant.dart';
import 'package:rewards_journey/src/common/values/reward_phrase_key.dart';
import 'package:rewards_journey/src/common/widgets/reward_appbar.dart';
import 'package:rewards_journey/src/common/widgets/unlock_reward_widget.dart';
import 'package:rewards_journey/src/features/partner_detail/bloc/reward_partner_detail_bloc.dart';
import 'package:rewards_journey/src/features/partner_detail/presentation/views/reward_status_levels_list_view.dart';
import 'package:rewards_journey/src/features/reveal_code/data/model/reveal_code_navigation_model.dart';
import 'package:rewards_journey/src/resources/resources.dart';

class RewardPartnerLandingScreen extends StatelessWidget {
  final String? rewardKey;
  final String? allocationRewardKey;

  const RewardPartnerLandingScreen({
    super.key,
    this.rewardKey,
    this.allocationRewardKey,
  });

  @override
  Widget build(BuildContext context) {
    return VGColoredStatusBar(
      color: VGApplicationTheme.colors.base50,
      brightness: Brightness.light,
      child: Scaffold(
        backgroundColor: VGApplicationTheme.colors.base0,
        appBar: const RewardAppbar(),
        body: SafeArea(
          child: SingleChildScrollView(
            child: BlocProvider(
              create: (_) => RewardPartnerDetailBloc(
                VGLocator.get<RewardStatusDetailRepo>(
                    instanceName: 'rewardsModule'),
              )
                ..add(GetStatusByMembershipIdEvent())
                ..add(
                  GetAwardedRewardByPartyIdEvent(
                    allocationRewardKey: allocationRewardKey,
                  ),
                )
                ..add(
                  GetAwardedRewardByPartyIdByRewardKeyEvent(
                    rewardKey: rewardKey,
                  ),
                ),
              child: BlocBuilder<RewardPartnerDetailBloc,
                  RewardPartnerDetailState>(
                buildWhen: (previous, current) {
                  return previous.awardedRevealCodes !=
                          current.awardedRevealCodes ||
                      previous.awardedRewards != current.awardedRewards ||
                  previous.listOfStatuses != current.listOfStatuses;
                },
                builder: (context, state) {
                  final bloc = context.read<RewardPartnerDetailBloc>();
                  if (state.rewardsErrorMessage != null) {
                    vgLogE('RewardPartnerDetailError',
                        state.rewardsErrorMessage.toString());
                  } else if (state.isRewardsLoading) {
                    return const Center(child: VGCircularLoadingIndicator());
                  } else if (state.isRewardsLoading == false &&
                      state.rewardsErrorMessage == null &&
                      state.stateFilter != null) {
                    return JsonContentView(
                      key: ValueKey(state.stateFilter),
                      pageName: AppConstants.getStatusRewardPageName(
                          allocationRewardKey: allocationRewardKey ?? ''),
                      stateFilter: state.stateFilter,
                      ctaButtonOnTap: (String navRoute) async {
                        final result = await VGNavigator.push(
                          navRoute,
                          extraParameters: RevealCodeNavigationModel(
                            currentStatusTypeKey: state.currentStatusTypeKey,
                            awardedRewardId: state.awardedRewards.first.id,
                          ),
                        );
                        if (result == true) {
                          bloc.add(
                            GetAwardedRewardByPartyIdByRewardKeyEvent(
                                rewardKey: rewardKey),
                          );
                        }
                      },
                      outletContentBuilder: (String value) =>
                          _outletContentBuilder(
                              context, value, state, rewardKey),
                    );
                  }
                  return const SizedBox.shrink();
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _outletContentBuilder(
    BuildContext context,
    String value,
    RewardPartnerDetailState state,
    String? rewardKey,
  ) {
    if (value == AppConstants.unlockBenefitAdidas) {
      return const UnlockRewardWidget();
    } else if (value == AppConstants.statusBasedRRewardsVitalityStatusBadge) {
      if (state.statusErrorMessage != null) {
        vgLogE('RewardPartnerDetailError', state.statusErrorMessage.toString());
      } else if (state.isStatusLoading == false &&
          state.statusErrorMessage == null) {
        return RewardStatusLevelsList(
          partnerName: value,
          planName: state.planName,
          listOfStatuses: state.listOfStatuses,
          rewardKey: rewardKey,
        );
      }
      return const SizedBox.shrink();
    } else if (value == AppConstants.adidasCompleteStatusIllustration) {
      return Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16.0,
        ),
        child: VGEmptyStates.emptyState(
          type: VGEmptyStateType.status,
          bodyText: context.resolveText(
            RewardPhraseKey.getPhraseKey(
                RewardPhraseKey.txtCompletedText, rewardKey ?? ''),
          ),
          image: ImageBuilder(
            Images.adidasLandingCompletedIllustrationImage,
            builder: (imageProvider) => VGImage(
              image: imageProvider,
              fit: BoxFit.cover,
            ),
            placeHolder: Assets.images.placeholder.keyName,
            noImagePlaceholder: Assets.images.noImagePlaceholder.keyName,
          ),
        ),
      );
    } else {
      return const SizedBox.shrink();
    }
  }
}
