import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:platform_core/platform_core.dart';
import 'package:rewards_journey/src/common/models/codes_card_item.dart';
import 'package:rewards_journey/src/common/utils/icon_utils.dart';
import 'package:rewards_journey/src/common/values/constant.dart';
import 'package:rewards_journey/src/features/code_history/domain/repository/codes_history_repository.dart';
import 'package:rewards_journey/src/features/code_history/presentation/bloc/reward_codes_history_event.dart';
import 'package:rewards_journey/src/features/code_history/presentation/bloc/reward_codes_history_state.dart';
import 'package:v1_journey_commons_flutter/journey_commons.dart';

class RewardCodesHistoryBloc
    extends Bloc<RewardCodesHistoryEvent, RewardCodesHistoryState> {
  RewardCodesHistoryBloc() : super(const RewardCodesHistoryState()) {
    on<LoadActiveCodesEvent>(_onLoadActiveCodesEvent);
    on<LoadArchivedCodesEvent>(_onLoadArchivedCodesEvent);
    on<ToggleArchiveModeEvent>(_onToggleArchiveModeEvent);
    on<SetArchiveButtonStateEvent>(_onSetArchiveButtonStateEvent);
    on<ArchiveSelectedCodesEvent>(_onArchiveSelectedCodesEvent);
    on<ArchiveSingleCodeEvent>(_onArchiveSingleCodeEvent);
    on<UpdateCodesListEvent>(_onUpdateCodesListEvent);
  }

  void _onLoadActiveCodesEvent(
    LoadActiveCodesEvent event,
    Emitter<RewardCodesHistoryState> emit,
  ) async {
    emit(
      state.copyWith(
        isLoading: true,
        errorMessage: null,
      ),
    );
    try {
      final repository = VGLocator.get<CodesHistoryRepository>(
        instanceName: 'rewardsModule',
      );
      final rewardKey = event.rewardKey ?? state.rewardKey; // <-- fallback
      final awardedRewardCards = await repository.getAwardedRewardByPartyId(
        rewardKey: rewardKey,
      );
      final availableAwardedRewardCards = awardedRewardCards?.where(
            (element) {
              return element.awardedRewardStatuses.any((status) =>
                      status.key == AppConstants.kIssuePendingKey) ||
                  element.awardedRewardStatuses
                      .any((status) => status.key == AppConstants.kIssuedKey) ||
                  element.awardedRewardStatuses.any(
                      (status) => status.key == AppConstants.kUsedStatusKey);
            },
          ).toList() ??
          [];

      availableAwardedRewardCards.sort((a, b) {
        final firstAwardedDate = DateTime.parse(a.awardedOn);
        final secondAwardedDate = DateTime.parse(b.awardedOn);
        final firstAwardedPercent = a.rewardValue.percent ?? 0.0;
        final secondAwardedPercent = b.rewardValue.percent ?? 0.0;
        // First: awardedOn DESC
        final compareDate = secondAwardedDate.compareTo(firstAwardedDate);
        if (compareDate != 0) return compareDate;
        // Then: percent DESC
        final comparePercent =
            secondAwardedPercent.compareTo(firstAwardedPercent);
        if (comparePercent != 0) return comparePercent;
        // Then: id DESC
        return (b.id ?? 0).compareTo(a.id ?? 0);
      });

      final cards = availableAwardedRewardCards.map((awardedReward) {
        var rewardValue = awardedReward.rewardValue.percent ?? 0;
        final rewardName = utf8.decode(awardedReward.reward.name.codeUnits);
        String awardedOn = awardedReward.awardedOn;
        return VGCodesCardItem(
          id: "${awardedReward.id}",
          amount: rewardValue.toInt().toString(),
          logo: IconUtil.icon("${awardedReward.reward.key}"),
          brandName: rewardName,
          awardedOn: awardedOn,
          expiryDate: awardedReward.effectiveTo,
          rewardCode:
              (awardedReward.awardedRewardReferences?.isNotEmpty ?? false)
                  ? awardedReward.awardedRewardReferences!.first.value
                  : '',
          awardedReward: awardedReward,
        );
      }).toList();

      emit(
        state.copyWith(
          rewardKey: rewardKey,
          allCodes: cards,
          archivedCount: 0,
          isLoading: false,
        ),
      );
    } catch (e) {
      if (e is NoConnectionFailure) {
        uiEventDelegate?.logException(
          exceptionType: e.errorType.toString(),
          exceptionMessage: e.message.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onLoadVoucherCardItemsEvent',
          severity: ExceptionSeverity.High.name,
          stackTrace: e.stackTrace.toString(),
        );
        emit(
            state.copyWith(errorMessage: VGResourceBundleKey.networkError.key));
      } else {
        uiEventDelegate?.logException(
          exceptionType: e.runtimeType.toString(),
          exceptionMessage: e.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onLoadVoucherCardItemsEvent',
          severity: ExceptionSeverity.High.name,
        );
        emit(state.copyWith(isLoading: false, errorMessage: ""));
      }
    }
  }

  void _onLoadArchivedCodesEvent(
    LoadArchivedCodesEvent event,
    Emitter<RewardCodesHistoryState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    try {
      final repository = VGLocator.get<CodesHistoryRepository>(
        instanceName: 'rewardsModule',
      );
      final rewardKey = event.rewardKey ?? state.rewardKey; // <-- fallback
      final awardedRewardCards = await repository.getAwardedRewardByPartyId(
        rewardKey: rewardKey,
      );
      if (awardedRewardCards != null) {
        awardedRewardCards.sort((a, b) {
          final firstAwardedDate = DateTime.parse(a.awardedOn);
          final secondAwardedDate = DateTime.parse(b.awardedOn);
          final firstAwardedPercent = a.rewardValue.percent ?? 0.0;
          final secondAwardedPercent = b.rewardValue.percent ?? 0.0;
          // First: awardedOn DESC
          final compareDate = secondAwardedDate.compareTo(firstAwardedDate);
          if (compareDate != 0) return compareDate;
          // Then: percent DESC
          final comparePercent =
              secondAwardedPercent.compareTo(firstAwardedPercent);
          if (comparePercent != 0) return comparePercent;
          // Then: id DESC
          return (b.id ?? 0).compareTo(a.id ?? 0);
        });
      }

      final archivedAwardedRewardCards = awardedRewardCards?.where((element) {
            return element.awardedRewardStatuses.any((status) =>
                status.key == AppConstants.kManuallyArchivedStatusKey);
          }).toList() ??
          [];

      final archivedCards = archivedAwardedRewardCards.map((awardedReward) {
        var rewardValue = awardedReward.rewardValue.percent ?? 0;
        final rewardName = utf8.decode(awardedReward.reward.name.codeUnits);
        String awardedOn = awardedReward.awardedOn;
        return VGCodesCardItem(
          id: "${awardedReward.id}",
          amount: rewardValue.toInt().toString(),
          logo: IconUtil.icon("${awardedReward.reward.key}"),
          brandName: rewardName,
          awardedOn: awardedOn,
          expiryDate: awardedReward.effectiveTo,
          rewardCode:
              (awardedReward.awardedRewardReferences?.isNotEmpty ?? false)
                  ? awardedReward.awardedRewardReferences!.first.value
                  : '',
          awardedReward: awardedReward,
        );
      }).toList();

      emit(
        state.copyWith(
          rewardKey: rewardKey,
          archivedCodes: archivedCards,
          archivedCount: archivedCards.length,
          isLoading: false,
        ),
      );
    } catch (e) {
      if (e is NoConnectionFailure) {
        uiEventDelegate?.logException(
          exceptionType: e.errorType.toString(),
          exceptionMessage: e.message.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onLoadArchivedCodesEvent',
          severity: ExceptionSeverity.High.name,
          stackTrace: e.stackTrace.toString(),
        );
        emit(
            state.copyWith(errorMessage: VGResourceBundleKey.networkError.key));
      } else {
        uiEventDelegate?.logException(
          exceptionType: e.runtimeType.toString(),
          exceptionMessage: e.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onLoadArchivedCodesEvent',
          severity: ExceptionSeverity.High.name,
        );
        emit(state.copyWith(isLoading: false, errorMessage: ""));
      }
    }
  }

  void _onArchiveSelectedCodesEvent(
    ArchiveSelectedCodesEvent event,
    Emitter<RewardCodesHistoryState> emit,
  ) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    try {
      final codesHistoryRepository = VGLocator.get<CodesHistoryRepository>(
        instanceName: 'rewardsModule',
      );

      final updatedCodes = List<VGCodesCardItem>.from(state.allCodes);
      for (var code in updatedCodes) {
        if (event.selectedIds.contains(code.id)) {
          code.isArchived = true;
          code.archiveEnabled = false;
        }
      }
      // Use event.selectedIds instead of state.selectedIds
      await codesHistoryRepository.archiveCodesList(event.selectedIds);
      emit(
        state.copyWith(
          selectedIds: [],
          archivedCount: event.selectedIds.length,
          isLoading: false,
        ),
      );
      // Reset the archive mode to inactive
      add(ToggleArchiveModeEvent(
        archiveMode: ArchiveStatus.inactive,
      ));
      // Refresh the active codes list
      add(LoadActiveCodesEvent(
        rewardKey: state.rewardKey,
      ));
    } catch (e) {
      if (e is NoConnectionFailure) {
        uiEventDelegate?.logException(
          exceptionType: e.errorType.toString(),
          exceptionMessage: e.message.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onArchiveCodesListEvent',
          severity: ExceptionSeverity.High.name,
          stackTrace: e.stackTrace.toString(),
        );
        emit(
          state.copyWith(
            selectedIds: [],
            archivedCount: 0,
            errorMessage: VGResourceBundleKey.networkError.key,
            isLoading: false,
          ),
        );
      } else {
        uiEventDelegate?.logException(
          exceptionType: e.runtimeType.toString(),
          exceptionMessage: e.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onArchiveCodesListEvent',
          severity: ExceptionSeverity.High.name,
        );
        emit(
          state.copyWith(
            selectedIds: [],
            archivedCount: 0,
            errorMessage: "",
            isLoading: false,
          ),
        );
      }
    }
  }

  Future<void> _onToggleArchiveModeEvent(
    ToggleArchiveModeEvent event,
    Emitter<RewardCodesHistoryState> emit,
  ) async {
    emit(state.copyWith(archiveMode: event.archiveMode));
  }

  Future<void> _onSetArchiveButtonStateEvent(
    SetArchiveButtonStateEvent event,
    Emitter<RewardCodesHistoryState> emit,
  ) async {
    emit(state.copyWith(isArchiveButtonEnabled: event.isEnabled));
  }

  void _onUpdateCodesListEvent(
      UpdateCodesListEvent event, Emitter<RewardCodesHistoryState> emit) {
    emit(state.copyWith(allCodes: event.updatedCodes));
  }

  void _onArchiveSingleCodeEvent(ArchiveSingleCodeEvent event,
      Emitter<RewardCodesHistoryState> emit) async {
    emit(state.copyWith(isLoading: true, errorMessage: null));
    try {
      final codesHistoryRepository = VGLocator.get<CodesHistoryRepository>(
        instanceName: 'rewardsModule',
      );
      await codesHistoryRepository.archiveCodesList([event.selectedCodeId]);
      // Refresh the active codes list
      if (state.rewardKey != null) {
        add(LoadActiveCodesEvent(rewardKey: state.rewardKey));
      }
      emit(
        state.copyWith(
          isLoading: false,
          errorMessage: null,
          archivedCount: [event.selectedCodeId].length,
        ),
      );
    } catch (e) {
      if (e is NoConnectionFailure) {
        uiEventDelegate?.logException(
          exceptionType: e.errorType.toString(),
          exceptionMessage: e.message.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onArchiveCodesListEvent',
          severity: ExceptionSeverity.High.name,
          stackTrace: e.stackTrace.toString(),
        );
        emit(
          state.copyWith(
            selectedIds: [],
            archivedCount: 0,
            errorMessage: VGResourceBundleKey.networkError.key,
            isLoading: false,
          ),
        );
      } else {
        uiEventDelegate?.logException(
          exceptionType: e.runtimeType.toString(),
          exceptionMessage: e.toString(),
          customUserTag:
              'Rewards-RewardCodesHistoryBloc: onArchiveCodesListEvent',
          severity: ExceptionSeverity.High.name,
        );
        emit(
          state.copyWith(
            selectedIds: [],
            archivedCount: 0,
            errorMessage: "",
            isLoading: false,
          ),
        );
      }
    }
  }
}
