import 'package:flutter/material.dart';
import 'package:platform_core/platform_core.dart';
import 'package:rewards_journey/src/common/widgets/widgets.dart';
import 'package:rewards_journey/src/resources/resources.dart';

class RewardCodesEmptyComponent extends StatelessWidget {
  const RewardCodesEmptyComponent({
    super.key,
    required this.textBundleKey,
    required this.subTextBundleKey,
  });

  final String textBundleKey;
  final String subTextBundleKey;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(
          left: 16.0, right: 16.0, top: 16.0, bottom: 32.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CustomImageWidget(
            Images.discountCode,
            width: 120,
            height: 120,
            placeHolder: Assets.images.placeholder.keyName,
            noImagePlaceholder: Assets.images.noImagePlaceholder.keyName,
          ),
          // Top Label
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: CustomTextWidget(
              textBundleKey,
              style: VGApplicationTheme.typography.pLarge,
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: 24),
            child: CustomTextWidget(
              subTextBundleKey,
              style: VGApplicationTheme.typography.pSmall,
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
