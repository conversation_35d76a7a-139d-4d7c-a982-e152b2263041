name: "rewards_journey"
description: "Module repository for GTB Project."
version: 0.0.123
homepage: https://github.com/discovery-ltd/v1-rewards-journey-flutter
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=2.17.0"
dependencies:
  flutter:
    sdk: flutter
  json_annotation: ^4.4.0
  flutter_modular: ^5.0.3
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  # package_info_plus: ^4.0.2
  url_launcher: ^6.1.11
  go_router: ^10.1.2
  # injectable: ^2.3.1
  flutter_timezone: ^1.0.7
  # connectivity_plus: ^6.1.0
  intl: ^0.18.1
  http: 1.1.0
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.246
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.71
  gutenberg_shared_package:
    git:
      path: gutenberg_shared_package
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      ref: 0.0.85
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  manage_events_micro_service_sdk:
    git:
      path: manage_events_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-events-micro-service-sdk-flutter.git
      ref: main
  manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-party-information-micro-service-sdk-flutter.git
      path: manage_party_information_micro_service_sdk
      ref: main
  manage_user_micro_service_sdk:
    git:
      path: manage_user_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-user-micro-service-sdk-flutter.git
      ref: main
  manage_vitality_points_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-points-micro-service-sdk-flutter.git
      path: manage_vitality_points_micro_service_sdk
      ref: main
  manage_vitality_status_micro_service_sdk:
    git:
      path: manage_vitality_status_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-vitality-status-micro-service-sdk-flutter.git
      ref: main
  party_party_information_services_micro_service_sdk:
    git:
      path: party_party_information_services_micro_service_sdk
      url: **************:discovery-ltd/v1-party-party-information-services-micro-service-sdk-flutter.git
      ref: main
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.150
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.77
  integration_platform_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-integration-platform-services-micro-service-sdk-flutter.git
      path: integration_platform_services_micro_service_sdk
      ref: main
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  mockito: ^5.4.4
  v2_manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-rewards-micro-service-sdk-flutter.git
      path: v2_manage_rewards_micro_service_sdk
      ref: main
  flutter_styled_toast: ^2.2.1
dependency_overrides:
  app_settings: 5.1.1
  # 3rd party dependencies-----------------
  flutter_secure_storage: ^9.0.0
  http: 1.1.0
  intl: ^0.18.1
  # project dependencies-------------------
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.134
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.71
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.246
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.150
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.486
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.542
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.52
  gutenberg_shared_package:
    git:
      path: gutenberg_shared_package
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      ref: 0.0.85
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.145
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.7
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  pull_to_refresh_flutter3: ^2.0.2
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.17
      path: vg_framework_contracts
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.32
  http_manager:
    git:
      url: **************:discovery-ltd/v1-http-manager-flutter.git
      path: http_manager
      ref: 0.0.18
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  v2_manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-rewards-micro-service-sdk-flutter.git
      path: v2_manage_rewards_micro_service_sdk
      ref: main
  flutter_styled_toast: ^2.2.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: 2.4.13
  analyzer: ^5.13.0
  injectable_generator: ^2.4.1
  bloc_test: ^9.1.7
  mocktail: ^1.0.3
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
    - assets/css/T9998-Android-Styling.css
    - assets/css/T9998-iOS-Styling.css
    - assets/test_data.json
