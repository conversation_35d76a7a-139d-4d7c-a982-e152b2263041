# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.123] - 2025-08-20

* [DFCT0049313] - Status list not coming issue fix 

## [v0.0.122] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v0.0.121] - 2025-08-19

* [DFCT0049313] - Remove loader 

## [v0.0.120] - 2025-08-11

* [DFCT0049148] - Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn.rewardValue.percent 

## [v0.0.119] - 2025-08-07

* [SFSTRY0107013] - add semantic label to reveal_code 

## [v0.0.118] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v0.0.117] - 2025-08-05

* [DFCT0049314] - Update arguments that we passed in terms 

## [v0.0.116] - 2025-07-31

* No notable changes on this release version. 

## [v0.0.115] - 2025-07-31

* [DFCT0049202] - refactor code

* [DFCT0049186] - Updated semantics issue on VGtoast

* [DFCT0049202] - AccelQ_Adidas_Incorrect ordinal index for element matching value "12" using regex ^(0?[0-9]|1[0-2])$

## [v0.0.114] - 2025-07-31

* [DFCT0049176] - Update resource bundle version and date format in phrase

* [DFCT0049186] - added semantics to VGAlert.showToast

* [DFCT0049148] - Rewards sorting vouchers list by awardedOn date

* [DFCT0049148] - Rewards sorting vouchers list by awardedOn date 

## [v0.0.113] - 2025-07-29

* [SFSTRY0110877] - Copy the discount code 

## [v0.0.112] - 2025-07-29

* [DFCT0049107] - Add VGCircularLoadingIndicator 

## [v0.0.111] - 2025-07-29

* [SFSTRY0107013] - Update jsonContentView after completion state 

## [v0.0.110] - 2025-07-28

* [SFSTRY0111193] - Rewards Update code details screen when click on available and archive cards 

## [v0.0.109] - 2025-07-28

* [SFSTRY0110877] - Update reveal count 

## [v0.0.108] - 2025-07-25

* [SFSTRY0111193] - Rewards vouchers codes list API integrated 

## [v0.0.107] - 2025-07-25

* [SFSTRY0110877] - Integrate revealcode MultiRewardExchange API 

## [v0.0.106] - 2025-07-24

* [SFSTRY0107013] - count of voucher 

## [v0.0.105] - 2025-07-03

* [SFSTRY0107017] - Update pod file SDK version 

## [v0.0.104] - 2025-07-03

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v0.0.103] - 2025-07-02

* [SFSTRY0110877] - migrate reward micro service to V2 

## [v0.0.102] - 2025-06-30

* [SFSTRY0109290] - Update test cases

* [SFSTRY0109290] - Refactor, remove unnecessary code 

## [v0.0.101] - 2025-06-27

* [SFSTRY0113807] - Create History code details screen 

## [v0.0.100] - 2025-06-27

* [SFSTRY0111193] - Rewards vouchers codes list archive API integrated 

## [v0.0.99] - 2025-06-25

* [SFSTRY0113310] - Update revealCode UI 

## [v0.0.98] - 2025-06-25

* [SFSTRY0113310] - Update revealCode UI 

## [v0.0.97] - 2025-06-24

* [SFSTRY0111193] - code refactor 

## [v0.0.96] - 2025-06-24

* [SFSTRY0113307] - Rewards - updated UI screens for codes history in adidas

* [SFSTRY0113307] - Rewards - updated UI screens for codes history in adidas 

## [v0.0.95] - 2025-06-23

* [SFSTRY0109290] - Integrate getAwardedRewardByPartyId API 

## [v0.0.94] - 2025-06-17

* [SFSTRY0109290] - For Adidas landing page API Integration 

## [v0.0.93] - 2025-06-12

* [SFSTRY0111193] - Add Default state 

## [v0.0.92] - 2025-06-11

* [SFSTRY0112069] - rewards partner update phrase keys and refactor code 

## [v0.0.91] - 2025-06-10

* [SFSTRY0112069] - rewards partner update phrase keys and refactor code 

## [v0.0.90] - 2025-06-10

* [SFSTRY0112390] -  Adidas reward Landing, completion state 

## [v0.0.89] - 2025-06-09

* [SFSTRY0112380] - rewards partner lear more about rules screen

* [SFSTRY0112380] - rewards partner lear more about rules screen

* [SFSTRY0112380] - rewards partner lear more about rules screen 

## [v0.0.88] - 2025-06-04

* [SFSTRY0107017] - Add revealCode UI 

## [v0.0.87] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement changes for CTA button 

## [v0.0.86] - 2025-05-30

* [SFSTRY0111535] - Archive functionality UI 

## [v0.0.85] - 2025-05-27

* [SFSTRY0107017] - code refactor and test_app rename

* [SFSTRY0107017] - code refactor and test_app rename 

## [v0.0.84] - 2025-05-27

* [SFSTRY0107017] - Add Static HA complete and update login module 

## [v0.0.83] - 2025-05-27

* [SFSTRY0107718] - make reward page generic 

## [v0.0.82] - 2025-05-26

* [SFSTRY0107018] - Rewards Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0110457] - Rewards - Added button segmented picker in reward codes history screen with UI test cases

* [SFSTRY0110457] - Rewards - Added button segmented picker in reward codes history screen 

## [v0.0.81] - 2025-05-23

* [SFSTRY0107718] - OutletContent Builder, feedcard extension changes

* [SFSTRY0106993] - dynamic card 15 may 

## [v0.0.80] - 2025-05-21

* [SFSTRY0107017] - Update gradle file

* [SFSTRY0107017] - Add revealCode UI

* [SFSTRY0107017] - Add revealCode UI

* [SFSTRY0106993] - iOS build issue 

## [v0.0.79] - 2025-05-08

* [SFSTRY0106993] - iOS build issue

* [SFSTRY0106993] - iOS build issue

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases-Not Eligible

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases-Eligible

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases

* [SFSTRY0106993] - Update adidas landing page

* [SFSTRY0106993] - Rewards - Added all the dummy extensions and jsoncontentview in adidas landing page

* [SFSTRY0106993] - Rewards - Adidas landing page with checking VHR completed or not 

## [v0.0.78] - 2025-03-24

* [SFSTRY0104866] - Rewards - Libraries Versions Upgraded 

## [v0.0.77] - 2025-03-17

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.76] - 2025-03-12

* [SFSTRY0104866] - Rewards - Update CDN Version 

## [v0.0.75] - 2025-03-07

* [SFSTRY0104866] - Rewards - Flutter version upgrade New Changes:

## [v0.0.123] - 2025-08-20

* [DFCT0049313] - Status list not coming issue fix 

## [v0.0.122] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v0.0.121] - 2025-08-19

* [DFCT0049313] - Remove loader 

## [v0.0.120] - 2025-08-11

* [DFCT0049148] - Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn.rewardValue.percent 

## [v0.0.119] - 2025-08-07

* [SFSTRY0107013] - add semantic label to reveal_code 

## [v0.0.118] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v0.0.117] - 2025-08-05

* [DFCT0049314] - Update arguments that we passed in terms 

## [v0.0.116] - 2025-07-31

* No notable changes on this release version. 

## [v0.0.114] - 2025-07-31

* [DFCT0049176] - Update resource bundle version and date format in phrase

* [DFCT0049186] - added semantics to VGAlert.showToast

* [DFCT0049148] - Rewards sorting vouchers list by awardedOn date

* [DFCT0049148] - Rewards sorting vouchers list by awardedOn date 

## [v0.0.113] - 2025-07-29

* [SFSTRY0110877] - Copy the discount code 

## [v0.0.112] - 2025-07-29

* [DFCT0049107] - Add VGCircularLoadingIndicator 

## [v0.0.111] - 2025-07-29

* [SFSTRY0107013] - Update jsonContentView after completion state 

## [v0.0.110] - 2025-07-28

* [SFSTRY0111193] - Rewards Update code details screen when click on available and archive cards 

## [v0.0.109] - 2025-07-28

* [SFSTRY0110877] - Update reveal count 

## [v0.0.108] - 2025-07-25

* [SFSTRY0111193] - Rewards vouchers codes list API integrated 

## [v0.0.107] - 2025-07-25

* [SFSTRY0110877] - Integrate revealcode MultiRewardExchange API 

## [v0.0.106] - 2025-07-24

* [SFSTRY0107013] - count of voucher 

## [v0.0.105] - 2025-07-03

* [SFSTRY0107017] - Update pod file SDK version 

## [v0.0.104] - 2025-07-03

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v0.0.103] - 2025-07-02

* [SFSTRY0110877] - migrate reward micro service to V2 

## [v0.0.102] - 2025-06-30

* [SFSTRY0109290] - Update test cases

* [SFSTRY0109290] - Refactor, remove unnecessary code 

## [v0.0.101] - 2025-06-27

* [SFSTRY0113807] - Create History code details screen 

## [v0.0.100] - 2025-06-27

* [SFSTRY0111193] - Rewards vouchers codes list archive API integrated 

## [v0.0.99] - 2025-06-25

* [SFSTRY0113310] - Update revealCode UI 

## [v0.0.98] - 2025-06-25

* [SFSTRY0113310] - Update revealCode UI 

## [v0.0.97] - 2025-06-24

* [SFSTRY0111193] - code refactor 

## [v0.0.96] - 2025-06-24

* [SFSTRY0113307] - Rewards - updated UI screens for codes history in adidas

* [SFSTRY0113307] - Rewards - updated UI screens for codes history in adidas 

## [v0.0.95] - 2025-06-23

* [SFSTRY0109290] - Integrate getAwardedRewardByPartyId API 

## [v0.0.94] - 2025-06-17

* [SFSTRY0109290] - For Adidas landing page API Integration 

## [v0.0.93] - 2025-06-12

* [SFSTRY0111193] - Add Default state 

## [v0.0.92] - 2025-06-11

* [SFSTRY0112069] - rewards partner update phrase keys and refactor code 

## [v0.0.91] - 2025-06-10

* [SFSTRY0112069] - rewards partner update phrase keys and refactor code 

## [v0.0.90] - 2025-06-10

* [SFSTRY0112390] -  Adidas reward Landing, completion state 

## [v0.0.89] - 2025-06-09

* [SFSTRY0112380] - rewards partner lear more about rules screen

* [SFSTRY0112380] - rewards partner lear more about rules screen

* [SFSTRY0112380] - rewards partner lear more about rules screen 

## [v0.0.88] - 2025-06-04

* [SFSTRY0107017] - Add revealCode UI 

## [v0.0.87] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement changes for CTA button 

## [v0.0.86] - 2025-05-30

* [SFSTRY0111535] - Archive functionality UI 

## [v0.0.85] - 2025-05-27

* [SFSTRY0107017] - code refactor and test_app rename

* [SFSTRY0107017] - code refactor and test_app rename 

## [v0.0.84] - 2025-05-27

* [SFSTRY0107017] - Add Static HA complete and update login module 

## [v0.0.83] - 2025-05-27

* [SFSTRY0107718] - make reward page generic 

## [v0.0.82] - 2025-05-26

* [SFSTRY0107018] - Rewards Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0107018] - Rewards - Codes history screen with UI test cases

* [SFSTRY0110457] - Rewards - Added button segmented picker in reward codes history screen with UI test cases

* [SFSTRY0110457] - Rewards - Added button segmented picker in reward codes history screen 

## [v0.0.81] - 2025-05-23

* [SFSTRY0107718] - OutletContent Builder, feedcard extension changes

* [SFSTRY0106993] - dynamic card 15 may 

## [v0.0.80] - 2025-05-21

* [SFSTRY0107017] - Update gradle file

* [SFSTRY0107017] - Add revealCode UI

* [SFSTRY0107017] - Add revealCode UI

* [SFSTRY0106993] - iOS build issue 

## [v0.0.79] - 2025-05-08

* [SFSTRY0106993] - iOS build issue

* [SFSTRY0106993] - iOS build issue

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases-Not Eligible

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases-Eligible

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases

* [SFSTRY0106993] - Rewards - Adidas Landing screen-> added widget test cases

* [SFSTRY0106993] - Update adidas landing page

* [SFSTRY0106993] - Rewards - Added all the dummy extensions and jsoncontentview in adidas landing page

* [SFSTRY0106993] - Rewards - Adidas landing page with checking VHR completed or not 

## [v0.0.78] - 2025-03-24

* [SFSTRY0104866] - Rewards - Libraries Versions Upgraded 

## [v0.0.77] - 2025-03-17

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.76] - 2025-03-12

* [SFSTRY0104866] - Rewards - Update CDN Version  added module screen in test_app 

## [v0.0.74] - 2025-03-07

* [SFSTRY0104866] - Rewards - Flutter version upgrade and CDN updated 

## [v0.0.73] - 2025-03-04

* [DFCT0044335] - updating session manager version 

## [v0.0.72] - 2025-03-04

* [SFSTRY0094159] - Rewards - Flutter version upgrade 

## [v0.0.71] - 2025-02-14

* [DFCT0041011][Gutenberg][Reward - In rewards added agree and disagree buttons and added dynamic privacy page] (#83)
* [DFCT0041011] -  in rewards added agree and disagree buttons and added dynamic privacy page

* [DFCT0041011] - updated testcases

* [DFCT0041011] - created view for agree-disagree buttons 

## [v0.0.70] - 2025-02-13

* [SFSTRY0099373] - Rewards - Replace Preferences with Session Manager

* [SFSTRY0099373] - Rewards - Replace Preferences with Session Manager 

## [v0.0.69] - 2025-02-10

* [DFCT0043665] - fixing status rewards navigation updated module versions and added ios folder. 

## [v0.0.68] - 2025-02-06

* [SFSTRY0094205] - Rewards - Error Handling for VHC journey with Dynatrace (Rework) 

## [v0.0.67] - 2025-02-04

* [DFCT0041011] - Reward: Renaissance Online Lessons: Issue of not transitioning to partner site リワード：ルネサンス　オンラインレッスン：パートナーサイトに遷移しない[SLI GTB][MKT UAT][Android/iOS] 

## [v0.0.66] - 2025-01-21

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.65] - 2024-11-25

* [DFCT0041033] - pagename fixes for content 

## [v0.0.64] - 2024-11-08

* [SFSTRY0096758] - wrap routes with VGInheritedTopLevelWidget 

## [v0.0.63] - 2024-11-06

* [DFCT0040308] - Semantics issue on data sharing policy because of InAppWebView revert 

## [v0.0.62] - 2024-11-06

* [DFCT0037863] Use statusTypeName data object for localized contents 

## [v0.0.61] - 2024-11-05

* [DFCT0040308] - Semantics issue on data sharing policy because of InAppWebView 

## [v0.0.60] - 2024-10-29

* [SFSTRY0093541] - Update Sizebox 

## [v0.0.59] - 2024-10-29

* [SFSTRY0093978] - test case added

* [SFSTRY0093978] - test case added 

## [v0.0.58] - 2024-10-29

* [SFSTRY0093541] - merge

* [SFSTRY0093541] - Add test cases 

## [v0.0.57] - 2024-10-28

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines. 

## [v0.0.56] - 2024-10-28

* [SFSTRY0093405] - fixes issue for back button 

## [v0.0.55] - 2024-10-28

* [SFSTRY0093978] - One Signal Unengaged notification - update tags

* [SFSTRY0093978] - One Signal Unengaged notification - update tags

* [SFSTRY0093978] - One Signal Unengaged notification - update tags

* [SFSTRY0093978] - One Signal Unengaged notification - update tags

* [SFSTRY0093978] - One Signal Unengaged notification - update tags 

## [v0.0.54] - 2024-10-28

* [SFSTRY0093541] - Merge

* [SFSTRY0093541] - Add Test cases 

## [v0.0.53] - 2024-10-25

* [SFSTRY0093541] - Update

* [SFSTRY0093541] - Add Semantic Label

* [SFSTRY0093541] - Add Semantic label in Expedia Discount Code UI 
