name: test_app
description: "Test app for Reward Journey implementation"
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
version: 0.0.123
dependencies:
  # sdk dependencies-----------------------
  flutter:
    sdk: flutter
  # 3rd party dependencies-----------------
  # project dependencies-------------------
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: master
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.307
  manage_vitality_status_micro_service_sdk:
    git:
      path: manage_vitality_status_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-vitality-status-micro-service-sdk-flutter.git
      ref: main
  manage_vitality_points_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-points-micro-service-sdk-flutter.git
      path: manage_vitality_points_micro_service_sdk
      ref: main
  rewards_journey:
    path: ../rewards_journey
  device_info_plus: ^9.1.0
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.150
  flutter_styled_toast: ^2.2.1
dependency_overrides:
  app_settings: 5.1.1
  flutter_secure_storage: ^9.0.0
  dynatrace_flutter_plugin: 3.307.1
  http: 1.1.0
  intl: ^0.18.1
  # project dependencies-------------------

  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.134
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.246
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.486
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.543
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.71
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.52
  gutenberg_shared_package:
    git:
      path: gutenberg_shared_package
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      ref: 0.0.85
  http_util:
    git:
      url: **************:discovery-ltd/v1-http-util-flutter.git
      path: http_util
      ref: 0.0.17
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.145
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  manage_vitality_status_micro_service_sdk:
    git:
      path: manage_vitality_status_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-vitality-status-micro-service-sdk-flutter.git
      ref: main
  integration_platform_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-integration-platform-services-micro-service-sdk-flutter.git
      path: integration_platform_services_micro_service_sdk
      ref: main
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.7
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.150
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.17
      path: vg_framework_contracts
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.132
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  pull_to_refresh_flutter3: ^2.0.2
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.32
  http_manager:
    git:
      url: **************:discovery-ltd/v1-http-manager-flutter.git
      path: http_manager
      ref: 0.0.18
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.128
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.307
  physical_activity_goals:
    git:
      url: **************:discovery-ltd/v1-physical-activity-goals-flutter.git
      path: physical_activity_goals
      ref: 0.0.259
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.77
  manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-rewards-micro-service-sdk-flutter.git
      path: manage_rewards_micro_service_sdk
      ref: main
  v2_manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-rewards-micro-service-sdk-flutter.git
      path: v2_manage_rewards_micro_service_sdk
      ref: main
  flutter_styled_toast: ^2.2.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: 2.4.13
  injectable_generator: ^2.4.1
  flutter_lints: ^2.0.0
  shared_preferences_platform_interface: ^2.2.0
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
