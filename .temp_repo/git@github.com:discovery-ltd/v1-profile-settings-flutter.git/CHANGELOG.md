# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout 

## [v0.0.215] - 2025-08-07

* [SFSTRY0116897][G<PERSON><PERSON>][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#246)
* [SFSTRY0116897][<PERSON><PERSON><PERSON>][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][<PERSON><PERSON><PERSON>][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#245)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] 

## [v0.0.214] - 2025-08-01

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout 

## [v0.0.215] - 2025-08-07

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#246)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#245)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#244)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout 

## [v0.0.215] - 2025-08-07

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#246)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#245)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout 

## [v0.0.215] - 2025-08-07

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#246)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#245)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] 

## [v0.0.213] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Profile and settings- Change app content when language is selected] (#242)
* [SFSTRY0114785][Gutenberg][Profile and settings- implement app language manager]

* [SFSTRY0114785][Gutenberg][Profile and settings- Update dependency and clear points data on language update]

* [SFSTRY0114785][Gutenberg][Profile and settings- test case fix]

* [SFSTRY0114785][Gutenberg][Profile and settings- update framework contracts version]

* [SFSTRY0114785][Gutenberg][Profile and settings- Replaced pointsStatusTypeName with pointsStatusTypeCode to cater for the different locale]

* [SFSTRY0114785][Gutenberg][Profile and settings- Updated analytics version to fix the facebook sdk issue] 

## [v0.0.212] - 2025-07-31

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout 

## [v0.0.215] - 2025-08-07

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#246)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#245)
* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS]

* [SFSTRY0116897][Gutenberg][ProfileNew Changes:

## [v0.0.216] - 2025-08-13

* [SFSTRY0116897] - fix semantics label on logout Settings - Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] 