name: profile_and_settings
description: "A description of your project"
version: 0.0.216
homepage: https://github.com/discovery-ltd/v1-profile-settings-flutter
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  go_router: ^10.1.2
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  injectable: ^2.3.1
  get_it: ^7.6.4
  permission_handler: ^10.2.0
  secure_shared_preferences: ^0.0.4
  dynatrace_flutter_plugin: 3.307.1
  package_info_plus: ^4.2.0
  flutter_html: ^3.0.0-beta.2
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.73
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.152
  party_party_information_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-party-party-information-services-micro-service-sdk-flutter.git
      path: party_party_information_services_micro_service_sdk
      ref: main
  integration_platform_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-integration-platform-services-micro-service-sdk-flutter.git
      path: integration_platform_services_micro_service_sdk
      ref: main
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  event_points_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-event-points-services-micro-service-sdk-flutter.git
      path: event_points_services_micro_service_sdk
      ref: main
  manage_events_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-events-micro-service-sdk-flutter.git
      path: manage_events_micro_service_sdk
      ref: main
  v2_manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-party-information-micro-service-sdk-flutter.git
      path: v2_manage_party_information_micro_service_sdk
      ref: main
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.138
  manage_goals_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-goals-micro-service-sdk-flutter.git
      path: manage_goals_micro_service_sdk
      ref: main
  manage_vitality_points_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-points-micro-service-sdk-flutter.git
      path: manage_vitality_points_micro_service_sdk
      ref: main
  manage_party_core_micro_service_sdk:
    git:
      path: manage_party_core_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-party-core-micro-service-sdk-flutter.git
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.151
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.21
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.299
  intl: ^0.18.1
  cookie_jar: ^4.0.5
  app_settings: 5.1.1
  collection: ^1.17.2
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.133
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.17
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.563
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.14
dependency_overrides:
  intl: 0.19.0
  flutter_secure_storage: ^8.0.0
  http: 1.1.0
  flutter_inappwebview: 6.1.5
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.21
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.299
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.563
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.466
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.152
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.17
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.138
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-asset-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.54
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.84
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.73
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.151
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.18
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.37
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.6
  goals_points_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-goals-points-services-micro-service-sdk-flutter.git
      path: goals_points_services_micro_service_sdk
      ref: main
  physical_activity_goals:
    git:
      url: **************:discovery-ltd/v1-physical-activity-goals-flutter.git
      path: physical_activity_goals
      ref: 0.0.264
  status_journey:
    git:
      url: **************:discovery-ltd/v1-status-journey-flutter.git
      path: status_journey
      ref: 0.0.68
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.62
  how_to_earn_points:
    git:
      url: **************:discovery-ltd/v1-how-to-earn-points-flutter.git
      path: how_to_earn_points
      ref: 0.0.55
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.14
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.14
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.133
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.34
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.19
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
  bloc_test: ^9.1.4
  mockito: ^5.4.2
  modular_test: ^2.0.0
  injectable_generator: ^2.4.1
  shared_preferences_platform_interface: ^2.2.0
  connectivity_plus_platform_interface: ^1.2.4
  plugin_platform_interface: ^2.1.6
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
    - assets/images/
