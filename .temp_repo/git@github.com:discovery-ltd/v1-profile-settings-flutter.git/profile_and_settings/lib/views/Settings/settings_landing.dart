import 'package:flutter/material.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
import 'package:help_faq/src/utils/route_config_key.dart' as help_faq_route;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:platform_core/platform_core.dart';
import 'package:profile_and_settings/utils/constants.dart';
import 'package:profile_and_settings/values/constants.dart';
import 'package:profile_and_settings/views/Settings/settings_lists_model.dart';
import 'package:v1_modular_app_flutter/modular_app.dart';

import '../../blocs/Profile/profile_points_blocks.dart';
import '../../utils/config_key.dart';
import '../../utils/helpers.dart';
import '../../utils/route_config_key.dart';
import '../../values/dimens.dart';
import '../Widgets/custom_text_widget.dart';

class SettingsLanding extends StatefulWidget {
  // final Authentication authentication;
  // final GatewayHealthSDK gatewayHealthSDK;

  const SettingsLanding({
    Key? key,
    // Authentication? authentication,
    // GatewayHealthSDK? gatewayHealthSDK,
  }) :
        // authentication = authentication ??
        //     VGLocator.get<Authentication>(instanceName: 'loginModule'),
        // gatewayHealthSDK = gatewayHealthSDK ??
        //     VGLocator.get<GatewayHealthSDK>(instanceName: 'pagModule'),
        super(key: key);

  @override
  State<SettingsLanding> createState() => _SettingsLandingState();
}

class _SettingsLandingState extends State<SettingsLanding> {
  late ProfilePointBloc bloc;
  bool isProductFeatureLanguageEnabled = false;

  @override
  void initState() {
    // TODO: implement initState
    initBloc();
    initData();
    super.initState();
  }

  Future<void> initData() async {
    await bloc.retrieveProfileVariation();
  }

  void initBloc() {
    bloc = ProfilePointBloc();
  }

  @override
  Widget build(BuildContext context) {
    final VGAppTheme appTheme = Theme.of(context).extension<VGAppTheme>()!;

    var localization =
        VGLocator.get<PlatformCore>().resourceProvider.localization(context)!;

    List<IconData> manageItemsIcons = [];

    List<IconData> generalItemsIcons = [];

    List<SettingsItem> manageItems = [];

    List<SettingsItem> generalItems = [];

    ValueProviderWithArgumentExtension? settingsListExtension;

    return FutureBuilder<bool>(future: <bool>() async {
      try {
        await checkProductFeatureLanguageEnabled(
            languageTypeCode: ProfileConstants.updateLanguageFeatureTypeCode,
            languageProductFeatureKey:
                ProfileConstants.updateLanguageFeatureTypeKey);
        manageItemsIcons = getArrayManageItemsIcons();
        manageItems = await getArrayManageItems(localization);
        generalItemsIcons = getArrayGeneralItemsIcons();
        generalItems = await getArrayGeneralItems(localization);
        settingsListExtension = appHost.extensionRegistry
            .findFirstExtension<ValueProviderWithArgumentExtension>(
          extensionPoint: AppExtensionConstants.profileSettingsListExtension,
        );
        return true;
      } catch (e) {
        vgLogE(
            "SettingsLandingState FutureBuilder future failed", e.toString());
      }
      return false;
    }(), builder: (context, snapshot) {
      return (snapshot.data ?? false)
          ? Scaffold(
              backgroundColor: VGApplicationTheme.colors.base0,
              appBar: AppBar(
                toolbarHeight: 64,
                elevation: 0,
                backgroundColor: Colors.transparent,
                leading: VGBackButton(
                  onTap: () => Navigator.of(context).pop(),
                  backIcon: FontAwesomeIcons.arrowLeft,
                ),
                title: const VGText(""),
                centerTitle: true,
              ),
              body: settingsListExtension == null
                  ? settingsList(
                      localization,
                      appTheme,
                      manageItems,
                      manageItemsIcons,
                      generalItems,
                      generalItemsIcons,
                      context,
                    )
                  : FutureBuilder(
                      future: settingsListExtension?.execute(SettingsLists(
                          manageItemsIcons: manageItemsIcons,
                          generalItemsIcons: generalItemsIcons,
                          manageItems: manageItems,
                          generalItems: generalItems,
                          localization: localization)),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState != ConnectionState.done) {
                          return const Center(
                              child: VGCircularLoadingIndicator());
                        } else {
                          final data = snapshot.data as SettingsLists;

                          return settingsList(
                            localization,
                            appTheme,
                            data.manageItems,
                            data.manageItemsIcons,
                            data.generalItems,
                            data.generalItemsIcons,
                            context,
                          );
                        }
                      },
                    ),
            )
          : const SizedBox.shrink();
    });
  }

  SafeArea settingsList(
      ResourceBundleLocalization localization,
      VGAppTheme appTheme,
      List<SettingsItem> manageItems,
      List<IconData> manageItemsIcons,
      List<SettingsItem> generalItems,
      List<IconData> generalItemsIcons,
      BuildContext context) {
    return SafeArea(
        child: SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Container(
            padding: const EdgeInsets.all(dimen16),
            child: VGText(
              localization.valueOf(
                  key: VGResourceBundleKey.txtsettings_001.key),
              style: appTheme.typography.h3,
            ),
          ),
          Container(
            padding: const EdgeInsets.all(dimen16),
            child: VGText(
              localization.valueOf(
                  key: VGResourceBundleKey.section_manage_001.key),
              style: showHeadingsBold()
                  ? appTheme.typography.h5Bold
                  : appTheme.typography.h4,
            ),
          ),
          ...manageItems
              .map(
                (item) => Visibility(
                  visible: item.visibility,
                  child: VGList(
                    title: item.name,
                    leading: SizedBox(
                      width: dimen24,
                      height: dimen24,
                      child: Center(
                        child: FaIcon(
                          manageItemsIcons[manageItems.indexOf(item)],
                          size: dimen18,
                        ),
                      ),
                    ),
                    onTap: () => {onTapSectionDetails(item.value)},
                  ),
                ),
              )
              .toList(),
          SizedBox.fromSize(
            size: const Size(0, dimen16),
          ),
          Container(
            padding: const EdgeInsets.all(dimen16),
            child: VGText(
              localization.valueOf(
                  key: VGResourceBundleKey.section_general_001.key),
              style: showHeadingsBold()
                  ? appTheme.typography.h5Bold
                  : appTheme.typography.h4,
            ),
          ),
          ...generalItems.map((item) {
            Widget trailingIcon;
            if ((VGLocator.get<PreferenceUtils>().getGlobalRouteConfig()[
                        ConfigKey.isTncWebBreakOutEnabled.name] ??
                    false) &&
                item.name ==
                    localization.valueOf(
                      key: VGResourceBundleKey.txttermsandconditions_001.key,
                    )) {
              trailingIcon = const Icon(
                  FontAwesomeIcons.lightArrowUpRightFromSquare,
                  size: 16.0);
            } else if (item.name ==
                localization.valueOf(
                    key: VGResourceBundleKey.txthelpcentre_001.key)) {
              trailingIcon = bloc.helpCentreWeb
                  ? const Icon(FontAwesomeIcons.arrowUpRightFromSquare,
                      size: 20.0)
                  : const Icon(Icons.chevron_right_rounded, size: 20.0);
            } else {
              trailingIcon =
                  const Icon(Icons.chevron_right_rounded, size: 20.0);
            }
            return Visibility(
              visible: item.visibility,
              child: VGList(
                contentPadding: const EdgeInsets.only(
                    left: dimen16,
                    right: dimen16,
                    top: dimen16,
                    bottom: dimen16),
                key: const ValueKey("Ontaprow"),
                title: item.name,
                trailing: trailingIcon,
                leading: SizedBox(
                  width: dimen24,
                  height: dimen24,
                  child: Center(
                    child: FaIcon(
                      generalItemsIcons[generalItems.indexOf(item)],
                      size: dimen18,
                    ),
                  ),
                ),
                onTap: () => {onTapSectionDetails(item.value)},
              ),
            );
          }),
          const SizedBox(
            height: dimen16,
          ),
          Padding(
            key: const ValueKey("Logout"),
            padding: const EdgeInsets.all(dimen16),
            child: CustomTextWidget(
              VGResourceBundleKey.ctrl_logout_001.key,
              style: appTheme.typography.internalLinkPMedium,
              onTap: () => logoutClicked(context),
            ),
          ),
          const SizedBox(height: dimen16 * 2),
          Visibility(
            visible: true,
            child: Padding(
              padding: const EdgeInsets.all(dimen6),
              child: FutureBuilder<PackageInfo>(
                future: getPackageInfo(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return CustomTextWidget(
                      '${snapshot.data?.appName} ${snapshot.data?.version}',
                      semanticsLabel: "BuildInfo",
                      textAlign: TextAlign.center,
                      style: VGApplicationTheme.typography.footnote.copyWith(
                        color: VGApplicationTheme.colors.base600,
                      ),
                    );
                  } else {
                    return const SizedBox.shrink();
                  }
                },
              ),
            ),
          ),
        ],
      ),
    ));
  }

  List<IconData> getArrayManageItemsIcons() {
    return [
      FontAwesomeIcons.lightWatchFitness,
      FontAwesomeIcons.lightArrowRightToBracket,
      FontAwesomeIcons.lightEnvelope,
      FontAwesomeIcons.lightBell,
      FontAwesomeIcons.lightUserShield,
    ];
    // manageItemsIcons
  }

  List<IconData> getArrayGeneralItemsIcons() {
    return [
      FontAwesomeIcons.lightCircleUser,
      if (isProductFeatureLanguageEnabled) FontAwesomeIcons.lightGlobe,
      FontAwesomeIcons.lightCircleQuestion,
      FontAwesomeIcons.lightShieldCheck,
      FontAwesomeIcons.lightFileLines,
      FontAwesomeIcons.lightFileLines,
      FontAwesomeIcons.lightFileLines,
    ];
  }

  Future<List<SettingsItem>> getArrayManageItems(
      ResourceBundleLocalization localization) async {
    return [
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtappsanddevices_001.key),
          true,
          "/apps_and_devices"),
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtloginpreferences_001.key),
          VGLocator.get<PreferenceUtils>()
                      .getGlobalRouteConfig()['tenant']
                      .toString() ==
                  "9998".toString()
              ? true
              : await bloc.settingVariationEnabled(kLoginPrefConfigurability) ??
                  true,
          "/settings_login_preferences"),
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtemailcommunication_001.key),
          VGLocator.get<PreferenceUtils>()
                      .getGlobalRouteConfig()['tenant']
                      .toString() ==
                  "9998".toString()
              ? true
              : await bloc.settingVariationEnabled(kEmailCommConfigurability) ??
                  false,
          "/settings_emailcomms"),
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtnotifications_001.key),
          true,
          "/settings_notification"),
      SettingsItem(
          localization.valueOf(key: VGResourceBundleKey.txtdatasharing_001.key),
          true,
          "/data_sharing")
    ];
  }

  Future<List<SettingsItem>> getArrayGeneralItems(
      ResourceBundleLocalization localization) async {
    // List<ListItem> generalItems =
    final globalConfig =
        VGLocator.get<PreferenceUtils>().getGlobalRouteConfig();
    return [
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtaccountdetails_001.key),
          true,
          globalConfig[RouteConfigKey.accountDetailsScreen.name]),
      if (isProductFeatureLanguageEnabled)
        SettingsItem(
            localization.valueOf(key: VGResourceBundleKey.txtlanguage_001.key),
            true,
            globalConfig[RouteConfigKey.settingsLanguageSelectionScreen.name]),
      SettingsItem(
          localization.valueOf(key: VGResourceBundleKey.txthelpcentre_001.key),
          true,
          globalConfig[help_faq_route.RouteConfigKey.helpCenterLanding.name]),
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txtprivacypolicy_001.key),
          true,
          globalConfig[RouteConfigKey.privacyPolicyScreen.name]),
      SettingsItem(
          localization.valueOf(
              key: VGResourceBundleKey.txttermsandconditions_001.key),
          true,
          "/terms_and_conditions_screen"),
      SettingsItem(
          localization.valueOf(key: VGResourceBundleKey.txtlegal1_001.key),
          await bloc.settingVariationEnabled(kAdditionalLegal1) ?? false,
          "/legal1"),
      SettingsItem(
          localization.valueOf(key: VGResourceBundleKey.txtlegal2_001.key),
          await bloc.settingVariationEnabled(kAdditionalLegal2) ?? false,
          "/legal2")
    ];
  }

  void onTapSectionDetails(String routePath) async {
    if (routePath == "/terms_and_conditions_screen") {
      if (VGLocator.get<PreferenceUtils>()
              .getGlobalRouteConfig()[ConfigKey.isTncWebBreakOutEnabled.name] ??
          false) {
        String webUrl = VGLocator.get<PreferenceUtils>()
            .getGlobalRouteConfig()['tncWebUrl']
            .toString();
        await HelperMethod.launchInBrowser(
          Uri.parse(webUrl),
        );
      } else {
        VGNavigator.pushNamed(
          routePath,
          extraParameters: {
            "shouldShowAgreeDisagreeSection": false,
            "isFromLogin": false,
          },
        );
      }
    } else if (routePath == "/legal1") {
      String? legalContent =
          await bloc.retrieveConfigureValue(kAdditionalLegal1) ?? "";

      bool isFromWebUrl = false;
      final Uri? uri = Uri.tryParse(legalContent);
      if (uri!.hasAbsolutePath) {
        isFromWebUrl = true;
      }
      String path = VGLocator.get<PreferenceUtils>()
          .getGlobalRouteConfig()[RouteConfigKey.manageContentScreen.name];
      VGNavigator.pushNamed(path, extraParameters: {
        "pageValue": legalContent,
        "pageTitle": "Legal 1",
        "isFromWebUrl": isFromWebUrl
      });
    } else if (routePath == "/legal2") {
      String? legalContent =
          await bloc.retrieveConfigureValue(kAdditionalLegal2) ?? "";

      bool isFromWebUrl = false;
      final Uri? uri = Uri.tryParse(legalContent);
      if (uri!.hasAbsolutePath) {
        isFromWebUrl = true;
      }

      String path = VGLocator.get<PreferenceUtils>()
          .getGlobalRouteConfig()[RouteConfigKey.manageContentScreen.name];
      VGNavigator.pushNamed(path, extraParameters: {
        "pageValue": legalContent,
        "pageTitle": "Legal 2",
        "isFromWebUrl": isFromWebUrl
      });
    } else {
      final settingsTapExtension = appHost.extensionRegistry
          .findFirstExtension<ValueProviderWithArgumentExtension>(
        extensionPoint: AppExtensionConstants.profileSettingsTapExtension,
      );

      final isTapIntercepted =
          settingsTapExtension?.execute(routePath) ?? false;

      if (!isTapIntercepted) {
        VGNavigator.pushNamed(routePath);
      }
    }
  }

  void logoutClicked(BuildContext context) {
    VGBasicDialog.showDialogWithContent(
      context: context,
      titleWidget: CustomTextWidget(
        semanticsLabel: "Dialog Title",
        VGResourceBundleKey.ctrl_logout_001.key,
        style: VGApplicationTheme.typography.h3,
      ),
      contentWidget: CustomTextWidget(
        semanticsLabel: "Dialog Description",
        VGResourceBundleKey.dialog_body_areyouwanttologout_001.key,
        style: VGApplicationTheme.typography.pSmall,
      ),
      actionButtonTextWidget1: CustomTextWidget(
        semanticsLabel: "Dialog Button 1",
        VGResourceBundleKey.dialog_btn2_cancel_001.key,
        style: VGApplicationTheme.typography.cta
            .copyWith(color: VGApplicationTheme.colors.primary),
      ),
      onActionButton1Clicked: (popContext) {
        Navigator.of(popContext).pop();
      },
      actionButtonTextWidget2: CustomTextWidget(
        semanticsLabel: "Dialog Button 2",
        VGResourceBundleKey.dialog_btn1_yeslogout_001.key,
        style: VGApplicationTheme.typography.cta
            .copyWith(color: VGApplicationTheme.colors.primary),
      ),
      onActionButton2Clicked: (popContext) async {
        Navigator.of(popContext).pop();
        await VGLocator.get<GatewayHealthSDK>(instanceName: 'pagModule')
            .logout();
        VGLocator.get<LogOutObserverManager>().logOutObserverManager();
      },
    );
  }

  Future<void> checkProductFeatureLanguageEnabled(
      {required String languageTypeCode,
      int? languageProductFeatureKey}) async {
    final sessionManager = VGLocator.get<PlatformCore>().sessionManager;
    LoginResponse? loginResponse = await sessionManager.getLoginResponse();
    final vitalityMembershipList = loginResponse?.vitalityMembership;
    if ((vitalityMembershipList ?? []).isNotEmpty) {
      final membershipProducts =
          vitalityMembershipList?.first.membershipProducts;
      if ((membershipProducts ?? []).isNotEmpty) {
        final membershipProduct = membershipProducts?.first;
        final productFeatureApplicabilitiesList = membershipProduct
            ?.productFeatureApplicabilities
            ?.where((e) =>
                e.typeCode == languageTypeCode &&
                e.typeKey == languageProductFeatureKey)
            .toList();
        if ((productFeatureApplicabilitiesList ?? []).isNotEmpty) {
          isProductFeatureLanguageEnabled = true;
        }
      }
    }
  }

  bool showHeadingsBold() {
    final showHeadingsBold = VGLocator.get<PreferenceUtils>()
            .getGlobalRouteConfig()[ConfigKey.showHeadingsBold.name] ??
        false;
    return showHeadingsBold;
  }
}
