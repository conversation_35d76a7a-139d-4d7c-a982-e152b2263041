name: test_app
description: "Test app for Profile and Settings Widget"
publish_to: 'none'
version: 0.0.216
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.3
  cupertino_icons: ^1.0.2
  flutter_modular: ^5.0.3
  dynatrace_flutter_plugin: 3.307.1
  get_it: ^7.6.4
  collection: ^1.17.2
  flutter_html: ^3.0.0-beta.2
  profile_and_settings:
    path: ../profile_and_settings
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.301
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.152
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.138
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.151
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.563
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
dependency_overrides:
  intl: 0.19.0
  flutter_secure_storage: ^8.0.0
  http: 1.1.0
  dynatrace_flutter_plugin: 3.307.1
  flutter_inappwebview: 6.1.5
  visibility_detector: ^0.4.0+2
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.301
  physical_activity_goals:
    git:
      url: **************:discovery-ltd/v1-physical-activity-goals-flutter.git
      path: physical_activity_goals
      ref: 0.0.264
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.21
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.73
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.299
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.152
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.563
  goal_streaks_milestones:
    git:
      url: **************:discovery-ltd/v1-goal-streaks-milestones-flutter.git
      path: goal_streaks_milestones
      ref: 0.0.49
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.146
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.466
  status_journey:
    git:
      url: **************:discovery-ltd/v1-status-journey-flutter.git
      path: status_journey
      ref: 0.0.68
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-party-information-micro-service-sdk-flutter.git
      path: manage_party_information_micro_service_sdk
      ref: main
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.123
  landing_pages:
    git:
      url: **************:discovery-ltd/v1-landing-pages-flutter.git
      path: landing_pages
      ref: 0.0.195
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.19
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.128
  giftcards:
    git:
      url: **************:discovery-ltd/v1-giftcards-flutter.git
      path: giftcards
      ref: 0.0.160
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.138
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-asset-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.54
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  manage_party_core_micro_service_sdk:
    git:
      path: manage_party_core_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-party-core-micro-service-sdk-flutter.git
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.151
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.18
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.6
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.37
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.17
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.14
  goals_points_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-goals-points-services-micro-service-sdk-flutter.git
      path: goals_points_services_micro_service_sdk
      ref: main
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.62
  how_to_earn_points:
    git:
      url: **************:discovery-ltd/v1-how-to-earn-points-flutter.git
      path: how_to_earn_points
      ref: 0.0.55
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.14
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.133
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.34
  registration_journey:
    git:
      url: **************:discovery-ltd/v1-registration-journey-flutter.git
      path: registration_journey
      ref: 0.0.214
  v1_feeds_card_builder_ctas:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builder-ctas-flutter.git
      path: v1_feeds_card_builder_ctas
      ref: 0.0.32
  v1_font_awesome_icons_flutter:
    git:
      url: **************:discovery-ltd/v1-font-awesome-icons-flutter.git
      path: v1_font_awesome_icons_flutter
      version: 0.0.8
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: ^2.1.2
  injectable_generator: ^2.4.1
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
