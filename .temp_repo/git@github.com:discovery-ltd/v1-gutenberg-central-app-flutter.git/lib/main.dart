import 'dart:async';
import 'dart:convert';

import 'package:device_cashback/config/device_cashback_routes.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:gutenberg_shared_package/common/pref_keys.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/preferences.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
import 'package:gutenberg_startup_package/utils/route_config_utils.dart';
import 'package:gutenberg_startup_package/utils/theme_utils.dart';
import 'package:gutenberg_startup_package/values/constants.dart';
import 'package:msmr/msmr_initializer.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:physical_activity_goals/blocs/gate_way_sdk_authentication/gate_way_sdk_authentication_cubit.dart';
import 'package:physical_activity_goals/custom_widgets/custom_overlay_loader.dart';
import 'package:physical_activity_goals/domain/repositories/physical_activity_goals_repositories.dart';
import 'package:physical_activity_goals/views/how_to_connect_apps/health_connect_privacy_policy_screen.dart';
import 'package:platform_core/platform_core.dart';
import 'package:v1_journey_commons_flutter/src/data/maintenance_status_repository.dart';
import 'package:v1_journey_commons_flutter/views/maintenance_screen.dart';
import 'package:v1_main_nav_package_flutter/helper/logout_helper.dart';
import 'package:v1_modular_app_flutter/modular_app.dart';
import 'package:v1_modular_app_flutter/modular_app_extensions.dart';
import 'package:v1_ui_event_tracker/v1_ui_event_tracker.dart';

import '/helper/constants.dart';
import '/helper/resource_helper.dart';
import '../bloc/notification_bloc.dart';
import '../bloc/notification_event.dart';
import '../bloc/notification_state.dart';
import 'base_routes_register.dart';
import 'bloc/deeplink/deeplink_bloc.dart';
import 'di/di.dart' as di;
import 'helper/deep_link_helper.dart';
import 'helper/user_preferences_helper.dart';
import 'modules.gen.dart';
import 'providers/providers.dart';

final preferences = Preferences();

Future<void> main() async {
  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      // Modify logger behaviour without VGLocator through Platform Core as the
      // global instance is directly used in some services like RouteConfigUtils.
      VGLog.instance.forceDisableLogs = _shouldForceDisableLogs;

      // On Android 12 and below image_picker has optional Android Photo Picker functionality.
      // Below code is to enable Android Photo Picker for Android 12 and below.
      ImagePickerHelper.initialize();

      await FlutterTimezoneManager().initialize();
      await preferences.init();

      await UserPreferencesHelper.initUserPreferences(
          platformCore: PlatformCoreProvider.instance);

      /// moduleConfigFilePath: mandatory
      /// centralConfigFilePath: optional
      /// marketNewModuleConfigFilePath: optional
      /// marketConfigFilePath: optional
      await RouteConfigUtils().readConfigs(
        omitEnvConfigsFromModules: true,
        moduleConfigFilePath: Constants.moduleRouteConfigFilePath,
        configFilesPath: Constants.moduleConfigFilePath,
        centralRouteConfigFilePath: Constants.centralConfigFilePath,
        marketNewModuleConfigFilePath: Constants.marketNewModuleConfigFilePath,
        marketRouteConfigFilePath: Constants.marketRouteConfigFilePath,
        marketConfigFilePath: Constants.marketConfigFilePath,
      );
      setServiceLocator(VGLocator.serviceLocator);
      registerModules(productModules);
      await di.configureDependencies();
      await initializeModules();
      await moduleInitializationComplete();

      FlutterError.onError = (FlutterErrorDetails details) {
        // ---------------------------------------------------------------------------
        // FlutterError.onError setup
        //
        // Purpose:
        //   - In debug mode, crash immediately on framework errors (e.g., widget tree
        //     misuse like Expanded outside of a Flex) so issues are caught early.
        //   - In release mode, avoid crashing the app directly; instead, send errors
        //     to a crash reporting service.
        //
        // Why:
        //   - In debug builds, Flutter sometimes logs certain layout/render errors
        //     without fully stopping execution, making them easy to miss.
        //   - In release builds, assertions are stripped out, so the same errors can
        //     cause white screens, freezes, or undefined behavior.
        //
        // Notes:
        //   - Wrapped in `kDebugMode` so that throwing the error only happens in debug.
        //   - In release, you should log the error instead of throwing, to prevent
        //     app crashes in production.
        //
        // Example of error caught early:
        //   Incorrect use of ParentDataWidget (Expanded outside of Row/Column/Flex).

        if (kDebugMode) {
          FlutterError.presentError(details);
          assert(() {
            throw details.exception;
          }());
        }
        // ---------------------------------------------------------------------------
        catchUnhandledExceptions(details.exception, details.stack);
      };

      await Firebase.initializeApp();
      FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);

      final resourceHelper = VGLocator.get<ResourceHelper>();
      await AppLanguageManager.instance.initialize(
        currentOrDefaultLocale: resourceHelper.defaultLocale(),
        preferences: preferences,
      );

      runApp(const BaseApp());
      setupSDKLifeCycleChannel();
    },
    catchUnhandledExceptions,
  );
}

void setupSDKLifeCycleChannel() {
  final gateWaySDKAuthenticationCubit =
      VGLocator.get<GateWaySDKAuthenticationCubit>(instanceName: 'pagModule');
  const MethodChannel mainChannel = MethodChannel('main_sdk_lifecycle_channel');
  mainChannel.setMethodCallHandler((call) async {
    debugPrint("main_sdk_lifecycle_channel ${call.method} received");
    switch (call.method) {
      case "onLogin":
        WidgetsBinding.instance.addPostFrameCallback((_) {
          debugPrint("AuthenticationCubit().onNativeSDKLogin(call.arguments);");
          gateWaySDKAuthenticationCubit.logIn('');
        });
        return "User Logged in";

      case "onLogout":
        WidgetsBinding.instance.addPostFrameCallback((_) {
          debugPrint("AuthenticationCubit().onNativeSDKLogout()");
          gateWaySDKAuthenticationCubit.logOut();
        });
        return "User Logged Out";

      case "getJwtToken":
        debugPrint("AuthenticationCubit().getJwtToken()");
        return await gateWaySDKAuthenticationCubit.getJwtToken();

      default:
        debugPrint("Unknown method call: ${call.method}");
    }
  });
}

void catchUnhandledExceptions(Object error, StackTrace? stack) {
  uiEventDelegate?.logException(
    exceptionType: "UNHANDLED_EXCEPTION",
    exceptionMessage: error.toString(),
    severity: "HIGH",
    stackTrace: stack.toString(),
  );
  vgLogE("UNHANDLED_EXCEPTION_MAIN", error.toString());
}

bool get _shouldForceDisableLogs {
  // This is to disable logs in debug builds.
  // Additional conditions can be added here if needed.
  return const bool.fromEnvironment("LOOOKIT_BUILD", defaultValue: false);
}

class BaseApp extends StatefulWidget {
  const BaseApp({Key? key}) : super(key: key);

  @override
  State<BaseApp> createState() => _BaseAppState();
}

class _BaseAppState extends State<BaseApp> with WidgetsBindingObserver {
  late final RouterConfig<Object>? routerConfig;
  late final ValueNotifier<GoRouterState?> pushedRouterStateNotifier;
  final platformCore = VGLocator.get<PlatformCore>();
  final gateWaySDKAuthenticationCubit =
      VGLocator.get<GateWaySDKAuthenticationCubit>(instanceName: 'pagModule');

  final preferenceUtils = VGLocator.get<PreferenceUtils>();
  final resourceHelper = VGLocator.get<ResourceHelper>();
  final eventDispatcher = VGLocator.get<VGEventDispatcher>();
  final ValueNotifier<bool> isMaintenanceEnabled = ValueNotifier(false);

  @override
  void initState() {
    super.initState();
    // Set accessibilityMode based on the global route config
    VGUiLibGlobalUtils.setAccessibilityMode(
        enabled: VGLocator.get<PreferenceUtils>()
                .getGlobalRouteConfig()["accessibilityMode"] ??
            true);
    VGUiLibGlobalUtils.setLoggingDisabled(disabled: _shouldForceDisableLogs);

    WidgetsBinding.instance.addObserver(this);
    pushedRouterStateNotifier = ValueNotifier(null);
    routerConfig = BaseRoutesRegister(pushedRouterStateNotifier).router;

    /// Needs to instantiate UIEventTracker here so that the analytics manager
    /// can capture all of the logs before everything else starts.
    VGLocator.get<UIEventTracker>();

    checkMaintenanceStatus();
    initializeResourceManager();
    initUIEventTracker();
    final notificationBloc = VGLocator.get<NotificationBloc>();
    notificationBloc.add(NotificationInit());
    VGLocator.get<GatewayHealthSDK>(instanceName: "pagModule")
        .logGatewayHealthSdkEvent(_logGatewayHealthSdkEvent);
    VGLocator.get<GatewayHealthSDK>(instanceName: "pagModule")
        .initGatewayHealthSDK(gateWaySDKAuthenticationCubit);
    VGLocator.get<GatewayHealthSDK>(instanceName: "pagModule")
        .setLoggingDisabled(_shouldForceDisableLogs);
    VGLocator.get<GatewayHealthSDK>(instanceName: "pagModule")
        .onLogSignificantEvent(_logSignificantEvent);
    VGLocator.get<GatewayHealthSDK>(instanceName: "pagModule")
        .initializeSession();
    VGLocator.get<LoginObserverManager>(instanceName: 'loginModule')
        .subscribeInit(voidCallback: () async {
      VGLocator.get<PhysicalActivityGoalsRepositories>().gateWaySDKLogin();

      var partyId = await getPartyId();
      var encryptedPartyId = await getEncryptedPartyId();
      if (partyId.isNotEmpty) {
        vgLogD('$runtimeType', "EncryptedId: $partyId");
        final shouldEncryptPartyId = VGLocator.get<PreferenceUtils>()
                .getGlobalRouteConfig()["shouldEncryptPartyId"] ??
            false;

        // decoding partyId for notificationBloc if shouldEncryptPartyId is false
        final notificationPartyId =
            shouldEncryptPartyId ? encryptedPartyId : partyId;
        notificationBloc.add(OnNotificationLogin(partyId: notificationPartyId));
        notificationBloc.add(
            OnNotificationAddTags(tags: {"Party ID": notificationPartyId}));
        VGLocator.get<UIEventTracker>().setTrackingId(encryptedPartyId);
      }

      var tenantId =
          VGLocator.get<PlatformCore>().sessionManager.getTenantId() ?? 0;
      notificationBloc
          .add(OnNotificationAddTags(tags: {"Tenant ID": tenantId.toString()}));

      VGLocator.get<PlatformCore>().appConfig.setTenantId(tenantId);

      vgLogD('$runtimeType', "Party Id: $partyId");
      var env = VGLocator.get<PlatformCore>()
          .userPreferencesProvider
          .getInModularBox<String>(
              name: AppConstants.appsData, key: PrefKeys.kEnvPrefix)
          ?.replaceAll('--', '')
          .toUpperCase();
      VGLocator.get<UIEventTracker>().setEnvironment(env ?? '');
      uiEventDelegate?.onLogEvent(
          name: '', feature: 'BaseApp', type: '', event: "LOGIN_$env");
      VGLocator.get<DeeplinkBloc>()
          .add(OnDeepLinkLandedPreLogin(isForPreLogin: false));
      await checkForLoginObserverApiExtensions();
    });

    VGLocator.get<LogOutObserverManager>().subscribeInit(
        voidCallback: () async {
      uiEventDelegate?.onLogEvent(
          name: '', feature: 'BaseApp', type: '', event: "LOGOUT");
      VGLocator.get<DeeplinkBloc>()
          .add(OnDeepLinkLandedMainScreen(landedMainScreen: false));
      await LogoutHelper.logout();
      await clearCache();
      notificationBloc
          .add(OnNotificationAddTags(tags: {"current_screen": "log_out"}));
    });
    DeepLinkHelper.handleAppLinks();
  }

  void analyticsCallback(Map<String, dynamic> data) {
    var eventKey = data['eventKey'] ?? 'Unknown';
    var eventDataId = data['eventDataId'] ?? 'Unknown';
    var eventDataName = data['eventDataName'] ?? 'Unknown';
    var eventDataValue = data['eventDataValue'] ?? 'Unknown';

    uiEventDelegate?.onLogEvent(
        name: eventDataName,
        feature: eventDataId,
        type: eventKey,
        event: eventDataValue);
  }

  void logoutCallback() {
    VGLocator.get<LogOutObserverManager>().logOutObserverManager();
  }

  Future<void> clearCache() async {
    uiEventDelegate?.onLogEvent(
        name: 'clearCache()',
        feature: 'BaseApp',
        type: '',
        event: 'CLEAR_CACHE',
        value:
            'Performing storageManager.getStorage().clearAll(), userPreferencesProvider.clearAll();, sessionManager.deleteCache()');
    await VGLocator.get<PlatformCore>().storageManager.getStorage().clearAll();
    VGLocator.get<PlatformCore>().userPreferencesProvider.clearAll();
    await VGLocator.get<PlatformCore>().sessionManager.deleteCache();
  }

  /// This method logs significant events to Dynatrace from the GatewayHealthSDK.
  void _logSignificantEvent(TroubleshootingLogEvent? event) {
    if (event != null) {
      var value = 'sdkVer: ${event.metadata['SDK_VERSION'] ?? ''}, ' +
          'partnerApp: ${event.metadata['PARTNER_APP'] ?? ''}, ' +
          'failReason: ${event.metadata['FAILURE_REASON'] ?? ''}, ' +
          'failCode: ${event.metadata['FAILURE_CODE'] ?? ''}, ' +
          'event: ${event.metadata['EVENT'] ?? ''}, ' +
          'subEvent: ${event.metadata['SUB_EVENT'] ?? ''} ' +
          'eventId : ${event.metadata['EVENT_ID'] ?? ''} ' +
          'verOfConfig : ${event.metadata['VERSION_OF_CONFIG'] ?? ''} ' +
          'message : ${event.message}';

      uiEventDelegate?.onLogEvent(
        name: event.level.name,
        feature: 'GatewayHealthSDK',
        type: UIEventType.typeConnectAppOrDevice,
        event: UIEventName.significantEvent,
        value: value,
      );
      vgLogD('[GatewayHealthSDK-Significant-event]', event.toJson().toString());
    }
  }

  void _logGatewayHealthSdkEvent(GatewayHealthSdkLogEvent? event) {
    if (event != null) {
      switch (event.eventType) {
        case GatewayHealthSdkLogEventType.exception:
          uiEventDelegate?.logException(
            exceptionType: 'GatewayHealthSDKException',
            exceptionMessage: event.eventMessage ?? '',
            severity: event.severity ?? 'HIGH',
            stackTrace: event.stackTrace ?? '',
          );
          vgLogE('[GatewayHealthSDKException-${event.eventName}]',
              event.toJson().toString());
        default:
          uiEventDelegate?.onLogEvent(
            name: event.eventName ?? 'GatewayHealthSDKEvent',
            feature: 'GatewayHealthSDK',
            type: UIEventType.typeConnectAppOrDevice,
            event: 'GatewayHealthSDKEvent',
            value: event.eventMessage,
          );
          vgLogD('[GatewayHealthSDKEvent-${event.eventName}]',
              event.eventMessage ?? '');
          break;
      }
    }
  }

  Future<String> getPartyId() async {
    try {
      final sessionExists =
          await platformCore.sessionManager.getLoginResponse() != null;

      bool shouldEncryptPartyId = VGLocator.get<PreferenceUtils>()
              .getGlobalRouteConfig()["shouldEncryptPartyId"] ??
          false;

      return shouldEncryptPartyId && sessionExists
          ? await VGLocator.get<PlatformCore>().getEncryptedID() ?? ""
          : platformCore.sessionManager.getPartyId().toString();
    } catch (e) {
      vgLogE('404', "PartyId or membershipId is missing");
      uiEventDelegate?.onLogEvent(
          name: 'getPartyId()',
          feature: 'BaseApp',
          type: '',
          event: 'EXCEPTION',
          value: 'Exception: $e',
          subEvent: 'Calling LogoutHelper.logout()');
      LogoutHelper.logout();
    }

    return "";
  }

  Future<String> getEncryptedPartyId() async {
    try {
      // Check if the session exists
      final sessionExists =
          await platformCore.sessionManager.getLoginResponse() != null;
      if (!sessionExists) {
        throw Exception("Session does not exist");
      }

      // Determine if encryption is required
      final shouldEncryptPartyId = VGLocator.get<PreferenceUtils>()
              .getGlobalRouteConfig()["shouldEncryptPartyId"] ??
          false;

      if (shouldEncryptPartyId) {
        // Attempt to get encrypted ID from the service
        return await encryptPartyId();
      } else {
        // Perform local encryption
        return _performLocalEncryption();
      }
    } catch (e) {
      // Handle general exceptions
      _logException("PartyId or membershipId is missing", e);
      LogoutHelper.logout();
      return "";
    }
  }

  Future<String> encryptPartyId() async {
    try {
      return await VGLocator.get<PlatformCore>().getEncryptedID() ?? "";
    } catch (e) {
      // Log the exception and fallback to local encryption
      _logException("Failed to get encrypted ID from service", e);
      return _performLocalEncryption();
    }
  }

  String _performLocalEncryption() {
    try {
      final partyId = platformCore.sessionManager.getPartyId().toString();
      return encodeToBase64Url(partyId);
    } catch (e) {
      // Log the exception during local encryption
      _logException("Failed to perform local encryption", e);
      return "";
    }
  }

  String encodeToBase64Url(String input) {
    final bytes = utf8.encode(input);
    return base64Url.encode(bytes); // URL-safe variant
  }

  void _logException(String message, Object exception) {
    vgLogE(message, exception.toString());
    uiEventDelegate?.onLogEvent(
      name: 'getPartyId()',
      feature: 'BaseApp',
      type: '',
      event: 'EXCEPTION',
      value: 'Exception: $exception',
      subEvent: message,
    );
  }

  Future<void> initializeResourceManager() async {
    vgLogD(
        '$runtimeType', "${preferences.getValue(PrefKeys.globalRouteConfig)}");
    String tenantId = preferenceUtils.getGlobalRouteConfig()[Constants.tenant];
    String groupId = preferenceUtils.getGlobalRouteConfig()[Constants.groupId];
    var config = ResourceConfig(tenant: tenantId, groupId: groupId);

    try {
      await VGLocator.get<PlatformCore>().resourceProvider.initConfig(config);
      initTutorialPopups();
    } catch (e) {
      vgLogE('$runtimeType', "===> Error: ${e.toString()}");
      uiEventDelegate?.onLogEvent(
          name: 'initializeResourceManager()',
          feature: 'BaseApp',
          type: '',
          event: 'EXCEPTION',
          value: 'Exception: $e');
    }
  }

  void initTutorialPopups() {
    final extensions = appHost.extensionRegistry
        .findAllExtensions<TutorialPopupExtension>(
            extensionPoint: 'gtb.main.tutorial_popups');
    for (var ext in extensions) {
      final _popups = ext.execute();
      VGLocator.get<VGOverlayService>().initTutorialPopups(_popups);
    }
  }

  void checkResourceBundle() {
    var _cdnHasError =
        VGLocator.get<Preferences>().getValue(PrefKeys.kHasErrorOnCDN) ?? false;

    if (_cdnHasError) {
      VGLocator.get<Preferences>().setValue(
        PrefKeys.kHasErrorOnCDN,
        false,
      );

      vgLogD('$runtimeType',
          'ResourceBundle has error, redirecting to downtime screen');
      VGNavigator.goNamed(Constants.downtimeV2);
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      checkMaintenanceStatus();
    }
  }

  void checkMaintenanceStatus() async {
    final maintenanceService = VGLocator.get<MaintenanceStatusRepository>();
    bool isInMaintenanceMode =
        await maintenanceService.checkMaintenanceStatus();
    if (isInMaintenanceMode != isMaintenanceEnabled.value) {
      isMaintenanceEnabled.value = isInMaintenanceMode;
    }
  }

  Future<void> checkForLoginObserverApiExtensions() async {
    List<AsyncTaskExtension> loginObserverExtensions = appHost.extensionRegistry
        .findAllExtensions<AsyncTaskExtension>(
            extensionPoint: AppExtensionConstants.loginObserverApiExtension);

    List<Future<void>> results = [];

    for (var loginObserverExtension in loginObserverExtensions) {
      try {
        results.add(loginObserverExtension.execute());
      } catch (e) {
        vgLogD('$runtimeType',
            "Exception in loginObserverExtension class ${loginObserverExtension.runtimeType} with name ${loginObserverExtension.name}: ${e.toString()}");
        uiEventDelegate?.onLogEvent(
            name: 'checkForLoginObserverApiExtensions()',
            feature: 'BaseApp',
            type: '',
            event: 'EXCEPTION',
            value: 'Exception: $e');
      }
    }

    await Future.wait(results);
  }

  void initUIEventTracker() async {
    var partyId = await getPartyId();

    vgLogD('$runtimeType', "partyId: $partyId");
    if (partyId.isNotEmpty) {
      VGLocator.get<UIEventTracker>().setTrackingId(partyId);
    }

    var env = VGLocator.get<PlatformCore>()
        .userPreferencesProvider
        .getInModularBox<String>(
            name: AppConstants.appsData, key: PrefKeys.kEnvPrefix)
        ?.replaceAll('--', '')
        .toUpperCase();
    VGLocator.get<UIEventTracker>().setEnvironment(env ?? '');
  }

  @override
  void dispose() {
    pushedRouterStateNotifier.dispose();
    VGLocator.get<DeeplinkBloc>().close();
    VGLocator.get<NotificationBloc>().close();
    isMaintenanceEnabled.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ThemeData _colorTheme() {
      return VGAppTheme.create(colorTheme: ColorTheme());
    }

    final packageInfo = VGLocator.get<PackageInfo>();

    return MultiBlocProvider(
      providers: Providers().getProviders(),
      child: ValueListenableBuilder(
          valueListenable: AppLanguageManager.instance.localeNotifier,
          builder: (context, locale, _) {
            return MaterialApp.router(
              builder: (context, child) {
                final localization = VGLocator.get<PlatformCore>()
                    .resourceProvider
                    .localization(context);

                return MSMRInitializer(
                  dialogTitle: localization?.valueOf(
                        key: VGResourceBundleKey
                            .txtunsafedevice_detected_title_001.key,
                      ) ??
                      "Unknown Title",
                  dialogMessage: localization?.valueOf(
                        key: VGResourceBundleKey
                            .txtunsafedevice_detected_description_001.key,
                        params: [packageInfo.appName],
                      ) ??
                      "Unknown Message",
                  dialogButtonTitle: localization?.valueOf(
                        key: VGResourceBundleKey
                            .txtunsafedevice_detected_ok_001.key,
                      ) ??
                      "Unknown Button",
                  analyticsCallback: analyticsCallback,
                  logoutCallback: logoutCallback,
                  child: BlocListener<NotificationBloc, NotificationState>(
                    listener: (context, state) {
                      if (state is NotificationReady) {
                        vgLogD('$runtimeType', "NotificationReady");
                      }
                      if (state is InAppMessageReceived) {
                        vgLogD('$runtimeType', "InApp Message Received");
                        BlocProvider.of<NotificationBloc>(context).add(
                          OnPresentInAppMessage(
                              context: context, inAppMessageData: state.data),
                        );
                      }
                      if (state is PushNotificationOpened) {
                        vgLogD('$runtimeType', "Push Notification Opened");
                        DeeplinkBloc deeplinkBloc =
                            BlocProvider.of<DeeplinkBloc>(context);
                        deeplinkBloc.add(OnDeepLinkTarget(
                          routeName:
                              state.notification.notificationRoute?.routeName,
                          params: state.notification.additionalData,
                          routeType:
                              state.notification.notificationRoute?.routeType,
                        ));
                      }
                      if (state is InAppMessageClicked) {
                        vgLogD('$runtimeType',
                            "InAppMessageClicked - Action : ${state.result?.action}} Data : ${state.result?.data} URL : ${state.result?.url}");
                      }

                      if (state is OneSignalInAppMessageClicked) {
                        vgLogD('$runtimeType',
                            "OneSignalInAppMessageClicked - Action : ${state.result?.action}} Data : ${state.result?.data} URL : ${state.result?.url}");
                      }
                    },
                    child: SharedWidgetExtensionHost(
                      extensionRegistry: appHost.extensionRegistry,
                      extensionPoint: "globalModels.root",
                      child: BlocListener<DeeplinkBloc, DeeplinkState>(
                        listener:
                            (BuildContext context, DeeplinkState state) async {
                          if (state is DeeplinkSplashShown) {
                            checkResourceBundle();
                            _handleDeepLinks(state, context);
                          }
                        },
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            if (child != null) child,
                            CustomOverlayLoader(),
                            ValueListenableBuilder<bool>(
                              valueListenable: isMaintenanceEnabled,
                              builder: (BuildContext context,
                                  bool isUnderMaintenance,
                                  Widget? builderChild) {
                                return isUnderMaintenance
                                    ? VGMaintenanceScreen()
                                    : SizedBox.shrink();
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
              title: 'Gutenberg',
              debugShowCheckedModeBanner: false,
              routerConfig: routerConfig,
              localizationsDelegates:
                  platformCore.resourceProvider.localizationsDelegates(),
              supportedLocales:
                  platformCore.resourceProvider.supportedLocales(),
              localeResolutionCallback: platformCore.resourceProvider
                  .defaultLocaleCallback(
                      defaultLocale: resourceHelper.defaultLocale()),
              locale: locale,
              theme: _colorTheme().copyWith(useMaterial3: false),
            );
          }),
    );
  }

  Future<void> _handleDeepLinks(
      DeeplinkSplashShown state, BuildContext context) async {
    if (!await DeepLinkHelper.validateDeepLink(
        uri: Uri.parse(state.extraParameters.toString()))) {
      return;
    }
    if (state.landedMainScreen || state.isForPreLogin) {
      try {
        switch (state.routeType) {
          case "tab":
            VGNavigator.goNamed(state.routeName,
                extraParameters: state.extraParameters);
            break;
          case "push":
            if (state.routeName == DeviceCashbackRoutes.partnerRedirect) {
              appHost.serviceLocator
                  .get<PartnerNavigator>()
                  .navigate(url: state.extraParameters.toString());
            } else if (BreakoutTypeExtension.pushNamePattern
                .hasMatch(state.routeName)) {
              VGNavigator.pushNamed(state.routeName,
                  parameter: state.params,
                  extraParameters: state.extraParameters,
                  pathParameter: state.params);
            } else if (BreakoutTypeExtension.pushPathPattern
                .hasMatch(state.routeName)) {
              VGNavigator.push(state.routeName,
                  parameter: state.params,
                  extraParameters: state.extraParameters);
            } else {
              VGNavigator.pushNamed(state.routeName,
                  parameter: state.params,
                  extraParameters: state.extraParameters);
            }
            break;
          case "pushReplacement":
            if (state.routeName == DeviceCashbackRoutes.partnerRedirect) {
              await appHost.serviceLocator
                  .get<PartnerNavigator>()
                  .navigate(
                      url: state.extraParameters.toString(),
                      isForPushReplace: () {
                        final currentContext =
                            VGNavigator.navigatorKey.currentContext;
                        String currentPath = "";
                        if (currentContext != null) {
                          final goRouter = GoRouter.of(currentContext);
                          currentPath = goRouter
                              .routerDelegate.currentConfiguration.fullPath;
                        }
                        return currentPath == state.routeName;
                      }())
                  .then((value) {
                try {
                  if (state.postDeepLinkNavigationRoute.isNotEmpty) {
                    VGNavigator.goNamed(state.postDeepLinkNavigationRoute);
                  }
                } catch (e) {
                  vgLogE('$runtimeType',
                      "Error with routing. Error: ${e.toString()}");
                }
              });
            } else {
              VGNavigator.pushReplacementNamed(state.routeName,
                  parameter: state.params,
                  extraParameters: state.extraParameters);
            }
            break;
        }
      } catch (e) {
        vgLogD('$runtimeType', "Error with routing. Error: ${e.toString()}");
        uiEventDelegate?.onLogEvent(
            name: '_handleDeepLinks()',
            feature: 'BaseApp',
            type: '',
            event: 'EXCEPTION',
            value: 'Exception: $e');
      }

      BlocProvider.of<DeeplinkBloc>(context).add(OnDeepLinkTargetLanded());
    }
  }
}

@pragma("vm:entry-point")
void loadHealthConnectPrivacyPolicy() async {
  WidgetsFlutterBinding.ensureInitialized();
  runApp(HealthConnectPrivacyPolicyScreen());
}
