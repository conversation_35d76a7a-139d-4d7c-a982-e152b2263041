// GENERATED CODE - DO NOT MODIFY BY HAND

// **************************************************************************
// InjectableConfigGenerator
// **************************************************************************

// ignore_for_file: type=lint
// coverage:ignore-file

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:application_configuration_micro_service_sdk/service/ApplicationConfigurationservice.dart'
    as _i353;
import 'package:common_lib/src/utils/environment_switch_observer.dart' as _i508;
import 'package:device_info_plus/device_info_plus.dart' as _i833;
import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart'
    as _i238;
import 'package:get_it/get_it.dart' as _i174;
import 'package:goals_points_services_micro_service_sdk/service/GoalsPointsServicesservice.dart'
    as _i971;
import 'package:gutenberg_shared_package/common/preference_utils.dart' as _i67;
import 'package:gutenberg_shared_package/common/preferences.dart' as _i498;
import 'package:http/http.dart' as _i519;
import 'package:injectable/injectable.dart' as _i526;
import 'package:landing_pages/data/points_rewards_repository.dart' as _i610;
import 'package:landing_pages/data/see_all_cta_repository.dart' as _i98;
import 'package:manage_assessments_micro_service_sdk/service/ManageAssessmentsservice.dart'
    as _i266;
import 'package:manage_benefit_agreement_micro_service_sdk/service/ManageBenefitAgreementservice.dart'
    as _i800;
import 'package:manage_content_micro_service_sdk/service/ManageContentservice.dart'
    as _i672;
import 'package:manage_events_micro_service_sdk/service/ManageEventsservice.dart'
    as _i658;
import 'package:manage_feeds_micro_service_sdk/service/ManageFeedsservice.dart'
    as _i869;
import 'package:manage_goal_configurations_micro_service_sdk/service/ManageGoalConfigurationsservice.dart'
    as _i683;
import 'package:manage_goals_micro_service_sdk/service/ManageGoalsservice.dart'
    as _i547;
import 'package:manage_healthy_foods_services_sdk/service/ManageHealthyFoodsServices.dart'
    as _i949;
import 'package:manage_party_core_micro_service_sdk/service/ManagePartyCoreservice.dart'
    as _i674;
import 'package:manage_party_information_micro_service_sdk/service/ManagePartyInformationservice.dart'
    as _i261;
import 'package:manage_rewards_micro_service_sdk/service/ManageRewardsservice.dart'
    as _i985;
import 'package:manage_user_micro_service_sdk/service/ManageUserservice.dart'
    as _i7;
import 'package:manage_vitality_points_micro_service_sdk/service/ManageVitalityPointsservice.dart'
    as _i29;
import 'package:manage_vitality_products_micro_service_sdk/service/ManageVitalityProductsservice.dart'
    as _i826;
import 'package:manage_vitality_status_micro_service_sdk/service/ManageVitalityStatusservice.dart'
    as _i475;
import 'package:my_health/blocs/health_landing_bloc/service/events_data_service.dart'
    as _i181;
import 'package:my_health/blocs/health_landing_bloc/service/feeds_data_service.dart'
    as _i208;
import 'package:my_health/blocs/health_landing_bloc/service/related_activities_data_service.dart'
    as _i292;
import 'package:my_health/service/vg_manage_events_api_module_flutter.dart'
    as _i644;
import 'package:package_info_plus/package_info_plus.dart' as _i655;
import 'package:partner_unique_number_service_sdk/service/PartnerUniqueNumberService.dart'
    as _i40;
import 'package:party_party_information_services_micro_service_sdk/service/PartyPartyInformationServicesservice.dart'
    as _i998;
import 'package:platform_core/platform_core.dart' as _i832;
import 'package:profile_and_settings/repository/email_communication_repo.dart'
    as _i1062;
import 'package:profile_and_settings/repository/language_selection_repository.dart'
    as _i438;
import 'package:purchase_orchestration_micro_service_sdk/service/PurchaseOrchestrationservice.dart'
    as _i524;
import 'package:v1_feeds_card_builders/feeds_card_builder.dart' as _i442;
import 'package:v1_feeds_rendering_api_module_flutter/v1_feeds_rendering_api_module_flutter.dart'
    as _i822;
import 'package:v1_journey_commons_flutter/src/data/maintenance_status_repository.dart'
    as _i1035;
import 'package:v1_ui_event_tracker/v1_ui_event_tracker.dart' as _i881;
import 'package:v2_manage_goals_micro_service_sdk/service/ManageGoalsservice.dart'
    as _i964;
import 'package:v2_manage_rewards_micro_service_sdk/service/ManageRewardsservice.dart'
    as _i308;
import 'package:v2_manage_user_micro_service_sdk/service/ManageUserservice.dart'
    as _i303;
import 'package:v2_manage_vitality_products_micro_service_sdk/service/ManageVitalityProductsservice.dart'
    as _i463;

import '../bloc/deeplink/deeplink_bloc.dart' as _i275;
import '../bloc/notification_bloc.dart' as _i1015;
import '../helper/application_configuration_helper.dart' as _i280;
import '../helper/content_manager_helper.dart' as _i161;
import '../helper/http_manager_helper.dart' as _i863;
import '../helper/resource_helper.dart' as _i600;
import '../helper/storage_manager_helper.dart' as _i474;
import '../notifications/in_app_message_goal_achieved.dart' as _i591;
import '../notifications/in_app_message_rating_prompt.dart' as _i848;
import '../notifications/in_app_message_status_transition.dart' as _i237;
import 'app_module.dart' as _i460;

extension GetItInjectableX on _i174.GetIt {
// initializes the registration of main-scope dependencies inside of GetIt
  Future<_i174.GetIt> init({
    String? environment,
    _i526.EnvironmentFilter? environmentFilter,
  }) async {
    final gh = _i526.GetItHelper(
      this,
      environment,
      environmentFilter,
    );
    final appModule = _$AppModule();
    await gh.factoryAsync<_i655.PackageInfo>(
      () => appModule.packageInfo,
      preResolve: true,
    );
    gh.factory<_i519.Client>(() => appModule.httpClient);
    gh.lazySingleton<_i238.Dynatrace>(() => appModule.dynatrace);
    gh.lazySingleton<_i832.SessionManager>(() => appModule.sessionManager);
    gh.lazySingleton<_i1015.NotificationBloc>(() => appModule.notificationBloc);
    gh.lazySingleton<_i832.ResourceProvider>(() => appModule.resourceProvider);
    gh.lazySingleton<_i848.InAppMessageRatingPrompt>(
        () => appModule.inAppMessageRatingPrompt);
    gh.lazySingleton<_i237.InAppMessageStatusTransition>(
        () => appModule.inAppMessageStatusTransition);
    gh.lazySingleton<_i591.InAppMessageGoalAchieved>(
        () => appModule.inAppMessageGoalAchieved);
    gh.lazySingleton<_i832.LogOutObserverManager>(
        () => appModule.logOutObserverManager);
    gh.lazySingleton<_i508.EnvironmentSwitchObserver>(
        () => appModule.environmentSwitchObserver);
    gh.lazySingleton<_i832.VGOverlayService>(() => appModule.overlayService);
    gh.lazySingleton<_i833.DeviceInfoPlugin>(() => appModule.deviceInfoPlugin);
    gh.lazySingleton<_i832.PlatformCore>(() => appModule.platformCore);
    gh.lazySingleton<_i832.Authentication>(() => appModule.authentication);
    gh.lazySingleton<_i498.Preferences>(() => appModule.preferences);
    gh.lazySingleton<_i832.NetworkUtils>(() => appModule.networkUtils);
    gh.lazySingleton<_i832.VGEventDispatcher>(() => appModule.eventDispatcher);
    gh.lazySingleton<_i67.PreferenceUtils>(() => appModule.preferenceUtils);
    gh.lazySingleton<_i275.DeeplinkBloc>(() => appModule.deeplinkBloc);
    gh.lazySingleton<_i832.VGBreadcrumbManager>(
        () => appModule.breadcrumbManager);
    gh.lazySingleton<_i1035.MaintenanceStatusRepository>(
        () => appModule.maintenanceStatusRepository);
    gh.lazySingleton<_i832.ContentManager>(() => appModule.contentManager);
    gh.lazySingleton<_i832.ContentStorageManager>(
        () => appModule.contentStorageManager);
    gh.lazySingleton<_i181.EventDataService>(
        () => appModule.manageEventDataService());
    gh.lazySingleton<_i208.FeedsDataService>(
        () => appModule.manageFeedsDataService());
    gh.lazySingleton<_i292.RelatedActivitiesDataService>(
        () => appModule.manageRelatedActivitiesCardService());
    await gh.factoryAsync<_i600.ResourceHelper>(
      () => appModule.resourceHelper(
        gh<_i832.PlatformCore>(),
        gh<_i67.PreferenceUtils>(),
      ),
      preResolve: true,
    );
    await gh.factoryAsync<_i881.UIEventTracker>(
      () => appModule.getUIEventTracker(
        gh<_i832.PlatformCore>(),
        gh<_i67.PreferenceUtils>(),
      ),
      preResolve: true,
    );
    await gh.factoryAsync<_i832.DeviceInfo>(
      () => appModule.deviceInfo(gh<_i833.DeviceInfoPlugin>()),
      preResolve: true,
    );
    await gh.factoryAsync<_i474.StorageManagerHelper>(
      () => appModule.storageManagerHelper(gh<_i832.PlatformCore>()),
      preResolve: true,
    );
    gh.lazySingleton<_i832.UserAgent>(() => appModule.userAgent(
          gh<_i655.PackageInfo>(),
          gh<_i832.DeviceInfo>(),
          gh<_i67.PreferenceUtils>(),
        ));
    gh.lazySingleton<_i658.ManageEventsService>(
        () => appModule.manageEventsService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i881.UIEventTracker>(),
            ));
    gh.lazySingleton<_i353.ApplicationConfigurationService>(
        () => appModule.applicationConfigurationService(
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
              gh<_i832.PlatformCore>(),
            ));
    await gh.factoryAsync<_i863.HttpManagerHelper>(
      () => appModule.httpManagerHelper(
        gh<_i832.PlatformCore>(),
        gh<_i67.PreferenceUtils>(),
        gh<_i832.UserAgent>(),
        gh<_i600.ResourceHelper>(),
      ),
      preResolve: true,
    );
    gh.lazySingleton<_i998.PartyPartyInformationServicesService>(
        () => appModule.partyPartyInformationServicesService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i672.ManageContentService>(
        () => appModule.manageContentService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i7.ManageUserService>(() => appModule.manageUserService(
          gh<_i832.PlatformCore>(),
          gh<_i832.UserAgent>(),
          gh<_i67.PreferenceUtils>(),
        ));
    gh.lazySingleton<_i261.ManagePartyInformationService>(
        () => appModule.managePartyInformationService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i29.ManageVitalityPointsService>(
        () => appModule.manageVitalityPointsService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i985.ManageRewardsService>(
        () => appModule.manageRewardsService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i308.ManageRewardsService>(
        () => appModule.manageRewardsServiceV2(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i826.ManageVitalityProductsService>(
        () => appModule.manageVitalityProductService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i964.ManageGoalsService>(
        () => appModule.manageGoalsService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i303.ManageUserService>(
        () => appModule.v2ManageUserServiceInstance(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i524.PurchaseOrchestrationService>(
        () => appModule.purchaseOrchestrationService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i971.GoalsPointsServicesService>(
        () => appModule.goalsPointsServicesService(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i463.ManageVitalityProductsService>(
        () => appModule.manageVitalityProductServiceV2(
              gh<_i832.PlatformCore>(),
              gh<_i832.UserAgent>(),
              gh<_i67.PreferenceUtils>(),
            ));
    gh.lazySingleton<_i832.PartnerRedirectService>(
        () => appModule.partnerRedirectService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i98.SeeAllCTARepository>(
        () => appModule.seeAllCTARepository(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i266.ManageAssessmentsService>(
        () => appModule.manageAssessmentsService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i547.ManageGoalsService>(
        () => appModule.manageGoalService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i869.ManageFeedsService>(
        () => appModule.manageFeedsService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i683.ManageGoalConfigurationsService>(
        () => appModule.manageGoalConfigurationsService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i822.V1FeedsRenderingAPIModule>(
        () => appModule.manageFeedsRenderingAPIModule(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i644.VGManageEventsAPIModule>(
        () => appModule.manageEventsAPIModule(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i800.ManageBenefitAgreementService>(
        () => appModule.manageBenefitAgreementService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i475.ManageVitalityStatusService>(
        () => appModule.manageVitalityStatusService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i40.PartnerUniqueNumberService>(
        () => appModule.partnerUniqueNumberService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i674.ManagePartyCoreService>(
        () => appModule.managePartyCoreService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.lazySingleton<_i949.ManageHealthyFoodsService>(
        () => appModule.manageHealthyFoodsService(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.factory<_i832.VGFeedsRenderingEngine>(
        () => appModule.vGFeedsRenderingEngine(
              gh<_i832.PlatformCore>(),
              gh<_i67.PreferenceUtils>(),
              gh<_i832.UserAgent>(),
            ));
    gh.factory<_i832.PartnerNavigator>(() => appModule.partnerNavigator(
          gh<_i832.PartnerRedirectService>(),
          gh<_i832.SessionManager>(),
        ));
    await gh.factoryAsync<_i280.ApplicationConfigurationHelper>(
      () => appModule.applicationConfigurationHelper(
          gh<_i353.ApplicationConfigurationService>()),
      preResolve: true,
    );
    gh.lazySingleton<_i442.PointsCategoryRepository>(
        () => appModule.pointsCategoryRepository(
              gh<_i826.ManageVitalityProductsService>(),
              gh<_i498.Preferences>(),
            ));
    gh.lazySingleton<_i1062.PartyInformationServices>(
        () => appModule.partyInformationServices(
              gh<_i67.PreferenceUtils>(),
              gh<_i498.Preferences>(),
              gh<_i998.PartyPartyInformationServicesService>(),
            ));
    await gh.factoryAsync<_i161.ContentManagerHelper>(
      () => appModule.initContentManager(
        gh<_i832.PlatformCore>(),
        gh<_i67.PreferenceUtils>(),
        gh<_i863.HttpManagerHelper>(),
        gh<_i832.UserAgent>(),
      ),
      preResolve: true,
    );
    gh.lazySingleton<_i438.LanguageSelectionPreferenceServices>(
        () => appModule.languageSelectionPrefServices(
              gh<_i67.PreferenceUtils>(),
              gh<_i498.Preferences>(),
              gh<_i674.ManagePartyCoreService>(),
            ));
    gh.lazySingleton<_i610.PointsRewardsRepository>(
        () => appModule.pointsRewardsRepository(
              gh<_i826.ManageVitalityProductsService>(),
              gh<_i29.ManageVitalityPointsService>(),
              gh<_i498.Preferences>(),
              gh<_i985.ManageRewardsService>(),
              gh<_i832.SessionManager>(),
            ));
    return this;
  }
}

class _$AppModule extends _i460.AppModule {}
