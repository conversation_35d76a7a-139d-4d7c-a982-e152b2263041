// ignore_for_file: implementation_imports

import 'dart:io' as io;

import 'package:application_configuration_micro_service_sdk/service/ApplicationConfigurationservice.dart';
import 'package:authentication/authentication.dart';
import 'package:common_lib/src/utils/environment_switch_observer.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:goals_points_services_micro_service_sdk/service/GoalsPointsServicesservice.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/preferences.dart';
import 'package:http/http.dart' as http;
import 'package:injectable/injectable.dart';
import 'package:landing_pages/data/points_rewards_repository.dart';
import 'package:landing_pages/data/see_all_cta_repository.dart';
import 'package:manage_assessments_micro_service_sdk/service/ManageAssessmentsservice.dart';
import 'package:manage_benefit_agreement_micro_service_sdk/service/ManageBenefitAgreementservice.dart';
import 'package:manage_content_micro_service_sdk/service/ManageContentservice.dart';
import 'package:manage_events_micro_service_sdk/service/ManageEventsservice.dart';
import 'package:manage_feeds_micro_service_sdk/service/ManageFeedsservice.dart';
import 'package:manage_goal_configurations_micro_service_sdk/service/ManageGoalConfigurationsservice.dart';
import 'package:manage_goals_micro_service_sdk/service/ManageGoalsservice.dart';
import 'package:manage_healthy_foods_services_sdk/service/ManageHealthyFoodsServices.dart';
import 'package:manage_party_core_micro_service_sdk/service/ManagePartyCoreservice.dart';
import 'package:manage_party_information_micro_service_sdk/service/ManagePartyInformationservice.dart';
import 'package:manage_rewards_micro_service_sdk/service/ManageRewardsservice.dart';
import 'package:manage_user_micro_service_sdk/service/ManageUserservice.dart';
import 'package:manage_vitality_points_micro_service_sdk/service/ManageVitalityPointsservice.dart';
import 'package:manage_vitality_products_micro_service_sdk/service/ManageVitalityProductsservice.dart'
    as productsService;
import 'package:manage_vitality_status_micro_service_sdk/service/ManageVitalityStatusservice.dart';
import 'package:my_health/blocs/health_landing_bloc/service/events_data_service.dart';
import 'package:my_health/blocs/health_landing_bloc/service/feeds_data_service.dart';
import 'package:my_health/blocs/health_landing_bloc/service/related_activities_data_service.dart';
import 'package:my_health/service/vg_manage_events_api_module_flutter.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:partner_unique_number_service_sdk/service/PartnerUniqueNumberService.dart';
import 'package:party_party_information_services_micro_service_sdk/service/PartyPartyInformationServicesservice.dart';
import 'package:platform_core/platform_core.dart';
import 'package:profile_and_settings/repository/email_communication_repo.dart';
import 'package:profile_and_settings/repository/language_selection_repository.dart';
import 'package:purchase_orchestration_micro_service_sdk/service/PurchaseOrchestrationservice.dart';
import 'package:session_manager/session_manager.dart';
import 'package:v1_feeds_card_builders/feeds_card_builder.dart';
import 'package:v1_feeds_rendering_api_module_flutter/v1_feeds_rendering_api_module_flutter.dart';
import 'package:v1_feeds_rendering_engine/v1_feeds_rendering_engine.dart';
import 'package:v1_journey_commons_flutter/src/data/maintenance_status_repository.dart';
import 'package:v1_ui_event_tracker/v1_ui_event_tracker.dart';
import 'package:v2_manage_goals_micro_service_sdk/service/ManageGoalsservice.dart'
    as v2ManageGoalService;
import 'package:v2_manage_user_micro_service_sdk/service/ManageUserservice.dart'
    as v2ManageUserService;
import 'package:v2_manage_vitality_products_micro_service_sdk/service/ManageVitalityProductsservice.dart'
    as v2ProductService;
import 'package:v2_manage_rewards_micro_service_sdk/service/ManageRewardsservice.dart' as v2ManageRewardsService;

import '../bloc/deeplink/deeplink_bloc.dart';
import '../bloc/notification_bloc.dart';
import '../helper/application_configuration_helper.dart';
import '../helper/constants.dart';
import '../helper/content_manager_helper.dart';
import '../helper/http_manager_helper.dart';
import '../helper/resource_helper.dart';
import '../helper/storage_manager_helper.dart';
import '../notifications/in_app_message_goal_achieved.dart';
import '../notifications/in_app_message_rating_prompt.dart';
import '../notifications/in_app_message_status_transition.dart';

@module
abstract class AppModule {
  @lazySingleton
  Dynatrace get dynatrace => Dynatrace();

  @lazySingleton
  SessionManager get sessionManager => SessionManagerImpl.instance;

  @preResolve
  Future<ResourceHelper> resourceHelper(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
  ) {
    return ResourceHelper(
      platformCore: platformCore,
      preferenceUtils: preferenceUtils,
    ).initResourceBundle();
  }

  @preResolve
  Future<PackageInfo> get packageInfo => PackageInfo.fromPlatform();

  @lazySingleton
  PartnerRedirectService partnerRedirectService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return PartnerRedirectService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      'v1',
    );
  }

  @factoryMethod
  PartnerNavigator partnerNavigator(
    PartnerRedirectService service,
    SessionManager sessionManager,
  ) {
    return PartnerNavigator(
      service: service,
      sessionManager: sessionManager,
    );
  }

  @preResolve
  Future<HttpManagerHelper> httpManagerHelper(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
    ResourceHelper resourceHelper,
  ) {
    return HttpManagerHelper().initHttpManager(
      platformCore: platformCore,
      preferenceUtils: preferenceUtils,
      resourceHelper: resourceHelper,
      userAgent: userAgent.toString(),
    );
  }

  @preResolve
  Future<ContentManagerHelper> initContentManager(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    HttpManagerHelper httpManager,
    UserAgent userAgent,
  ) async {
    return ContentManagerHelper().init(
      platformCore,
      preferenceUtils,
      userAgent.toString(),
    );
  }

  @preResolve
  Future<StorageManagerHelper> storageManagerHelper(
    PlatformCore platformCore,
  ) {
    return StorageManagerHelper().initStorageManager(
      platformCore: platformCore,
    );
  }

  @lazySingleton
  NotificationBloc get notificationBloc => NotificationBloc();

  @lazySingleton
  ResourceProvider get resourceProvider => ResourceManagerImpl.instance;

  @lazySingleton
  InAppMessageRatingPrompt get inAppMessageRatingPrompt =>
      InAppMessageRatingPrompt();

  @lazySingleton
  InAppMessageStatusTransition get inAppMessageStatusTransition =>
      InAppMessageStatusTransition();

  @lazySingleton
  InAppMessageGoalAchieved get inAppMessageGoalAchieved =>
      InAppMessageGoalAchieved();

  @lazySingleton
  LogOutObserverManager get logOutObserverManager =>
      LogOutObserverManager.instance;

  @lazySingleton
  EnvironmentSwitchObserver get environmentSwitchObserver =>
      EnvironmentSwitchObserver.instance;

  @lazySingleton
  VGOverlayService get overlayService =>
      VGOverlayService(preferences: preferences);

  @preResolve
  Future<DeviceInfo> deviceInfo(DeviceInfoPlugin deviceInfoPlugin) async {
    var model;
    var osVersion;
    if (io.Platform.isAndroid) {
      var androidInfo = await deviceInfoPlugin.androidInfo;
      model = androidInfo.model;
      osVersion = androidInfo.version.release;
    } else if (io.Platform.isIOS) {
      var iosInfo = await deviceInfoPlugin.iosInfo;
      model = iosInfo.model;
      osVersion = iosInfo.systemVersion;
    }
    return DeviceInfo(model, osVersion);
  }

  @lazySingleton
  DeviceInfoPlugin get deviceInfoPlugin => DeviceInfoPlugin();

  @lazySingleton
  PlatformCore get platformCore => PlatformCoreProvider.instance;

  @lazySingleton
  Authentication get authentication => AuthenticationPlugin.instance;

  @factoryMethod
  http.Client get httpClient => http.Client();

  @lazySingleton
  Preferences get preferences => Preferences()..init();

  @lazySingleton
  NetworkUtils get networkUtils => NetworkUtils.instance;

  @lazySingleton
  VGEventDispatcher get eventDispatcher => VGEventDispatcher();

  @lazySingleton
  PreferenceUtils get preferenceUtils => PreferenceUtils();

  @lazySingleton
  DeeplinkBloc get deeplinkBloc => DeeplinkBloc();

  @lazySingleton
  VGBreadcrumbManager get breadcrumbManager => VGBreadcrumbManager.instance;

  @lazySingleton
  MaintenanceStatusRepository get maintenanceStatusRepository =>
      MaintenanceStatusRepository();

  @lazySingleton
  UserAgent userAgent(
    PackageInfo packageInfo,
    DeviceInfo deviceInfo,
    PreferenceUtils preferenceUtils,
  ) {
    var appConfigIdentifier =
        preferenceUtils.getGlobalRouteConfig()['appConfigIdentifier'];
    var appConfigIdentifierWithOS =
        preferenceUtils.getGlobalRouteConfig()['appConfigIdentifierWithOS'];
    if (null == appConfigIdentifier) {
      appConfigIdentifier = 'VitalityActive';
      uiEventDelegate?.onLogEvent(
          name: 'AppModule',
          feature: 'AppModule',
          type: 'Initialization',
          event: 'MissingConfigEvent',
          value:
              "preferenceUtils.getGlobalRouteConfig()['appConfigIdentifier'] is null. Using VitalityActive as appConfigIdentifier instead.");
    }
    var userAgent = UserAgent(
      appConfigIdentifier: appConfigIdentifier,
      packageInfo: packageInfo,
      deviceInfo: deviceInfo,
      withOS: appConfigIdentifierWithOS,
    );
    vgLogD(
      '$runtimeType',
      userAgent.toString(),
    );
    return userAgent;
  }

  @lazySingleton
  PartyInformationServices partyInformationServices(
          PreferenceUtils preferenceUtils,
          Preferences preferences,
          PartyPartyInformationServicesService service) =>
      PartyInformationServicesImp(
          preferences: preferences,
          partyPartyInformationServicesService: service,
          preferenceUtils: preferenceUtils);

  @lazySingleton
  PartyPartyInformationServicesService partyPartyInformationServicesService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return PartyPartyInformationServicesService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @lazySingleton
  ManageEventsService manageEventsService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
    UIEventTracker eventTracker,
  ) {
    return ManageEventsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
    );
  }

  @lazySingleton
  LanguageSelectionPreferenceServices languageSelectionPrefServices(
          PreferenceUtils preferenceUtils,
          Preferences preferences,
          ManagePartyCoreService service) =>
      LanguageSelectionPreferenceServicesImp(service,
          preferences: preferences, preferenceUtils: preferenceUtils);

  @lazySingleton
  ManageContentService manageContentService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return ManageContentService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  ContentManager get contentManager => ContentManager.instance;

  @lazySingleton
  ContentStorageManager get contentStorageManager =>
      ContentStorageManager.instance;

  @lazySingleton
  ManageUserService manageUserService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return ManageUserService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
    );
  }

  @lazySingleton
  ManagePartyInformationService managePartyInformationService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return ManagePartyInformationService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @lazySingleton
  ManageVitalityPointsService manageVitalityPointsService(
      PlatformCore platformCore,
      UserAgent userAgent,
      PreferenceUtils preferenceUtils) {
    return ManageVitalityPointsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @lazySingleton
  PointsRewardsRepository pointsRewardsRepository(
    productsService.ManageVitalityProductsService manageVitalityProductsService,
    ManageVitalityPointsService manageVitalityPointsService,
    Preferences preferences,
    ManageRewardsService manageRewardsService,
    SessionManager sessionManager,
  ) {
    return PointsRewardsRepositoryImp(
      manageVitalityProductsService: manageVitalityProductsService,
      manageVitalityPointsService: manageVitalityPointsService,
      preferences: preferences,
      manageRewardsService: manageRewardsService,
      sessionManager: sessionManager,
    );
  }

  @lazySingleton
  ManageRewardsService manageRewardsService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return ManageRewardsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "",
    );
  }

  @lazySingleton
  v2ManageRewardsService.ManageRewardsService manageRewardsServiceV2(
      PlatformCore platformCore,
      UserAgent userAgent,
      PreferenceUtils preferenceUtils,
      ) {
    return v2ManageRewardsService.ManageRewardsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "",
    );
  }

  @lazySingleton
  productsService.ManageVitalityProductsService manageVitalityProductService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return productsService.ManageVitalityProductsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @factoryMethod
  VGFeedsRenderingEngine vGFeedsRenderingEngine(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return V1FeedsRenderingEngineImpl(
      client: platformCore.httpManager.v1Client,
      baseUrl: preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent: userAgent.toString(),
      locale: io.Platform.localeName,
      resourceProvider: resourceProvider,
    );
  }

  @lazySingleton
  SeeAllCTARepository seeAllCTARepository(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return SeeAllCTARepositoryImp(
      client: platformCore.httpManager.v1Client,
      baseUrl: preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent: userAgent.toString(),
    );
  }

  @lazySingleton
  ManageAssessmentsService manageAssessmentsService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageAssessmentsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  ManageGoalsService manageGoalService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageGoalsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  v2ManageGoalService.ManageGoalsService manageGoalsService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return v2ManageGoalService.ManageGoalsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  v2ManageUserService.ManageUserService v2ManageUserServiceInstance(
      PlatformCore platformCore,
      UserAgent userAgent,
      PreferenceUtils preferenceUtils) {
    return v2ManageUserService.ManageUserService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '',
      'v1',
    );
  }

  @lazySingleton
  PurchaseOrchestrationService purchaseOrchestrationService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return PurchaseOrchestrationService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  ManageFeedsService manageFeedsService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageFeedsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '',
    );
  }

  @lazySingleton
  ManageGoalConfigurationsService manageGoalConfigurationsService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageGoalConfigurationsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '',
    );
  }

  @lazySingleton
  GoalsPointsServicesService goalsPointsServicesService(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return GoalsPointsServicesService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      'v1',
    );
  }

  @lazySingleton
  PointsCategoryRepository pointsCategoryRepository(
    productsService.ManageVitalityProductsService manageVitalityProductsService,
    Preferences preferences,
  ) {
    return PointsCategoryRepositoryImpl(
      preferences: preferences,
      manageVitalityProductsService: manageVitalityProductsService,
    );
  }

  @lazySingleton
  EventDataService manageEventDataService() => EventDataService();

  @lazySingleton
  FeedsDataService manageFeedsDataService() => FeedsDataService();

  @lazySingleton
  RelatedActivitiesDataService manageRelatedActivitiesCardService() =>
      RelatedActivitiesDataService();

  @lazySingleton
  V1FeedsRenderingAPIModule manageFeedsRenderingAPIModule(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return V1FeedsRenderingAPIModule(
      client: platformCore.httpManager.v1Client,
      baseUrl: preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent: userAgent.toString(),
    );
  }

  @lazySingleton
  VGManageEventsAPIModule manageEventsAPIModule(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return VGManageEventsAPIModule(
      client: platformCore.httpManager.v1Client,
      baseUrl: preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent: userAgent.toString(),
    );
  }

  @lazySingleton
  v2ProductService.ManageVitalityProductsService manageVitalityProductServiceV2(
    PlatformCore platformCore,
    UserAgent userAgent,
    PreferenceUtils preferenceUtils,
  ) {
    return v2ProductService.ManageVitalityProductsService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @lazySingleton
  ManageBenefitAgreementService manageBenefitAgreementService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageBenefitAgreementService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @preResolve
  Future<UIEventTracker> getUIEventTracker(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
  ) async {
    /// Dynatrace will be enabled by default and will be part of the T&C.
    /// The user can then change this through the Settings page.
    String tenantId = preferenceUtils.getGlobalRouteConfig()[Constants.tenant];
    String marketName =
        preferenceUtils.getGlobalRouteConfig()[Constants.marketName];

    /// partyId to be supplied on post-login
    var tracker = UIEventTracker(
        partyId: '',
        client: marketName,
        entity: tenantId,
        //dynatrace: dynatrace,
        isDynatraceEnabled: true,
        isGoogleEnabled: true);
    await tracker.init();

    VGUIEventDelegateManager.setDelegate(tracker);
    return tracker;
  }

  @lazySingleton
  ManageVitalityStatusService manageVitalityStatusService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManageVitalityStatusService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  PartnerUniqueNumberService partnerUniqueNumberService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return PartnerUniqueNumberService(
        platformCore.httpManager.v1Client,
        preferenceUtils.getGlobalRouteConfig()['baseUrl'],
        userAgent.toString(),
        '1.0');
  }

  @lazySingleton
  ManagePartyCoreService managePartyCoreService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) {
    return ManagePartyCoreService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      '1.0',
      '1',
    );
  }

  @lazySingleton
  ApplicationConfigurationService applicationConfigurationService(
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
    PlatformCore platformCore,
  ) {
    return ApplicationConfigurationService(
      platformCore.httpManager.v1Client,
      preferenceUtils.getGlobalRouteConfig()['baseUrl'],
      userAgent.toString(),
      "1.0",
      "1",
    );
  }

  @preResolve
  Future<ApplicationConfigurationHelper> applicationConfigurationHelper(
          ApplicationConfigurationService service) =>
      ApplicationConfigurationHelper().initAppConfig();

  @lazySingleton
  ManageHealthyFoodsService manageHealthyFoodsService(
    PlatformCore platformCore,
    PreferenceUtils preferenceUtils,
    UserAgent userAgent,
  ) =>
      ManageHealthyFoodsService(
        platformCore.httpManager.v1Client,
        preferenceUtils.getGlobalRouteConfig()['baseUrl'],
        userAgent.toString(),
        '1.0',
        '',
      );
}
