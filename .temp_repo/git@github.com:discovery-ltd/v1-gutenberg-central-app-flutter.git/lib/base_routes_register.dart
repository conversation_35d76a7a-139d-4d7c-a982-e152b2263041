// ignore_for_file: implementation_imports

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
import 'package:gutenberg_shared_package/extensions.dart';
import 'package:landing_pages/utils/route_config_key.dart';
import 'package:login_journey/utils/config_key.dart';
import 'package:platform_core/platform_core.dart';
import 'package:v1_modular_app_flutter/modular_app.dart';

import 'bloc/deeplink/deeplink_bloc.dart';
import 'helper/constants.dart';
import 'views/downtime_screen_v2.dart';
import 'views/splash_screen.dart';

class BaseRoutesRegister {
  static const journeyId = "CentralModule";

  const BaseRoutesRegister(this.pushedRouterStateNotifier);

  /// This notifier is used in [ScreenNavigationObserver], providing
  /// GoRouter level state for pushed screen. The notifier is used to read route
  /// metadata relevant for screen navigation event logging.
  final ValueNotifier<GoRouterState?> pushedRouterStateNotifier;

  RouterConfig<Object> get router {
    return GoRouter(
      navigatorKey: VGNavigator.navigatorKey,
      initialLocation: Constants.splashScreen,
      observers: [
        ScreenNavigationObserver(
          pushedRouterStateNotifier,
          logger: VGLocator.get(),
          breadcrumbsManager: VGLocator.get<VGBreadcrumbManager>(),
        ),
        VGOverlayServiceObserver(VGLocator.get<VGOverlayService>()),
      ],
      routes: <RouteBase>[
        GoRoute(
          name: Constants.splashScreen,
          path: Constants.splashScreen,
          pageBuilder: (context, _) => MaterialPage(
            child: VGInheritedTopLevelWidget(
              journeyId: journeyId,
              child: SplashScreen(),
            ),
          ),
        ),
        GoRoute(
          name: Constants.downtimeV2,
          path: Constants.downtimeV2,
          pageBuilder: (context, _) => MaterialPage(
            child: VGInheritedTopLevelWidget(
              journeyId: journeyId,
              child: DowntimeScreenV2(),
            ),
          ),
        ),
        ...getModuleRoutes(),
      ].setNavigatorKeys(),
      redirect: (context, state) {
        pushedRouterStateNotifier.value = state;

        final preferenceUtils = VGLocator.get<PreferenceUtils>();
        final path =
            preferenceUtils.getGlobalRouteConfig()[ConfigKey.has2FAEnabled.name]
                ? preferenceUtils
                    .getGlobalRouteConfig()[RouteConfigKey.homeScreen.name]
                : preferenceUtils
                    .getGlobalRouteConfig()[RouteConfigKey.postLogin.name];
        if (state.fullPath == path) {
          DeeplinkBloc deeplinkBloc = BlocProvider.of<DeeplinkBloc>(context);
          deeplinkBloc.add(OnDeepLinkLandedMainScreen(landedMainScreen: true));
        }
        if (isPreLoginEnable(state.fullPath, preferenceUtils)) {
          DeeplinkBloc deeplinkBloc = BlocProvider.of<DeeplinkBloc>(context);
          deeplinkBloc.add(OnDeepLinkLandedPreLogin(isForPreLogin: true));
        }
        return null;
      },
    );
  }

  bool isPreLoginEnable(String? fullPath, PreferenceUtils preferenceUtils) {
    List<dynamic>? preLoginRoutesList = preferenceUtils
        .getGlobalRouteConfig()[Constants.preLoginRoutesForDeepLink];
    if (preLoginRoutesList != null) {
      return preLoginRoutesList.contains(fullPath);
    }
    return false;
  }
}
