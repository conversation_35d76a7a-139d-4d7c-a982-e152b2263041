# Change Log

> All notable changes to this project will be documented in this file.
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][G<PERSON>nberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1842] - 2025-08-04

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2394)

* [SFSTRY0115230]-login, reg and help faq version update

* [DFCT0049148] - Gutenberg - Rewards AccelQ_Adidas_Incorrect ordinal index for element matching value "12" using regex ^(0?[0-9]|1[0-2])

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes 

## [v3.0.1634] - 2025-05-28

* [SFSTRY0111632][Gutenberg][Profile Settings - Login preferences New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes  Email communication showing black header New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes  footer color] (#2198) 

## [v3.0.1633] - 2025-05-28

* [SFSTRY0105519]-Update landing pages version 

## [v3.0.1632] - 2025-05-28

* [SFSTRY0088326] - wlg and uilib version updates 

## [v3.0.1631] - 2025-05-28

* No notable changes on this release version. 

## [v3.0.1630] - 2025-05-27

* [SFSTRY0110952][Gutenberg][UI Library - Points history section in Profile has too much side padding] (#2193) 

## [v3.0.1629] - 2025-05-27

* [SFSTRY0088229]-pag version update 

## [v3.0.1628] - 2025-05-27

* [DFCT0046840]: update common_lib version
updating the common_lib version to incorporate following changes:

[DFCT0046840]: remove extra gesture detector - https://github.com/discovery-ltd/v1-commons-lib-flutter/pull/169 

## [v3.0.1627] - 2025-05-27

* [SFSTRY0094991][Gutenberg][UI Library - Integration of bottom sheet for unachieved milestones] (#2190) 

## [v3.0.1626] - 2025-05-27

* [DFCT0047140][SLI:GTB:Prod][High][Cannot submit URLs with a dot "." in the path]
update v1-commons-lib-flutter  version to 0.0.134 

## [v3.0.1625] - 2025-05-27

* No notable changes on this release version. 

## [v3.0.1624] - 2025-05-26

* [SFSTRY0094990][Guntenburg][Central - latest version updates] 

## [v3.0.1623] - 2025-05-26

* [SFSTRY0094990][Guntenburg][Central - version updates] (#2187) 

## [v3.0.1622] - 2025-05-26

* No notable changes on this release version. 

## [v3.0.1621] - 2025-05-23

* [DFCT0043688] - fix: rewards_landing_sectional_navigation_view_issue 

## [v3.0.1620] - 2025-05-23

* [SFSTRY0111177][Gutenberg][UI Library - Member not able to click on Points Filter Close Icon in Points History screen] (#2183) 

## [v3.0.1619] - 2025-05-23

* [SFSTRY0087923] - physical activity goals version update 

## [v3.0.1618] - 2025-05-23

* [SFSTRY0110953][Gutenberg][Goal Streaks Milestones - Streak New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes 

## [v3.0.1634] - 2025-05-28

* [SFSTRY0111632][Gutenberg][Profile Settings - Login preferences New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes  Email communication showing black header New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

* [SFSTRY0111924]-login and wlg version update 

## [v3.0.1641] - 2025-05-30

* [SFSTRY0110981][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1640] - 2025-05-30

* No notable changes on this release version. 

## [v3.0.1639] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - goal streak and ui-lib latest versions added] 

## [v3.0.1638] - 2025-05-29

* [SFSTRY0105668][Gutenburg][Central - latest versions added] (#2202) 

## [v3.0.1637] - 2025-05-29

* [SFSTRY0107847] - mental health assessment version updated (#2201) 

## [v3.0.1636] - 2025-05-29

* No notable changes on this release version. 

## [v3.0.1635] - 2025-05-28

* [SFSTRY0107718] - Reward Journey, feedcard extension changes  footer color] (#2198) 

## [v3.0.1633] - 2025-05-28

* [SFSTRY0105519]-Update landing pages version 

## [v3.0.1632] - 2025-05-28

* [SFSTRY0088326] - wlg and uilib version updates 

## [v3.0.1631] - 2025-05-28

* No notable changes on this release version. 

## [v3.0.1630] - 2025-05-27

* [SFSTRY0110952][Gutenberg][UI Library - Points history section in Profile has too much side padding] (#2193) 

## [v3.0.1629] - 2025-05-27

* [SFSTRY0088229]-pag version update 

## [v3.0.1628] - 2025-05-27

* [DFCT0046840]: update common_lib version
updating the common_lib version to incorporate following changes:

[DFCT0046840]: remove extra gesture detector - https://github.com/discovery-ltd/v1-commons-lib-flutter/pull/169 

## [v3.0.1627] - 2025-05-27

* [SFSTRY0094991][Gutenberg][UI Library - Integration of bottom sheet for unachieved milestones] (#2190) 

## [v3.0.1626] - 2025-05-27

* [DFCT0047140][SLI:GTB:Prod][High][Cannot submit URLs with a dot "." in the path]
update v1-commons-lib-flutter  version to 0.0.134 

## [v3.0.1625] - 2025-05-27

* No notable changes on this release version. 

## [v3.0.1624] - 2025-05-26

* [SFSTRY0094990][Guntenburg][Central - latest version updates] 

## [v3.0.1623] - 2025-05-26

* [SFSTRY0094990][Guntenburg][Central - version updates] (#2187) 

## [v3.0.1622] - 2025-05-26

* No notable changes on this release version. 

## [v3.0.1621] - 2025-05-23

* [DFCT0043688] - fix: rewards_landing_sectional_navigation_view_issue 

## [v3.0.1620] - 2025-05-23

* [SFSTRY0111177][Gutenberg][UI Library - Member not able to click on Points Filter Close Icon in Points History screen] (#2183) 

## [v3.0.1619] - 2025-05-23

* [SFSTRY0087923] - physical activity goals version update  Milestone card not getting update at Profile screen][pushing mocks] (#2181)
* [SFSTRY0110953][Gutenberg][Goal Streaks Milestones - Streak New Changes:

## [v3.0.1905] - 2025-08-21

* [SFSTRY0111033] - version update 

## [v3.0.1904] - 2025-08-21

* [SFSTRY0114774][SLI][update resource bundle manager version] (#2448) 

## [v3.0.1903] - 2025-08-21

* [SFSTRY0108595] - HA version update 

## [v3.0.1902] - 2025-08-21

* No notable changes on this release version. 

## [v3.0.1901] - 2025-08-20

* [DFCT0049313] - Status list not showing in automation 

## [v3.0.1900] - 2025-08-20

* [SFSTRY0118720][Gutenberg][Points History - Navigation to points history is showing blank screen ] 

## [v3.0.1899] - 2025-08-20

* [SFSTRY0118506][SLI][Common lib version update] 

## [v3.0.1898] - 2025-08-20

* [DFCT0049537] - Physical activity Progression line 

## [v3.0.1897] - 2025-08-20

* No notable changes on this release version. 

## [v3.0.1896] - 2025-08-19

* [DFCT0049313] - Adidas Landing Page Flickers on Initial Load 

## [v3.0.1895] - 2025-08-19

* [SFSTRY0117082] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1894] - 2025-08-19

* [SFSTRY0118043] resource_bundle_manager version update for healthy gear reward 

## [v3.0.1893] - 2025-08-19

* No notable changes on this release version. 

## [v3.0.1892] - 2025-08-18

* [DFCT0049448] - Celebration screen alignment is not according to figma 

## [v3.0.1891] - 2025-08-18

* [DFCT0049409]-help and faq version update 

## [v3.0.1890] - 2025-08-18

* No notable changes on this release version. 

## [v3.0.1889] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix the issue raised by SLI regarding caching issue for CTA Spins
Change-Id: I963455a4296f59ab2a354b345fd9ec038509900b 

## [v3.0.1888] - 2025-08-15

* No notable changes on this release version. 

## [v3.0.1887] - 2025-08-14

* [SFSTRY0114522] - Added Breadcrumbs Manager and Content Source Metadata Tooltip 

## [v3.0.1886] - 2025-08-14

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1885] - 2025-08-14

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent

* [SFSTRY0114624] - version update regarding CTA alignment 200 percent 

## [v3.0.1884] - 2025-08-14

* [DFCT0049425]-ui lib version update 

## [v3.0.1883] - 2025-08-14

* No notable changes on this release version. 

## [v3.0.1882] - 2025-08-13

* [DFCT0049503][SLI][VHC version update] 

## [v3.0.1881] - 2025-08-13

* [SFSTRY0118115] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1880] - 2025-08-13

* [SFSTRY0116897] - bump profile_settings to 216 

## [v3.0.1879] - 2025-08-13

* [DFCT0048917] - URL validation fix 

## [v3.0.1878] - 2025-08-13

* [SFSTRY0114624] - Webview will open PDFs in External Browser when Tapped or Clicked 

## [v3.0.1877] - 2025-08-13

* [SFSTRY0114481][Gutenberg][Central - resource bundle pubSpec update] 

## [v3.0.1876] - 2025-08-13

* No notable changes on this release version. 

## [v3.0.1875] - 2025-08-12

* [SFSTRY0115231][Gutenberg][fix infinite scrolling issue when widget is scrolled into viewport]

* [DFCT0049457][SLI][VHC version update] 

## [v3.0.1874] - 2025-08-12

* [DFCT0049425]-ui-lib version update 

## [v3.0.1873] - 2025-08-12

* [SFSTRY0117084][Gutenberg][safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v3.0.1872] - 2025-08-12

* No notable changes on this release version. 

## [v3.0.1871] - 2025-08-11

* [DFCT0049409]-update the help and faq version 

## [v3.0.1870] - 2025-08-11

* [SFSTRY0111896][SLI][CR-325 ui changes Update resource_bundle_manager Version in pubspec.yaml]

* [SFSTRY0111896][SLI][CR-325 ui changes Update VHC Version in pubspec.yaml] 

## [v3.0.1869] - 2025-08-11

* [SFSTRY0111065] vitality_health_check version update 

## [v3.0.1868] - 2025-08-11

* [DFCT0049148] - Rewards Updated sorting of vouchers list by awardedOn date and awardedRewardId and then by awardedOn rewardValue percent 

## [v3.0.1867] - 2025-08-11

* [SFSTRY0049399] - update uilib version

* [SFSTRY0049399] - update uilib version 

## [v3.0.1866] - 2025-08-11

* No notable changes on this release version. 

## [v3.0.1865] - 2025-08-08

* [SFSTRY0089417] - version update 

## [v3.0.1864] - 2025-08-08

* [SFSTRY0115235]-update ui-lib version for accessibility issue fix 

## [v3.0.1863] - 2025-08-08

* [SFSTRY0114624] - Throw Flutter framework errors in debug mode for early detection. 

## [v3.0.1862] - 2025-08-08

* [SFSTRY0116508] - updated the font size accessibility my health 

## [v3.0.1861] - 2025-08-08

* [DFCT0049202] - Update reward journey version 

## [v3.0.1860] - 2025-08-08

* [SFSTRY0117841] Update Central Host App release pipeline to include Automated Accelq PDVTs before releasing to markets. 

## [v3.0.1859] - 2025-08-08

* No notable changes on this release version. 

## [v3.0.1858] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v3.0.1857] - 2025-08-07

* [SFSTRY0113984] update resource bundle

* [SFSTRY0113984] update resource bundle 

## [v3.0.1856] - 2025-08-07

* [SFSTRY0117758][Gutenberg][Apply filter functionality is failing] (#2403) 

## [v3.0.1855] - 2025-08-07

* [DFCT0049148] - Updated sorting of vouchers list by awardedRewardId and awardedOn date 

## [v3.0.1854] - 2025-08-07

* No notable changes on this release version. 

## [v3.0.1853] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi (#2392)
locale

Change-Id: Ie0b5f8c10342c85a8d54347a6728714713a05aef

Co-authored-by: P01HEMLATAA <<EMAIL>> 

## [v3.0.1852] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update registration journey version (#2404) 

## [v3.0.1851] - 2025-08-06

* [SFSTRY0116512] - 200% Font Size Accessibility 

## [v3.0.1850] - 2025-08-06

* [DFCT0045997][PRT][ResourceBundle]-ResourceBundle Version Update 

## [v3.0.1849] - 2025-08-06

* [SFSTRY0115235] - updated the landing pages, help-faq and wheelspin coins repo versions. 

## [v3.0.1848] - 2025-08-06

* [SFSTRY0114624] - update ui lib and rewards journey version

* [SFSTRY0114624] - update mha version 

## [v3.0.1847] - 2025-08-06

* No notable changes on this release version. 

## [v3.0.1846] - 2025-08-05

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - added safe area to expand the animation in the entire screen.

* [DFCT0048154] - removed the safe area from the splash screen. 

## [v3.0.1845] - 2025-08-05

* [SFSTRY0114624] - Added Backwards Compatibility for Process Name Fetch in Android < 8 

## [v3.0.1844] - 2025-08-05

* No notable changes on this release version. 

## [v3.0.1843] - 2025-08-04

* No notable changes on this release version. 

## [v3.0.1841] - 2025-08-01

* [DFCT0048154] - updating the splash screen with safe area. 

## [v3.0.1840] - 2025-08-01

* [SFSTRY0114624] - Forked the facebook_app_events repository into v1-facebook-app-events-flutter. 

## [v3.0.1839] - 2025-08-01

* [DFCT0049221][MEX][UILib]-UILib Version Update 

## [v3.0.1838] - 2025-08-01

* [SFSTRY0114785][Gutenberg][Fix format exception] (#2386)
* [SFSTRY0114785][Gutenberg][Fix format exception]

* [SFSTRY0114785][Gutenberg][Fix bitrise issue] 

## [v3.0.1837] - 2025-08-01

* No notable changes on this release version. 

## [v3.0.1836] - 2025-07-31

* [SFSTRY0114785][Gutenberg][Change app content when language is selected] (#2382)
* [SFSTRY0114785][Gutenberg][Change app content when language is selected]

* [SFSTRY0114785][Gutenberg][Update status version]

* [SFSTRY0114785][Gutenberg][Update profile and settings version] 

## [v3.0.1835] - 2025-07-31

* [SFSTRY0114624] -update ui-lib version 

## [v3.0.1834] - 2025-07-31

* [SFSTRY0116897][Gutenberg][Accessibility Fixes for All Journey Components (200% Zoom Compatibility) - iOS] (#2384) 

## [v3.0.1833] - 2025-07-31

* [DFCT0049148] - Gutenberg - Rewards sorting vouchers list by awardedOn date 

## [v3.0.1832] - 2025-07-31

* [SFSTRY0114624] - Updated analytics_manager version to resolve issue with facebook_app_events dependency 

## [v3.0.1831] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1830] - 2025-07-31

* No notable changes on this release version. 

## [v3.0.1829] - 2025-07-30

* No notable changes on this release version. 

## [v3.0.1828] - 2025-07-29

* [SFSTRY0114623] - update ui-lib version 

## [v3.0.1827] - 2025-07-29

* [DFCT0049107] - Solve mobile back button issue in reward 

## [v3.0.1826] - 2025-07-29

* [DFCT0049107] - Update reward journey version 

## [v3.0.1825] - 2025-07-29

* [SFSTRY0113841] - Capped Max Text Size to 200% 

## [v3.0.1824] - 2025-07-29

* [SFSTRY0116902][FeedsWrapper] - Update version
Change-Id: I36b79b824700561a4e4835c8f676a5e1add32de3

* [SFSTRY0116902][FeedsWrapper] - Added feedCard id inside eventMetadata
Change-Id: I1adfe8b1816fd8259bd02d2beb1d359e680b566a 

## [v3.0.1823] - 2025-07-29

* No notable changes on this release version. 

## [v3.0.1822] - 2025-07-28

* [SFSTRY0117128][Gutenberg][Json Content Renderer - MonthlyCashbackSteps_v1.0.0 is not aligned with Figma design] (#2374) 

## [v3.0.1821] - 2025-07-28

* [SFSTRY0115230]-version update for login and reg 

## [v3.0.1820] - 2025-07-28

* [DFCT0046821][ECU][HealthyFood]-Healthy Food Version Update 

## [v3.0.1819] - 2025-07-28

* [SFSTRY0111193] - Gutenberg - Adidas API integration build and json_content_render version update 

## [v3.0.1818] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v3.0.1817] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1816] - 2025-07-28

* [SFSTRY0110877] - Update PAG journey version 

## [v3.0.1815] - 2025-07-28

* No notable changes on this release version. 

## [v3.0.1814] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v3.0.1813] - 2025-07-25

* [SFSTRY0110877] - Update Reward journey version

* Update pubspec.yaml

* [SFSTRY0110877] - Update Reward journey version 

## [v3.0.1812] - 2025-07-25

* [SFSTRY0110941][Gutenberg][Central - pubSpec update] 

## [v3.0.1811] - 2025-07-25

* No notable changes on this release version. 

## [v3.0.1810] - 2025-07-24

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 2]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1] 

## [v3.0.1809] - 2025-07-24

* [SFSTRY0116508] - updated the font size accessibility apps and devices

* [SFSTRY0116508] - updated the font size accessibility apps and devices 

## [v3.0.1808] - 2025-07-24

* [SFSTSK0024777] - Add isClosed check before emit to prevent BadState 

## [v3.0.1807] - 2025-07-24

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update (#2358)
* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - version update

* [SFSTRY0111462][DFCT0047099][Gutenburg][Central] - my health version update

---------

Co-authored-by: chaitanya <<EMAIL>> 

## [v3.0.1806] - 2025-07-24

* [SFSTRY0114623] - update my health version 

## [v3.0.1805] - 2025-07-24

* [SFSTRY0116558] - update Earn Points version. 

## [v3.0.1804] - 2025-07-24

* No notable changes on this release version. 

## [v3.0.1803] - 2025-07-23

* [SFSTRY0110820] - update vhc version (#2345) 

## [v3.0.1802] - 2025-07-23

* [SFSTRY0116558] - update version for PAG, Earn Points and Resource Bundle. 

## [v3.0.1801] - 2025-07-23

* [SFSTRY0116840][DFCT0048878][Gutenberg][Central - version update] 

## [v3.0.1800] - 2025-07-23

* [SFSTRY0114623] - update uilib version 

## [v3.0.1799] - 2025-07-23

* [SFSTRY0115230] - registration and login update 

## [v3.0.1798] - 2025-07-23

* [SFSTRY0114623] - update ui_lib version 

## [v3.0.1797] - 2025-07-23

* No notable changes on this release version. 

## [v3.0.1796] - 2025-07-22

* No notable changes on this release version. 

## [v3.0.1795] - 2025-07-21

* No notable changes on this release version. 

## [v3.0.1794] - 2025-07-18

* [SFSTRY0116618][Gutenberg][Streaks and Milestones Milestone achieved date for lower milestones is displaying even though the achieved date is greater than the higher milestones achieved date] 

## [v3.0.1793] - 2025-07-18

* [SFSTSK0024747] - Authentication plugin version update 

## [v3.0.1792] - 2025-07-18

* [SFSTRY0112088][Gutenberg][Resource Bundle - new category keys for HealthyFood - pubSpec update] 

## [v3.0.1791] - 2025-07-18

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2340) 

## [v3.0.1790] - 2025-07-18

* No notable changes on this release version. 

## [v3.0.1789] - 2025-07-17

* [SFSTRY0116537][Gutenberg][Network error dialogue pop-up is not closing upon clicking once on "Got it] (#2337) 

## [v3.0.1788] - 2025-07-17

* [SFSTRY0114513][Gutenberg] - ui lib version update

* [SFSTRY0114513][Gutenberg] - version updates 

## [v3.0.1787] - 2025-07-17

* [SFSTRY0116538][Gutenberg][Update featured articles and coins to fuel bar versions] (#2335) 

## [v3.0.1786] - 2025-07-17

* No notable changes on this release version. 

## [v3.0.1785] - 2025-07-16

* [SFSTRY0109073]-update resource bundle 

## [v3.0.1784] - 2025-07-16

* No notable changes on this release version. 

## [v3.0.1783] - 2025-07-15

* [DFCT0048160] - The same proof appears for all indicators 

## [v3.0.1782] - 2025-07-15

* [SFSTRY0114542]- version update 

## [v3.0.1781] - 2025-07-15

* [SFSTRY0113404] - registration journey version update 

## [v3.0.1780] - 2025-07-15

* No notable changes on this release version. 

## [v3.0.1779] - 2025-07-14

* [DFCT0048431][ECU][Profile-Settings]-Profile and Settings Version Update 

## [v3.0.1778] - 2025-07-14

* No notable changes on this release version. 

## [v3.0.1777] - 2025-07-11

* [SFSTRY0113090][Gutenberg][Update gift card version] (#2328) 

## [v3.0.1776] - 2025-07-11

* No notable changes on this release version. 

## [v3.0.1775] - 2025-07-10

* [SFSTRY0100894] Update versions
Change-Id: Idf00dc2d42fcd057149d471683206c805bd14200

* [SFSTRY0100894] Updates from cards wrapper

* [SFSTRY0100894] Updates for deletion of feeds on dismissal the feed card

* [SFSTRY0100894][Feeds][Wrapper] - Added implementation for the feeds wrapper feature
Change-Id: I78037eaf7ea55db7e52c9169e414015c6f174072 

## [v3.0.1774] - 2025-07-10

* [DFCT0045189][ECU][Journey Commons]-Journey Commons Version Update 

## [v3.0.1773] - 2025-07-10

* No notable changes on this release version. 

## [v3.0.1772] - 2025-07-09

* [SFSTRY0112954] - updated gateway health sdk version

* [SFSTRY0112954] - Refactor Health SDK initialization into Android's onCreate 

## [v3.0.1771] - 2025-07-09

* [DFCT0048477]-reg version update for future dated user 

## [v3.0.1770] - 2025-07-09

* No notable changes on this release version. 

## [v3.0.1769] - 2025-07-08

* [SFSTRY0114707][Gutenberg][Coins to fuelbar - "NaN" Displayed in Rewards Coin Indicator on Rewards Screen] 

## [v3.0.1768] - 2025-07-08

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1767] - 2025-07-08

* [SFSTRY0111566] - Updated pubspec.yaml 

## [v3.0.1766] - 2025-07-08

* No notable changes on this release version. 

## [v3.0.1765] - 2025-07-07

* [SFSTRY0114712] - update version of ui_lib 

## [v3.0.1764] - 2025-07-07

* [SFSTRY0111566] - Updated yaml for login journey 

## [v3.0.1763] - 2025-07-07

* [SFSTRY0113640][Gutenberg][v1-feeds-card-builders-flutter - podSpec update] 

## [v3.0.1762] - 2025-07-07

* [SFSTRY0113115]-Update PAG, resource bundle and earn points version. 

## [v3.0.1761] - 2025-07-07

* [DFCT0048472]-reg version update 

## [v3.0.1760] - 2025-07-07

* No notable changes on this release version. 

## [v3.0.1759] - 2025-07-04

* [SFSTRY0110877] - migrate reward micro service to V2 issue fix 

## [v3.0.1758] - 2025-07-04

* [SFSTRY0113750]- ui_lIb version updated

* [SFSTRY0113750]- UI alignment issues 

## [v3.0.1757] - 2025-07-04

* [DFCT0047355] UI Lib version update 

## [v3.0.1756] - 2025-07-04

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update

* [SFSTRY0113821] - logout issue fix msmsr version update 

## [v3.0.1755] - 2025-07-04

* No notable changes on this release version. 

## [v3.0.1754] - 2025-07-03

* [DFCT0048286] v1-wheelspin-coinsrewards-flutter version update 

## [v3.0.1753] - 2025-07-03

* Gift card version update

* [SFSTRY0113090][Gutenberg][UI Library - Accessibility Fixes for Journey Components (200% Zoom Compatibility) - 1]

* [SFSTRY0113090][Gutenberg][Gift card - Render flex error fix] 

## [v3.0.1752] - 2025-07-03

* [DFCT0048388] - Fix reward card expiry check to include same-day cards 

## [v3.0.1751] - 2025-07-03

* [SFSTRY0114032] - common_lib version upgrade from "0.0.144" to "0.0.145" 

## [v3.0.1750] - 2025-07-03

* No notable changes on this release version. 

## [v3.0.1749] - 2025-07-02

* [SFSTRY0114077][Gutenberg][Streaks and milestone-current streak text fixed on goal streak card] 

## [v3.0.1748] - 2025-07-02

* [SFSTRY0113819] - Brought Back Double Quotes Around Provisioning Profile Names 

## [v3.0.1747] - 2025-07-02

* [SFSTRY0113819] - Pinned Firebase Core Platform Interface 

## [v3.0.1746] - 2025-07-02

* [DFCT0046626]-main nav version update 

## [v3.0.1745] - 2025-07-02

* [SFSTRY0113821] - update msmr version

* [SFSTRY0113821] - update msmr version 

## [v3.0.1744] - 2025-07-02

* No notable changes on this release version. 

## [v3.0.1743] - 2025-07-01

* No notable changes on this release version. 

## [v3.0.1742] - 2025-06-30

* No notable changes on this release version. 

## [v3.0.1741] - 2025-06-27

* [SFSTRY0113541] - updated registration, profile and settings repository versions. 

## [v3.0.1740] - 2025-06-27

* [SFSTRY0113807] - Update Reward journey version 

## [v3.0.1739] - 2025-06-27

* [SFSTRY0113090][Gutenburg][Central - version update] (#2293) 

## [v3.0.1738] - 2025-06-27

* No notable changes on this release version. 

## [v3.0.1737] - 2025-06-26

* [SFSTRY0111288][Gutenberg][Coins to fuelbar - [T-104][GTB-DW] [Transition] Credit Fuel Bar showing more then 1000 credits in Fuel bar for Transition users] (#2292) 

## [v3.0.1736] - 2025-06-26

* [SFSTRY0113296] Encryption Added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for PartyId and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification.

* [SFSTRY0113296] Encryption added for Party Id and with fallback mechanism for Dynatrace and Notification 

## [v3.0.1735] - 2025-06-26

* [SFSTSK0024420] – Updated pbxproj to resolve the pipeline issue by converting it into a single-line mapping format for proper parsing 

## [v3.0.1734] - 2025-06-26

* No notable changes on this release version. 

## [v3.0.1733] - 2025-06-25

* [SFSTRY0113221] -common lib version update 

## [v3.0.1732] - 2025-06-25

* [SFSTRY0107878][Goal streaks and milestone- Sending the "log_out" tag to the onesignal, when user logs out from the app] (#2289) 

## [v3.0.1731] - 2025-06-25

* [SFSTRY0113348]- wlg version update 

## [v3.0.1730] - 2025-06-25

* [SFSTRY0113310] - Update reward journey version 

## [v3.0.1729] - 2025-06-25

* [SFSTRY0113221] - common lib version update for VPN Handling

* [SFSTRY0113221] - Handling AdGuard if available for accessing GTB app 

## [v3.0.1728] - 2025-06-25

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition

* [SFSTRY0113574]- Deeplinks not opening the expected screen. - Add pattern match condition 

## [v3.0.1727] - 2025-06-25

* [DFCT0046871] - Gutenberg - See more label is missing from the Health Check submissions screen in VHC 

## [v3.0.1726] - 2025-06-25

* [DFCT0048059] - Updated auth plugin version where updated data type from Int to String 

## [v3.0.1725] - 2025-06-25

* No notable changes on this release version. 

## [v3.0.1724] - 2025-06-24

* [SFSTRY0112015][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1723] - 2025-06-24

* [DFCT0045189][ECU][Image-Asset-Provider]-Image Asset Provider Version Update 

## [v3.0.1722] - 2025-06-24

* [SFSTRY0112484][Gutenberg][resource_bundle_manager - podSpec update] 

## [v3.0.1721] - 2025-06-24

* [SFSTRY0110199] Address review points

* [SFSTRY0111485]Removed debugprints

* [SFSTRY0111485]Updated msmr native ios sdk  version
*Fixed alert dialog title placement

* [SFSTRY0110199]Added msmr initializer implementation remove old implementation

* [SFSTRY0110199]Removed initializeMSMR.

* [SFSTRY0110199]Integrate phrase keys in main.dart

* [SFSTRY0110199] - Update version
Change-Id: I8ddedf8b34037550d8df30a248824e50ee78ec2c

* [SFSTRY0110199]-Removal of msmr native integration

* [SFSTRY0110199] - Added decoupling of lifecycle implementation
Change-Id: I933294a8eaafdd370e305c9554e93535b4b321ae 

## [v3.0.1720] - 2025-06-24

* [SFSTRY0113259]-Updated gateway SDK version 

## [v3.0.1719] - 2025-06-24

* No notable changes on this release version. 

## [v3.0.1718] - 2025-06-23

* [SFSTRY0113541] - update Physical activity goals Repo version. 

## [v3.0.1717] - 2025-06-23

* No notable changes on this release version. 

## [v3.0.1716] - 2025-06-20

* [SFSTRY0113240][Gutenberg][Central - version update] (#2273) 

## [v3.0.1715] - 2025-06-20

* [SFSTRY0111881] - registration and profile update 

## [v3.0.1714] - 2025-06-20

* [SFSTRY0113095][Gutenberg][Development Work for Tenant 42 -  PRT Extension Journey][Added test login response for PRT]][DFCT0035896][DFCT0035889] (#2270) 

## [v3.0.1713] - 2025-06-20

* [SFSTRY0113306] Preference Migration version updated

* [SFSTRY0113306] Preference Migration version update 

## [v3.0.1712] - 2025-06-20

* [SFSTRY0107607][Gutenberg][In app action handler capability] (#2267)
* [SFSTRY0107607][Gutenberg][In app action handler capability]

* [SFSTRY0107607][Gutenberg][update goal streaks version]

* [SFSTRY0107607][Gutenberg][registration version update] 

## [v3.0.1711] - 2025-06-20

* No notable changes on this release version. 

## [v3.0.1710] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1709] - 2025-06-19

* [SFSTRY0112556]- Enhance OneSignal In-App notifications with Deeplink Handler and Payload Parsing 

## [v3.0.1708] - 2025-06-19

* [SFSTRY0107831] - version update for resource bundle manager 

## [v3.0.1707] - 2025-06-19

* No notable changes on this release version. 

## [v3.0.1706] - 2025-06-18

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review- Refactor Phase -1] (#2261)
* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1]

* [SFSTRY0113086][Gutenberg][Streaks and Milestones Journey Code Review - Refactor Phase -1] 

## [v3.0.1705] - 2025-06-18

* [DFCT0047808] - common lib version update

*  [DFCT0047808][Gutenberg][central - common lib version update] #2264

* [DFCT0047808] - common lib version update

* [DFCT0044016] - Gutenberg -updated library versions 

## [v3.0.1704] - 2025-06-18

* [SFSTRY0111193] – Revert the changes the FlutterActivity to FlutterFragmentActivity 

## [v3.0.1703] - 2025-06-18

* [DFCT0047507][ECU][UI-Lib]-Version Update 

## [v3.0.1702] - 2025-06-18

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 5.3.3 for coldstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing.

* [SFSTRY0111544] - onesignal upgraded to 3.5.2 for colstart notification testing. 

## [v3.0.1701] - 2025-06-18

* [SFSTRY0112498]-Added enabling/disabling gatewaySDK logs based on lookit 

## [v3.0.1700] - 2025-06-18

* No notable changes on this release version. 

## [v3.0.1699] - 2025-06-17

* [SFSTRY0106142] - Added a new method for logging all Gateway health sdk events to Dynatrace and gateway_health_sdk version updated. 

## [v3.0.1698] - 2025-06-17

* No notable changes on this release version. 

## [v3.0.1697] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1696] - 2025-06-16

* [SFSTRY0112519][ECU][Feeds-Card-Builder]-Version Update 

## [v3.0.1695] - 2025-06-16

* [DFCT0047808] - common lib version update 

## [v3.0.1694] - 2025-06-16

* [SFSTRY0111555] - Updated pubspec ui_lib, resource_bundle 

## [v3.0.1693] - 2025-06-16

* [DFCT0047500] - Updating physical activities module to 0.0.265 _ Adding missing conditions in heart rate 

## [v3.0.1692] - 2025-06-16

* No notable changes on this release version. 

## [v3.0.1691] - 2025-06-13

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version

* [SFSTRY0111193] - Update gateway health sdk version 

## [v3.0.1690] - 2025-06-13

* No notable changes on this release version. 

## [v3.0.1689] - 2025-06-12

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement

* [SFSTRY0107847] - Adding a Toggling capability for MHA Feedback Service Replacement 

## [v3.0.1688] - 2025-06-12

* [SFSTRY0106642]-common lib version update 

## [v3.0.1687] - 2025-06-12

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2248) 

## [v3.0.1686] - 2025-06-12

* [SFSTRY0111255][Gutenburg][Central - version update] (#2247) 

## [v3.0.1685] - 2025-06-12

* [SFSTRY0111193] - Update Rewards version 

## [v3.0.1684] - 2025-06-12

* [DFCT0039764] - Update VHC version 

## [v3.0.1683] - 2025-06-12

* No notable changes on this release version. 

## [v3.0.1682] - 2025-06-11

* [SFSTRY0112390] - Update Ui lib and json content render version 

## [v3.0.1681] - 2025-06-11

* [SFSTRY0112069] - Reward Journey updated phrase keys in adidas landing screen reward status 

## [v3.0.1680] - 2025-06-11

* [SFSTRY0112832][Gutenberg][[Streaks and Milestones - Milestones text is appearing instead of milestone  on hero component of view milstone page ] (#2243) 

## [v3.0.1679] - 2025-06-11

* [SFSTRY0111634][DFCT0047696][Gutenburg][Central] - how to earn version updates (#2242) 

## [v3.0.1678] - 2025-06-11

* [SFSTRY0106642]-common lib version update. 

## [v3.0.1677] - 2025-06-11

* No notable changes on this release version. 

## [v3.0.1676] - 2025-06-10

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2239)
* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update pag version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update profile settings version]

* [SFSTRY0107607][Gutenberg][Streaks and milestone- update goal streaks version] 

## [v3.0.1675] - 2025-06-10

* [SFSTRY0112519][ECU][Resource Bundle]-Version Update 

## [v3.0.1674] - 2025-06-10

* [SFSTRY0111737][Gutenberg][Creation and Injection of extension point for Streaks and Milestones] (#2236) 

## [v3.0.1673] - 2025-06-10

* [SFSTRY0111634][DFCT0047178][Gutenburg][Central] - how to earn version updates 

## [v3.0.1672] - 2025-06-10

* No notable changes on this release version. 

## [v3.0.1671] - 2025-06-09

* [DFCT0046879] - updating feeds-card-builder repo version and overriding remote_app_runtime version 

## [v3.0.1670] - 2025-06-09

* [SFSTRY0112380] - Reward Journey learn more about rules 

## [v3.0.1669] - 2025-06-09

* No notable changes on this release version. 

## [v3.0.1668] - 2025-06-06

* [SFSTRY0107847] - Replace existing LifeRay service with New backend service 

## [v3.0.1667] - 2025-06-06

* [SFSTRY0109121] - Updated pubspec yaml 

## [v3.0.1666] - 2025-06-06

* [SFSTRY0112151] Trim the older entries on the changelog to have the supportted file size on github.

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer

* [SFSTRY0112151] - Added 200% Font Size Support for WebRenderer 

## [v3.0.1665] - 2025-06-06

* [SFSTRY0107607][Gutenberg][Streaks and milestone- native milestone sharing] (#2230) 

## [v3.0.1664] - 2025-06-06

* [SFSTRY0112100][Gutenberg][Points History - added new category key for Health Check] (#2229) 

## [v3.0.1663] - 2025-06-06

* [DFCT0045568] - version updates (#2228) 

## [v3.0.1662] - 2025-06-06

* [DFCT0046752] - login version update 

## [v3.0.1661] - 2025-06-06

* No notable changes on this release version. 

## [v3.0.1660] - 2025-06-05

* [DFCT0043403] - login reg auth version update 

## [v3.0.1659] - 2025-06-05

* [SFSTRY0112406]-login version update 

## [v3.0.1658] - 2025-06-05

* No notable changes on this release version. 

## [v3.0.1657] - 2025-06-04

* [SFSTRY0106047] - Use VGLogger in application_configuration and version updated. 

## [v3.0.1656] - 2025-06-04

* [SFSTRY0105668][Gutenberg][Code cleanup and use of VGLogger] (#2217) 

## [v3.0.1655] - 2025-06-04

* [SFSTRY0112069] - Config changes for Reveal Code 

## [v3.0.1654] - 2025-06-04

* [SFSTRY0106048] - Toggle or Disable Logging in UI Library for Loookit Builds 

## [v3.0.1653] - 2025-06-04

* [DFCT0046816]-login,landing and ui-lib version update 

## [v3.0.1652] - 2025-06-04

* [SFSTRY0112069] - JsonContent renderer enhancement for giving option for Button and reward journey config changes 

## [v3.0.1651] - 2025-06-04

* [SFSTRY0106048]-Remove the unnecessary debugPrint 

## [v3.0.1650] - 2025-06-04

* [SFSTRY0106048]-Removed unnecessary debugPRint 

## [v3.0.1649] - 2025-06-04

* No notable changes on this release version. 

## [v3.0.1648] - 2025-06-03

* [SFSTRY0112263] - Added Forceful Suppression of Logs in `VGLog` through Env Vars 

## [v3.0.1647] - 2025-06-03

* [SFSTRY0111986][Gutenberg][Goal Streaks Milestones - UI distortion and the pixel issues on the goal streak card] (#2210) 

## [v3.0.1646] - 2025-06-03

* [SFSTRY0111987][Gutenberg][Goal Streaks Milestones - Number of milestones acheived is inappropriate on the Hero component on View milestones page] (#2209) 

## [v3.0.1645] - 2025-06-03

* [SFSTRY0105668][Gutenburg][Goal Streak - streak goal version fixes]

* [SFSTRY0105668][Gutenburg][Goal Streak - milestone streak goal fixes] 

## [v3.0.1644] - 2025-06-03

* No notable changes on this release version. 

## [v3.0.1643] - 2025-06-02

* No notable changes on this release version. 

## [v3.0.1642] - 2025-05-30

