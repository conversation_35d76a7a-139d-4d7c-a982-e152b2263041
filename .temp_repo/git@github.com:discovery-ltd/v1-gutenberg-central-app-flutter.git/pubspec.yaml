name: gutenberg_central_app
description: "Test app for Gutenberg Central App Widget"
publish_to: 'none'
version: 3.0.1891
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_modular: ^5.0.3
  bloc: ^8.1.2
  equatable: ^2.0.5
  flutter_bloc: ^8.1.3
  bloc_test: ^9.1.4
  intl: ^0.18.1
  in_app_review: ^2.0.9
  app_links: ^3.5.1
  firebase_core: ^2.11.0
  firebase_analytics: ^10.6.0
  pub_semver: ^2.1.4
  android_intent_plus: ^5.3.0
  msmr:
    git:
      url: **************:discovery-ltd/v1-msmr-flutter.git
      path: msmr
      ref: 0.0.18
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.21
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.365
  registration_journey:
    git:
      url: **************:discovery-ltd/v1-registration-journey-flutter.git
      path: registration_journey
      ref: 0.0.342
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.0
  v1_main_nav_package_flutter:
    git:
      url: **************:discovery-ltd/v1-main-nav-package-flutter.git
      path: v1_main_nav_package_flutter
      ref: 0.0.106
  featured_article:
    git:
      url: **************:discovery-ltd/v1-featured-article-flutter.git
      path: featured_article
      ref: 0.0.38
  giftcards:
    git:
      url: **************:discovery-ltd/v1-giftcards-flutter.git
      path: giftcards
      ref: 0.0.176
  coins_to_fuelbar:
    git:
      url: **************:discovery-ltd/v1-coins-to-fuelbar-flutter.git
      path: coins_to_fuelbar
      ref: 0.0.80
  profile_and_settings:
    git:
      url: **************:discovery-ltd/v1-profile-settings-flutter.git
      path: profile_and_settings
      ref: 0.0.216
  screenings_vaccinations:
    git:
      url: **************:discovery-ltd/v1-screenings-vaccinations-flutter.git
      path: screenings_vaccinations
      ref: 0.0.180
  weekly_lifestyle_goals:
    git:
      url: **************:discovery-ltd/v1-weekly-lifestyle-goals-flutter.git
      path: weekly_lifestyle_goals
      ref: 0.0.215
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.147
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  device_cashback:
    git:
      url: **************:discovery-ltd/v1-device-cashback-flutter.git
      path: device_cashback
      ref: 0.0.161
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.256
  status_journey:
    git:
      url: **************:discovery-ltd/v1-status-journey-flutter.git
      path: status_journey
      ref: 0.0.78
  rewards_journey:
    git:
      url: **************:discovery-ltd/v1-rewards-journey-flutter.git
      path: rewards_journey
      ref: 0.0.119
  landing_pages:
    git:
      url: **************:discovery-ltd/v1-landing-pages-flutter.git
      path: landing_pages
      ref: 0.0.213
  status_transition:
    git:
      url: **************:discovery-ltd/v1-status-transition-flutter.git
      path: status_transition
      ref: 0.0.14
  goals_points_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-goals-points-services-micro-service-sdk-flutter.git
      path: goals_points_services_micro_service_sdk
      ref: main
  organised_fitness_events:
    git:
      url: **************:discovery-ltd/v1-organised-fitness-events-flutter.git
      path: organised_fitness_events
      ref: 0.0.136
  vitality_health_check:
    git:
      url: **************:discovery-ltd/v1-vitality-health-check-flutter.git
      path: vitality_health_check
      ref: 0.0.151
  non_smoker_declaration:
    git:
      url: **************:discovery-ltd/v1-non-smoker-declaration-flutter.git
      path: non_smoker_declaration
      ref: 0.0.79
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  v1_font_awesome_icons_flutter:
    git:
      url: **************:discovery-ltd/v1-font-awesome-icons-flutter.git
      path: v1_font_awesome_icons_flutter
      version: 0.0.8
  remote_app_module:
    git:
      url: **************:discovery-ltd/v1-remote-app-module-flutter.git
      path: remote_app_module
      ref: 0.0.4
  goal_streaks_milestones:
    git:
      url: **************:discovery-ltd/v1-goal-streaks-milestones-flutter.git
      path: goal_streaks_milestones
      ref: 0.0.50
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.78
dependency_overrides:
  v1_font_awesome_icons_flutter:
    git:
      url: **************:discovery-ltd/v1-font-awesome-icons-flutter.git
      path: v1_font_awesome_icons_flutter
      version: 0.0.8
  intl: ^0.18.0
  analyzer: ^6.0.0
  matcher: ^0.12.17
  device_info_plus: ^10.1.2
  pdfx: 2.8.0
  flutter_secure_storage: ^8.0.0
  http: 1.1.0
  dynatrace_flutter_plugin: 3.307.1
  flutter_inappwebview: 6.1.5
  visibility_detector: ^0.4.0+2
  carousel_slider: ^5.0.0
  flutter_plugin_android_lifecycle: 2.0.19
  app_settings: 5.1.1
  firebase_core_platform_interface: 5.4.0
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.14
  preferences_migration:
    git:
      url: **************:discovery-ltd/v1-preferences-migration-flutter.git
      path: preferences_migration
      ref: 0.0.49
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: 0.0.13
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.22
  v1_main_nav_package_flutter:
    git:
      url: **************:discovery-ltd/v1-main-nav-package-flutter.git
      path: v1_main_nav_package_flutter
      ref: 0.0.106
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.365
  wheelspin_coins_rewards:
    git:
      url: **************:discovery-ltd/v1-wheelspin-coinsrewards-flutter.git
      path: wheelspin_coins_rewards
      ref: 0.0.51
  json_content_render:
    git:
      url: **************:discovery-ltd/v1-json-content-render-flutter.git
      path: json_content_render
      ref: 0.0.78
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.32
  health_assessment:
    git:
      url: **************:discovery-ltd/v1-health-assessment-flutter.git
      path: health_assessment
      ref: 0.0.125
  mental_health_assessment:
    git:
      url: **************:discovery-ltd/v1-mental-health-assessment-flutter.git
      path: mental_health_assessment
      ref: 0.0.93
  landing_pages:
    git:
      url: **************:discovery-ltd/v1-landing-pages-flutter.git
      path: landing_pages
      ref: 0.0.213
  registration_journey:
    git:
      url: **************:discovery-ltd/v1-registration-journey-flutter.git
      path: registration_journey
      ref: 0.0.342
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.316
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.58
  partner_unique_number_service_sdk:
    git:
      url: **************:discovery-ltd/v1-partner-unique-number-service-sdk-flutter.git
      path: partner_unique_number_service_sdk
      ref: master
  manage_content_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      path: manage_content_micro_service_sdk
      ref: main
  v1_feeds_card_builder_promotions:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builder-promotions-flutter.git
      path: v1_feeds_card_builder_promotions
      ref: 0.0.22
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.148
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.581
  featured_article:
    git:
      url: **************:discovery-ltd/v1-featured-article-flutter.git
      path: featured_article
      ref: 0.0.38
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.148
  manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-rewards-micro-service-sdk-flutter.git
      path: manage_rewards_micro_service_sdk
      ref: main
  v2_manage_rewards_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-rewards-micro-service-sdk-flutter.git
      path: v2_manage_rewards_micro_service_sdk
      ref: main
  manage_vitality_products_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-products-micro-service-sdk-flutter.git
      path: manage_vitality_products_micro_service_sdk
      ref: main
  giftcards:
    git:
      url: **************:discovery-ltd/v1-giftcards-flutter.git
      path: giftcards
      ref: 0.0.176
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.138
  v1_feeds_card_builder_ctas:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builder-ctas-flutter.git
      path: v1_feeds_card_builder_ctas
      ref: 0.0.35
  coins_to_fuelbar:
    git:
      url: **************:discovery-ltd/v1-coins-to-fuelbar-flutter.git
      path: coins_to_fuelbar
      ref: 0.0.80
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.74
  feeds_card_wrapper:
    git:
      url: **************:discovery-ltd/v1-feeds-card-wrapper-flutter.git
      path: feeds_card_wrapper
      ref: 0.0.3
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.135
  v1_feeds_rendering_api_module_flutter:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-api-module-flutter.git
      path: v1_feeds_rendering_api_module_flutter
      ref: 0.0.13
  profile_and_settings:
    git:
      url: **************:discovery-ltd/v1-profile-settings-flutter.git
      path: profile_and_settings
      ref: 0.0.216
  how_to_earn_points:
    git:
      url: **************:discovery-ltd/v1-how-to-earn-points-flutter.git
      path: how_to_earn_points
      ref: 0.0.67
  agreement_utility_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-agreement-utility-micro-service-sdk-flutter.git
      path: agreement_utility_micro_service_sdk
      ref: main
  points_history:
    git:
      url: **************:discovery-ltd/v1-points-history-flutter.git
      path: points_history
      ref: 0.0.147
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.58
  weekly_lifestyle_goals:
    git:
      url: **************:discovery-ltd/v1-weekly-lifestyle-goals-flutter.git
      path: weekly_lifestyle_goals
      ref: 0.0.215
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.243
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  manage_benefit_agreement_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-benefit-agreement-micro-service-sdk-flutter.git
      path: manage_benefit_agreement_micro_service_sdk
      ref: main
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.30
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.256
  healthy_food:
    git:
      url: **************:discovery-ltd/v1-healthy-food-flutter.git
      path: healthy_food
      ref: 0.0.62
  manage_party_core_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-party-core-micro-service-sdk-flutter.git
      path: manage_party_core_micro_service_sdk
      ref: main
  my_health:
    git:
      url: **************:discovery-ltd/v1-my-health-flutter.git
      path: my_health
      ref: 0.0.161
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.10
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      ref: 0.0.11
  v1_global_models_flutter:
    git:
      url: **************:discovery-ltd/v1-global-models-flutter.git
      path: global_models
      ref: 0.0.21
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.495
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.25
      path: vg_framework_contracts
  http_util:
    git:
      url: **************:discovery-ltd/v1-http-util-flutter.git
      path: http_util
      ref: 0.0.16
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.19
  physical_activity_goals:
    git:
      url: **************:discovery-ltd/v1-physical-activity-goals-flutter.git
      path: physical_activity_goals
      ref: 0.0.271
  goal_streaks_milestones:
    git:
      url: **************:discovery-ltd/v1-goal-streaks-milestones-flutter.git
      path: goal_streaks_milestones
      ref: 0.0.50
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  rewards_journey:
    git:
      url: **************:discovery-ltd/v1-rewards-journey-flutter.git
      path: rewards_journey
      ref: 0.0.120
  remote_app_runtime:
    git:
      url: **************:discovery-ltd/v1-remote-app-runtime-flutter.git
      path: remote_app_runtime
      ref: 0.0.4
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.1.2
  injectable_generator: ^2.4.1
  mockito: ^5.4.2
flutter:
  uses-material-design: true
  assets:
    - asset/
    - asset/market/
    - asset/central/
    - asset/module/
    - asset/images/
    - asset/lottie_json/
    - asset/certs/
