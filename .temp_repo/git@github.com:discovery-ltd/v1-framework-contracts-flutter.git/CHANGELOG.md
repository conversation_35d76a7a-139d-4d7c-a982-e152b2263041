# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.320] - 2025-08-21

* No notable changes on this release version. 

## [v0.0.319] - 2025-08-20

* No notable changes on this release version. 

## [v0.0.318] - 2025-08-19

* No notable changes on this release version. 

## [v0.0.317] - 2025-08-18

* No notable changes on this release version. 

## [v0.0.316] - 2025-08-15

* [SFSTRY0118441][Feeds] - Fix late initialization error for feed card status field
Change-Id: Ie287b57e4888667ffc5e83ab7997c5f4afb97dfb 

## [v0.0.315] - 2025-08-15

* No notable changes on this release version. 

## [v0.0.314] - 2025-08-14

* [SFSTRY0114624] - context parameter removed from feeds strategy 

## [v0.0.313] - 2025-08-14

* [SFSTRY0114624] - context parameter removed 

## [v0.0.312] - 2025-08-14

* No notable changes on this release version. 

## [v0.0.311] - 2025-08-13

* [SFSTRY0114624] - added context as a parameter in feeds container strategy

* [SFSTRY0114624] - added context as a parameter in CTA abstract class

* [SFSTRY0114624] - added context as a parameter in CTA abstract class 

## [v0.0.310] - 2025-08-13

* No notable changes on this release version. 

## [v0.0.309] - 2025-08-12

* No notable changes on this release version. 

## [v0.0.308] - 2025-08-11

* No notable changes on this release version. 

## [v0.0.307] - 2025-08-08

* No notable changes on this release version. 

## [v0.0.306] - 2025-08-07

* [SFSTRY0117247] - Updated local variable 

## [v0.0.305] - 2025-08-07

* No notable changes on this release version. 

## [v0.0.304] - 2025-08-06

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi locale
Change-Id: I46b0b4c54e19d3fc085e1651e0cd951064d26f05

* [SFSTRY0117247][FEEDS][MOBILE] - Updated feeds caching to support multi locale
Change-Id: I97c22973935f52d474df19e9c1c711922ece18b4 

## [v0.0.303] - 2025-07-31

* No notable changes on this release version. 

## [v0.0.302] - 2025-07-30

* No notable changes on this release version. 

## [v0.0.301] - 2025-07-29

* No notable changes on this release version. 

## [v0.0.300] - 2025-07-28

* No notable changes on this release version. 

## [v0.0.299] - 2025-07-25

* [SFSTRY0114781][Add a abstract method to clear selected schema] (#205) 

## [v0.0.298] - 2025-07-25

* No notable changes on this release version. 

## [v0.0.297] - 2025-07-24

* No notable changes on this release version. 

## [v0.0.296] - 2025-07-23

* No notable changes on this release version. 

## [v0.0.295] - 2025-07-22

* No notable changes on this release version. 

## [v0.0.294] - 2025-07-21

* No notable changes on this release version. 

## [v0.0.293] - 2025-07-18

* No notable changes on this release version. 

## [v0.0.292] - 2025-07-17

* No notable changes on this release version. 

## [v0.0.291] - 2025-07-16

* No notable changes on this release version. 

## [v0.0.290] - 2025-07-15

* No notable changes on this release version. 

## [v0.0.289] - 2025-07-14

* [SFSTRY0114542]- Add biometrics capability function

* [SFSTRY0114542]- Add biometrics capability function 

## [v0.0.288] - 2025-07-14

* No notable changes on this release version. 

## [v0.0.287] - 2025-07-11

* [SFSTRY0114542]- Add biometrics capability function 

## [v0.0.286] - 2025-07-11

* No notable changes on this release version. 

## [v0.0.285] - 2025-07-10

* [SFSTRY0113559][FEEDS][CTA] - Added new custom feed parameter
Change-Id: Idb13eff7881e353c702548feb43a4e9a90f9cceb 

## [v0.0.284] - 2025-07-10

* No notable changes on this release version. 

## [v0.0.283] - 2025-07-09

* No notable changes on this release version. 

## [v0.0.282] - 2025-07-08

* No notable changes on this release version. 

## [v0.0.281] - 2025-07-07

* No notable changes on this release version. 

## [v0.0.280] - 2025-07-04

* No notable changes on this release version. 

## [v0.0.279] - 2025-07-03

* No notable changes on this release version. 

## [v0.0.278] - 2025-07-01

* No notable changes on this release version. 

## [v0.0.277] - 2025-06-30

* No notable changes on this release version. 

## [v0.0.276] - 2025-06-27

* No notable changes on this release version. 

## [v0.0.275] - 2025-06-26

* No notable changes on this release version. 

## [v0.0.274] - 2025-06-25

* No notable changes on this release version. 

## [v0.0.273] - 2025-06-24

* No notable changes on this release version. 

## [v0.0.272] - 2025-06-23

* No notable changes on this release version. 

## [v0.0.271] - 2025-06-20

* No notable changes on this release version. 

## [v0.0.270] - 2025-06-19

* [SFSTRY0112556]- Change setOneSignalInAppOnCompleteListener() name for abstraction purposes 

## [v0.0.269] - 2025-06-19

* No notable changes on this release version. 

## [v0.0.268] - 2025-06-18

* [SFSTRY0112556]- add abstract InAppActionHandler for handling in-app actions 

## [v0.0.267] - 2025-06-18

* [SFSTRY0112556]- Add OneSignalInAppOnCompleteListener and pass URL in VGInAppCompletedResult 

## [v0.0.266] - 2025-06-18

* No notable changes on this release version. 

## [v0.0.265] - 2025-06-17

* No notable changes on this release version. 

## [v0.0.264] - 2025-06-16

* [SFSTRY0081567] - replaced getProductFeaturesByTypeKey with getProductFeatureApplicabilities 

## [v0.0.263] - 2025-06-16

* [SFSTRY0081567] - add getProductFeaturesByTypeKey to contract 

## [v0.0.262] - 2025-06-16

* No notable changes on this release version. 

## [v0.0.261] - 2025-06-13

* No notable changes on this release version. 

## [v0.0.260] - 2025-06-12

* No notable changes on this release version. 

## [v0.0.259] - 2025-06-11

* No notable changes on this release version. 

## [v0.0.258] - 2025-06-10

* No notable changes on this release version. 

## [v0.0.257] - 2025-06-09

* No notable changes on this release version. 

## [v0.0.256] - 2025-06-06

* No notable changes on this release version. 

## [v0.0.255] - 2025-06-05

* [SFSTRY0111485] - Remove msmr from platformCore 

## [v0.0.254] - 2025-06-05

* [SFSTRY0112498]-Updated vg-gateway-health-sdk-framework-contracts-flutter version 

## [v0.0.253] - 2025-06-05

* No notable changes on this release version. 

## [v0.0.252] - 2025-06-04

* No notable changes on this release version. 

## [v0.0.251] - 2025-06-03

* [SFSTRY0112263] - Added `forceDisableLogs` for `Log` to Cater for Forceful Suppression of Logs 

## [v0.0.250] - 2025-06-03

* No notable changes on this release version. 

## [v0.0.249] - 2025-06-02

* No notable changes on this release version. 

## [v0.0.248] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.247] - 2025-05-28

* No notable changes on this release version. 

## [v0.0.246] - 2025-05-27

* No notable changes on this release version. 

## [v0.0.245] - 2025-05-26

* No notable changes on this release version. 

## [v0.0.244] - 2025-05-23

* No notable changes on this release version. 

## [v0.0.243] - 2025-05-22

* No notable changes on this release version. 

## [v0.0.242] - 2025-05-21

* No notable changes on this release version. 

## [v0.0.241] - 2025-05-20

* No notable changes on this release version. 

## [v0.0.240] - 2025-05-19

* [SFSTRY0081567] - Updated msmr contracts reference
Change-Id: Ifa98771446932bced874bf2aa3f34b474de37dc4

* [SFSTRY0081567]-Updated msmr contracts reference

* [SFSTRY0081567] Added msmr as dependency.

* [SFSTRY0081567]-Added contracts for msmr 

## [v0.0.239] - 2025-05-19

* No notable changes on this release version. 

## [v0.0.238] - 2025-05-16

* No notable changes on this release version. 

## [v0.0.237] - 2025-05-15

* No notable changes on this release version. 

## [v0.0.236] - 2025-05-14

* No notable changes on this release version. 

## [v0.0.235] - 2025-05-13

* No notable changes on this release version. 

## [v0.0.234] - 2025-05-12

* No notable changes on this release version. 

## [v0.0.233] - 2025-05-09

* No notable changes on this release version. 

## [v0.0.232] - 2025-05-08

* No notable changes on this release version. 

## [v0.0.231] - 2025-05-07

* No notable changes on this release version. 

## [v0.0.230] - 2025-05-06

* No notable changes on this release version. 

## [v0.0.229] - 2025-05-05

* [SFSTRY0108364] - Added Abstract methods for Privacy Service 

## [v0.0.228] - 2025-05-05

* No notable changes on this release version. 

## [v0.0.227] - 2025-05-02

* No notable changes on this release version. 

## [v0.0.226] - 2025-05-01

* No notable changes on this release version. 

## [v0.0.225] - 2025-04-30

* No notable changes on this release version. 

## [v0.0.224] - 2025-04-29

* No notable changes on this release version. 

## [v0.0.223] - 2025-04-28

* No notable changes on this release version. 

## [v0.0.222] - 2025-04-25

* No notable changes on this release version. 

## [v0.0.221] - 2025-04-24

* No notable changes on this release version. 

## [v0.0.220] - 2025-04-24

* Update scheduled-bitrise-build.yml 

## [v0.0.219] - 2025-04-24

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline. 

## [v0.0.218] - 2025-04-24

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline. 

## [v0.0.217] - 2025-04-24

* No notable changes on this release version. 

## [v0.0.216] - 2025-04-23

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline.

* [SFSTRY0106076][DevOps - Engineering][CICD][POC] Investigate the addition of AccelQ into the DevOps pipeline. 

## [v0.0.215] - 2025-04-01

* [SFSTRY0106376] Dynatrace improvement for ApDex

* [SFSTRY0106376] Dynatrace improvement for ApDex 

## [v0.0.214] - 2025-03-18

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0104179]-Publishing the sdk flutter packages through github actions 

## [v0.0.213] - 2025-03-14

* [SFSTRY0104736] Include the locale in the headers of authorizedMemberInfo`. 

## [v0.0.212] - 2025-03-03

* [DFCT0040149] - Updated typeKey to nullable
Change-Id: I646e299ec7e12c56d55a20960a54596c7e8a4c17 

## [v0.0.211] - 2025-03-01

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct.

* [SFSTRY0100941] Testing parallel pipeline construct. 

## [v0.0.210] - 2025-02-28

* No notable changes on this release version. 

## [v0.0.209] - 2025-02-27

* No notable changes on this release version. 

## [v0.0.208] - 2025-02-26

* No notable changes on this release version. 

## [v0.0.207] - 2025-02-25

* No notable changes on this release version. 

## [v0.0.206] - 2025-02-24

* No notable changes on this release version. 

## [v0.0.205] - 2025-02-21

* [SFSTRY0100941] Add tenantId setter 

## [v0.0.204] - 2025-02-21

* No notable changes on this release version. 

## [v0.0.203] - 2025-02-20

* No notable changes on this release version. 

## [v0.0.202] - 2025-02-19

* [DFCT0043448] Refactor Session Manager Tokens and LoginResponse Setter

* [DFCT0043448] - Added updates on the session token contracts
Change-Id: I03731584020852f4680c79f3a0b800eb0ca71795

* [DFCT0043448] - Added updates on the session token contracts
Change-Id: Ia2c8083600ee25326264d00af7285ff7fdd0c76a 

## [v0.0.201] - 2025-02-19

* No notable changes on this release version. 

## [v0.0.200] - 2025-02-18

* No notable changes on this release version. 

## [v0.0.199] - 2025-02-17

* No notable changes on this release version. 

## [v0.0.198] - 2025-02-14

* No notable changes on this release version. 

## [v0.0.197] - 2025-02-13

* No notable changes on this release version. 

## [v0.0.196] - 2025-02-12

* No notable changes on this release version. 

## [v0.0.195] - 2025-02-11

* No notable changes on this release version. 

## [v0.0.194] - 2025-02-10

* No notable changes on this release version. 

## [v0.0.193] - 2025-02-07

* No notable changes on this release version. 

## [v0.0.192] - 2025-02-07

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration.

* [SFSTRY0102433] Test JFrog and GitHub Actions integration. 

## [v0.0.191] - 2025-02-07

* No notable changes on this release version. 

## [v0.0.190] - 2025-02-06

* [SFSTRY0102433]- add migrateSharedPrefToHive 

## [v0.0.189] - 2025-02-06

* No notable changes on this release version. 

## [v0.0.188] - 2025-02-05

* No notable changes on this release version. 

## [v0.0.187] - 2025-02-04

* No notable changes on this release version. 

## [v0.0.186] - 2025-02-03

* No notable changes on this release version. 

## [v0.0.185] - 2025-01-31

* No notable changes on this release version. 

## [v0.0.184] - 2025-01-30

* No notable changes on this release version. 

## [v0.0.183] - 2025-01-29

* No notable changes on this release version. 

## [v0.0.182] - 2025-01-28

* No notable changes on this release version. 

## [v0.0.181] - 2025-01-27

* No notable changes on this release version. 

## [v0.0.180] - 2025-01-24

* [SFSTRY0091585]-Resolved issues when running flutter analyze 

## [v0.0.179] - 2025-01-24

* No notable changes on this release version. 

## [v0.0.178] - 2025-01-23

* No notable changes on this release version. 

## [v0.0.177] - 2025-01-22

* No notable changes on this release version. 

## [v0.0.176] - 2025-01-21

* No notable changes on this release version. 

## [v0.0.175] - 2025-01-20

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.174] - 2025-01-20

* No notable changes on this release version. 

## [v0.0.173] - 2025-01-17

* No notable changes on this release version. 

## [v0.0.172] - 2025-01-16

* No notable changes on this release version. 

## [v0.0.171] - 2025-01-15

* No notable changes on this release version. 

## [v0.0.170] - 2025-01-14

* No notable changes on this release version. 

## [v0.0.169] - 2025-01-13

* No notable changes on this release version. 

## [v0.0.168] - 2025-01-10

* No notable changes on this release version. 

## [v0.0.167] - 2025-01-09

* No notable changes on this release version. 

## [v0.0.166] - 2025-01-08

* No notable changes on this release version. 

## [v0.0.165] - 2025-01-07

* No notable changes on this release version. 

## [v0.0.164] - 2025-01-06

* No notable changes on this release version. 

## [v0.0.163] - 2025-01-03

* No notable changes on this release version. 

## [v0.0.162] - 2025-01-02

* No notable changes on this release version. 

## [v0.0.161] - 2025-01-01

* No notable changes on this release version. 

## [v0.0.160] - 2024-12-31

* [SFSTRY0093492] - Adding capabilities for integrating HTTP 2 

## [v0.0.159] - 2024-12-31

* No notable changes on this release version. 

## [v0.0.158] - 2024-12-30

* No notable changes on this release version. 

## [v0.0.157] - 2024-12-27

* No notable changes on this release version. 

## [v0.0.156] - 2024-12-26

* No notable changes on this release version. 

## [v0.0.155] - 2024-12-25

* No notable changes on this release version. 

## [v0.0.154] - 2024-12-24

* No notable changes on this release version. 

## [v0.0.153] - 2024-12-23

* No notable changes on this release version. 

## [v0.0.152] - 2024-12-20

* No notable changes on this release version. 

## [v0.0.151] - 2024-12-19

* No notable changes on this release version. 

## [v0.0.150] - 2024-12-18

* No notable changes on this release version. 

## [v0.0.149] - 2024-12-17

* No notable changes on this release version. 

## [v0.0.148] - 2024-12-16

* [SFSTRY0093935] Add document and testing methods

* [SFSTRY0093935] Added modular box and deprecated unnecessary methods 

## [v0.0.147] - 2024-12-16

* No notable changes on this release version. 

## [v0.0.146] - 2024-12-13

* No notable changes on this release version. 

## [v0.0.145] - 2024-12-12

* [SFSTRY0086433][FEEDS] - Externalize container
Change-Id: Ifb30303ae65034ec64775cee86fd8f9d5f6b7e08

* [SFSTRY0086433][FEEDS] - Externalize container
Change-Id: Idd908fbd964c0b3395253de40f7cf982a6ceefba 

## [v0.0.144] - 2024-12-12

* No notable changes on this release version. 

## [v0.0.143] - 2024-12-11

* No notable changes on this release version. 

## [v0.0.142] - 2024-12-10

* [SFSTRY0098573]Testing Java 17 upgrade.

* [SFSTRY0098573]Testing Java 17 upgrade.

* [SFSTRY0098573]Testing Java 17 upgrade.

* [SFSTRY0098573]Testing Java 17 upgrade. 

## [v0.0.141] - 2024-12-10

* No notable changes on this release version. 

## [v0.0.140] - 2024-12-09

* No notable changes on this release version. 

## [v0.0.139] - 2024-12-06

* No notable changes on this release version. 

## [v0.0.138] - 2024-12-05

* No notable changes on this release version. 

## [v0.0.137] - 2024-12-04

* No notable changes on this release version. 

## [v0.0.136] - 2024-12-03

* No notable changes on this release version. 

## [v0.0.135] - 2024-12-02

* No notable changes on this release version. 

## [v0.0.134] - 2024-11-29

* No notable changes on this release version. 

## [v0.0.133] - 2024-11-28

* No notable changes on this release version. 

## [v0.0.132] - 2024-11-27

* No notable changes on this release version. 

## [v0.0.131] - 2024-11-26

* No notable changes on this release version. 

## [v0.0.130] - 2024-11-25

* No notable changes on this release version. 

## [v0.0.129] - 2024-11-22

* [DFCT0040560] - Adding additional parameter to the getFeedsCardsByFilter method.

* [DFCT0040560] - Adding additional parameter to the getFeedsCardsByFilter method. 

## [v0.0.128] - 2024-11-22

* No notable changes on this release version. 

## [v0.0.127] - 2024-11-21

* No notable changes on this release version. 

## [v0.0.126] - 2024-11-20

* No notable changes on this release version. 

## [v0.0.125] - 2024-11-19

* [SFSTRY0097087]-Updated the partyId encryption 

## [v0.0.124] - 2024-11-19

* [SFSTRY0097307] Add clear for storage manager 

## [v0.0.123] - 2024-11-19

* No notable changes on this release version. 

## [v0.0.122] - 2024-11-18

* No notable changes on this release version. 

## [v0.0.121] - 2024-11-15

* No notable changes on this release version. 

## [v0.0.120] - 2024-11-14

* No notable changes on this release version. 

## [v0.0.119] - 2024-11-13

* No notable changes on this release version. 

## [v0.0.118] - 2024-11-12

* [SFSTRY0093305] - Comments updated

* [SFSTRY0093305] - Deprecating the caching method from image_asset_provider and resource_bundle_provider because a new caching mechanism has been implemented in common_lib. 

## [v0.0.117] - 2024-11-12

* No notable changes on this release version. 

## [v0.0.116] - 2024-11-11

* No notable changes on this release version. 

## [v0.0.115] - 2024-11-08

* [SFFEAT0015068][DevOps][Modules][Alerting][Monitoring] Setup alerting on scheduled builds and send notifications to DT in case of unsuccessful builds. 

## [v0.0.114] - 2024-11-07

* [SFFEAT0015068][DevOps][Modules][Alerting][Monitoring] Setup alerting on scheduled builds and send notifications to DT in case of unsuccessful builds. 

## [v0.0.113] - 2024-11-07

* [SFSTRY0093933] Refactoring and removing PlatformCore to avoid cycle dependencies 

## [v0.0.112] - 2024-11-07

* [SFSTRY0093516] - Remove Android Alarms And reminders
- New version of the vg_contracts Removes all references of the Alarms and Reminders Mechanism 

## [v0.0.111] - 2024-11-04

* No notable changes on this release version. 

## [v0.0.110] - 2024-10-31

* [SFSTRY0095914]-Added method to get encrypted partyId and membershipId

* [SFSTRY0073866] - Expose new methods in ApplicationConfiguration

* [SFSTRY0073866] - Expose new methods in ApplicationConfiguration

* [SFSTRY0073866] - Code refactor

* [SFSTRY0073867]-Deleted the other contract

* [SFSTRY0073867]-Deleted the other contract

* [SFSTRY0073867]-Added contracts for application configuration

* [SFSTRY0073866] - Remove application_configuration_micro_service_sdk dependency and model added

* [SFSTRY0073867]-Added contracts for application configuration 

## [v0.0.109] - 2024-10-28

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines.

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines. 

## [v0.0.108] - 2024-10-15

* [SFSTRY0078729] - update Vg framework contracts to cater for : [SFSTRY0078729] Manual Functional testing for alarms and reminders and Android permissions on the GTB  central app
Work Done For SFSTRY0078729:
- introduced the alternate exception in order for the SDK Bridge and GTB to display different popups when failure is due to Android permissions missing and when due to failures during Google Auth flow (account selection and Google Permissions) 

## [v0.0.107] - 2024-10-09

* No notable changes on this release version. 

## [v0.0.106] - 2024-10-09

* No notable changes on this release version. 

## [v0.0.105] - 2024-10-09

* [SFSTRY0094874][DevOps] Assess if we can use ubuntu machines for the git release tag process. 

## [v0.0.104] - 2024-10-09

* [SFSTSK0021464]Migrate all the Packages from M1 to M2 Pro medium machines

* [SFSTSK0021464]Migrate all the Packages from M1 to M2 Pro medium machines 

## [v0.0.103] - 2024-10-04

* [SFSTRY0093305] - Added separate method for caching mechanism in resource bundle provider. 

## [v0.0.102] - 2024-09-30

* [DFCT0037918]- Renaissance online lesson issue

* [DFCT0037918]- Renaissance online lesson issue 

## [v0.0.101] - 2024-09-30

* [SFSTRY0092666] - added flag in ResourceBundleProvider for caching mechanism 

## [v0.0.100] - 2024-09-18

* [SFSTRY0089188] - Added certificate pinning enable toggle

* [SFSTRY0089188] - Added certificate pinning enable toggle

* [SFSTRY0089188] - Added certificate paths and exclusion from bad certificate logging as parameters

* [SFSTRY0089188]-Added host as parameter 

## [v0.0.99] - 2024-09-13

* [SFSTRY0091203] Deprecated export in the methods 

## [v0.0.98] - 2024-09-11

* [SFSTRY0073866] - ApplicationConfiguration contract updated 

## [v0.0.97] - 2024-09-10

* [SFSTRY0073866] - Remove unwanted dependencies

* [SFSTRY0073866] - Expose new methods in ApplicationConfiguration

* [SFSTRY0073866] - Code refactor

* [SFSTRY0073867]-Deleted the other contract

* [SFSTRY0073867]-Deleted the other contract

* [SFSTRY0073866] - Remove application_configuration_micro_service_sdk dependency and model added

* [SFSTRY0073867]-Added contracts for application configuration

* [SFSTRY0073867]-Added contracts for application configuration 

## [v0.0.96] - 2024-09-09

* [SFSTRY0090271] Testing M@ Mac Machines.

* [SFSTRY0088849] Testing message validation update. 

## [v0.0.95] - 2024-08-14

* [SFSTRY0089278]Added userAgent parameter in initAuthentication in session manager contracts.

* [SFSTRY0089278] Update UserAgent mapping 

## [v0.0.94] - 2024-08-14

* [SFSTRY0088098][FEEDS] - Added parameter for cache data
Change-Id: I255be50e4dbac7c7a9e21bb87de67c5f75aaa879

* [SFSTRY0048768][FEEDS] - Updates on feeds data models
Change-Id: I8283e2d119db9b7af0272c556eed7e8d8390a96a 

## [v0.0.93] - 2024-08-14

* [SFSTRY0089278] Update UserAgent mapping 

## [v0.0.92] - 2024-08-13

* [SFSTRY0089278] Add getter for UserAgent 

## [v0.0.91] - 2024-08-06

* [SFSTRY0088849] update commit validate script to include merges 

## [v0.0.90] - 2024-08-06

* [SFSTRY0087223] Adding new templates.

* [SFSTRY0087223] XCode 16 Migration. 

## [v0.0.89] - 2024-08-05

* [SFSTRY0087223] - modified invalidAccesstoken method in contenthub config

* [SFSTRY0087223] - modified invalidAccesstoken method in contenthub config 

## [v0.0.88] - 2024-08-05

* [SFSTRY0034937][SessionManager] - Getters for Tokens converted to Synchronous 

## [v0.0.87] - 2024-08-01

* [SFSTRY0087223] - added invalidAccesstoken method in contenthub config 

## [v0.0.86] - 2024-07-30

* [SFSTRY0087615] Storage Manager integration into platform core 

## [v0.0.85] - 2024-07-26

* [SFSTRY0044627] Remove realm, memory and hive storage type 

## [v0.0.84] - 2024-07-25

* [SFSTRY0078728] - update Vg framework contracts to cater for : [SFSTRY0078728] Update the GTB linking journey to accommodate the Android permissions  being rejected multiple times during Google fit linking
Happy path:
1. User clicks "link adapter" on Google Fit
2. Sees Android permissions, grants Android permissions, Link journey proceeds to Google permissions and Link is complete.

Unhappy Path:
1. User clicks "link adapter" on GF
2. user declines at least one of the Android permissions
3 linking fails, so the user can click "link adapter" again...
4 Missing Android permission(s) appear again and if user declines them again, Android no longer provides the popups for linking
5 user clicks link and the link attempt results in a "failed to link" message

Requirement:
A popup must be displayed with an option to take the user to the App Settings in order to grant the missing permissions. 

## [v0.0.83] - 2024-07-17

* [SFSTRY0082002] Adjustment for Main Nav Deeplinking 

## [v0.0.82] - 2024-07-16

* [SFSTRY0082002] Notification Updates

* Revert "Deeplink shellroute" 

## [v0.0.81] - 2024-07-16

* [SFSTRY0083942] - Optional Migration Parameter for Session Manager 

## [v0.0.80] - 2024-07-15

* [SFSTRY0082002] Notification Updates

* [SFSTRY0082002] Refactor route in notification 

## [v0.0.79] - 2024-07-12

* [SFSTRY0083943]- Add Isar storage type and contracts

* [SFSTRY0083943]- Add Isar storage type and contracts 

## [v0.0.78] - 2024-07-12

* [SFSTRY0083163][FEEDS] - added external container parameters
Change-Id: I7b435da6d3df72a312a58b40d906ba024e720c72 

## [v0.0.77] - 2024-07-11

* [SFSTRY0083163][FEEDS] - added feeds container contracts
Change-Id: Ib3a205688c4e3b08c90dcea9c26ce2742e862f41 

## [v0.0.76] - 2024-07-11

* [SFSTRY0083163][FEEDS] - added feeds container contracts
Change-Id: I3585faa85be297e5e74155b216ddd7c905821cfa 

## [v0.0.75] - 2024-07-10

* [SFSTRY0076847] Add new method of caching embedded images 

## [v0.0.74] - 2024-07-02

* [SFSTRY0082568][Feeds] - Adjusted filters for feeds rendering engine
Change-Id: I5c5b9e608e8bd6d48f6322557f5cc1d3c62e07b1 

## [v0.0.73] - 2024-06-30

* [SFSTRY0083623][Engineering - GTB] Pipeline improvements and betterments. Warning fix. 

## [v0.0.72] - 2024-06-28

* [SFSTRY0081895] - Exposed Setters and Getters for LoginResponse in Session Manager 

## [v0.0.71] - 2024-06-28

* [SFSTRY0078725] Refactor notification

* [SFSTRY0076387] Depcrecated adapters in initialize

* [SFSTRY0078725] Refactor in-app notification interfaces 

## [v0.0.70] - 2024-06-27

* [SFSTRY0079579][Integration] - Added contract for changeUsername
Change-Id: Ibc65af9ba9314d9c595042abc49cbf9b323e20fc

* [SFSTRY0079579][Integration] - Added contract for changeUsername
Change-Id: I22f8bd47984fa0f0bfb0d4947caca3292e5204d4

* [SFSTRY0079579][Integration] - Added contract for changeUsername
Change-Id: I210dd50a476e01e338bd85d5bab95728643d587d 

## [v0.0.69] - 2024-06-21

* [SFSTRY0082606] - Introduce cardFilter parameter in getFeedsCards 

## [v0.0.68] - 2024-06-21

* [SFSTRY0082802] - Added getArticleByUrlTitle method in CMSContract 

## [v0.0.67] - 2024-06-14

* [SFSTRY0082272][Engineering - GTB][v1-physical-activity-goals-flutter] Introducing code owners. 

## [v0.0.66] - 2024-06-12

* [SFSTSK0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test Final.

* [DFCT0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test DFCT.

* [DFCT008219][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test DFCT.

* [SFSTSK0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test 2

* [SFSTRY0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test 2

* [SFSTRY0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package. Test 1 

## [v0.0.65] - 2024-06-12

* [SFSTRY0082193][Engineering - GTB] Investigate duplicate checks on the frameworks contracts package.

* [SFSTRY0049631][Bitrise Pipeline] [v1-framework-contracts-flutter] Create Bitrise pipeline. 

## [v0.0.64] - 2024-06-12

* [SFSTRY0051096] - rewards card param make optional 

## [v0.0.63] - 2024-06-12

* [SFSTRY0051096] - rewards subtitle

* [SFSTRY0051096] - rewards subtitle 

## [v0.0.62] - 2024-06-10

* [SFSTRY0080971] Add validateCache 

## [v0.0.61] - 2024-06-06

* [SFSTRY0056262] - Refactor the code

* [SFSTRY0056262] - Refactor the code

* [SFSTRY0056262] - Update setAccessAndRefreshTokens methods parameters. 

## [v0.0.60] - 2024-06-05

* [SFSTRY0056262] - Added method to set access and refresh token in SessionManager

* [SFSTRY0056256] Caching Test 

## [v0.0.59] - 2024-05-31

* [SFSTRY0080527][Engineering - GTB] Minor alignments. Testing caching.

* [SFSTRY0080527][Engineering - GTB] Minor alignments. Testing caching.

* [SFSTRY0080527][Engineering - GTB] Minor alignments. Testing caching. 

## [v0.0.58] - 2024-05-22

* [SFSTRY0080527][Engineering - GTB] Minor alignments. Testing the PR process. 

## [v0.0.57] - 2024-05-20

* [SFSTRY0073582][RefreshToken] - added contract for refreshToken
Change-Id: I202b871157155b8cce264d45a2fdf45f68dcbe67 

## [v0.0.56] - 2024-05-18

* [SFSTRY0080338]On-board flutter packages via Bitrise. Minor corrections.

* [SFSTRY0080338]On-board flutter packages via Bitrise. Minor corrections. 

## [v0.0.55] - 2024-05-15

* [SFSTRY0074873] - Creating new contract due to renaming of
update pubspec.yaml
* [SFSTRY0074873] - Creating new contract due to renaming of framework
- Address PR Comments

* [SFSTRY0074873] - Creating new contract due to renaming of framework
- Remove Contracts
- Point to remote artifact vg_framework_contracts v 0.0.2

* [SFSTRY0074873] - Creating new contract due to renaming of framework
- Remove Contracts
- Point to remote artifact vg_framework_contracts v 0.0.2

* WIP: Remove Gateway Health SDK Contracts
- Remove Contracts
- Point to local Package (WIP) 

## [v0.0.54] - 2024-05-14

* [SFSTRY0076387] Depcrecated adapters in initialize 

## [v0.0.53] - 2024-05-14

* [SFSTRY0076387] Fixed .gitignore

* [SFSTRY0076387] Refactor user pref interface 

## [v0.0.52] - 2024-05-03

* [SFSTRY0079258][AuthSDK] - Added correct name for telephoneNumbers
Change-Id: Ia27121e2d92b74d63e60b29deb5ec633cba86b75 

## [v0.0.51] - 2024-04-17

* No notable changes on this release version. 

## [v0.0.50] - 2024-04-17

* No notable changes on this release version. 

## [v0.0.49] - 2024-04-17

* No notable changes on this release version. 

## [v0.0.48] - 2024-04-17

* No notable changes on this release version. 

## [v0.0.47] - 2024-04-17

* No notable changes on this release version. 
