import 'package:isar/isar.dart';

part 'get_feeds_cards_response_model.g.dart';

class GetFeedsCardsResponseModel {
  late List<FeedOutModel> feedOuts;

  GetFeedsCardsResponseModel();

  Map<String, dynamic> toJson() {
    return {'feedOuts': feedOuts.map((e) => e.toJson()).toList()};
  }

  GetFeedsCardsResponseModel.fromJson(Map<String, dynamic> json) {
    feedOuts = json['feedOuts']?.map<FeedOutModel>((json) => FeedOutModel.fromJson(json)).toList() ?? [];
  }

  @override
  String toString() {
    return 'GetFeedsCardsResponseModel(feedOuts: $feedOuts)';
  }
}

@collection
class FeedOutModel {
  FeedOutModel();

  late Id isarId = Isar.autoIncrement;
  late List<FeedCardOutModel> feedCardOuts;
  List<FeedAttributeModel>? feedAttributeses;
  @override
  late int hashCode;
  late int id;
  late String name;
  late String contextName;
  late String effectiveFrom;
  late String effectiveTo;
  int? typeKey;
  String locale = "";

  Map<String, dynamic> toJson() {
    return {
      'feedCardOuts': feedCardOuts.map((e) => e.toJson()).toList(),
      'feedAttributeses': feedAttributeses?.map((e) => e.toJson()).toList(),
      'hashCode': hashCode,
      'id': id,
      'name': name,
      'contextName': contextName,
      'effectiveFrom': effectiveFrom,
      'effectiveTo': effectiveTo,
      'typeKey': typeKey,
      'locale': locale,
    };
  }

  FeedOutModel.fromJson(Map<String, dynamic> json) {
    feedCardOuts = json['feedCardOuts']?.map<FeedCardOutModel>((json) => FeedCardOutModel.fromJson(json)).toList() ?? [];
    feedAttributeses = json['feedAttributeses']?.map<FeedAttributeModel>((json) => FeedAttributeModel.fromJson(json)).toList() ?? [];
    hashCode = int.parse(json['hashCode']);
    id = int.tryParse(json['id'].toString()) ?? -1;
    name = json['name'] ?? '';
    contextName = json['contextName'] ?? '';
    effectiveFrom = json['effectiveFrom'] ?? '';
    effectiveTo = json['effectiveTo'] ?? '';
    locale = json['locale'] ?? '';
    typeKey = int.tryParse(json['typeKey'].toString()) ?? -1;
  }

  @override
  String toString() {
    return 'FeedCardOutModel(feedCardOuts: $feedCardOuts, feedAttributeses: $feedAttributeses,'
        ' hashCode: $hashCode, id: $id, name: $name, contextName: $contextName, effectiveFrom: $effectiveFrom,'
        'effectiveTo: $effectiveTo, typeKey: $typeKey, locale: $locale)';
  }

  @override
  bool operator ==(Object other) {
    return super.hashCode == other.hashCode;
  }
}

@embedded
class FeedCardOutModel {
  FeedCardOutModel();

  late String applicabilityRule;
  late double defaultRankingScore;
  late String effectiveFrom;
  late String effectiveTo;
  late String feedItemHashCode;
  @override
  late int hashCode;
  late int id;
  late String name;
  late String? rankingRule;
  late double relevanceScore;
  late List<FeedItemAttributeModel> feedItemAttributes;
  late List<FeedCardImageModel> feedCardImages;
  late List<FeedCardIconModel> feedCardIcons;
  late List<FeedCardTextModel> feedCardTexts;
  late List<FeedCardAttributeOutModel> feedCardAttributeOuts;
  FeedCardStatusModel? feedCardStatus;
  late FeedCardTypeModel? feedCardType;

  Map<String, dynamic> toJson() {
    return {
      'applicabilityRule': applicabilityRule,
      'defaultRankingScore': defaultRankingScore,
      'effectiveFrom': effectiveFrom,
      'effectiveTo': effectiveTo,
      'feedItemHashCode': feedItemHashCode,
      'hashCode': hashCode,
      'id': id,
      'name': name,
      'rankingRule': rankingRule,
      'relevanceScore': relevanceScore,
      'feedItemAttributes': feedItemAttributes.map((e) => e.toJson()).toList(),
      'feedCardImages': feedCardImages.map((e) => e.toJson()).toList(),
      'feedCardIcons': feedCardIcons.map((e) => e.toJson()).toList(),
      'feedCardTexts': feedCardTexts.map((e) => e.toJson()).toList(),
      'feedCardAttributeOuts': feedCardAttributeOuts.map((e) => e.toJson()).toList(),
      'feedCardType': feedCardType?.toJson(),
      'feedCardStatus': feedCardStatus?.toJson()
    };
  }

  FeedCardOutModel.fromJson(Map<String, dynamic> json) {
    applicabilityRule = json['applicabilityRule'];
    defaultRankingScore = double.tryParse(json['defaultRankingScore'].toString()) ?? 0;
    effectiveFrom = json['effectiveFrom'];
    effectiveTo = json['effectiveTo'];
    feedItemHashCode = json['feedItemHashCode'];
    hashCode = int.tryParse(json['hashCode'].toString()) ?? -1;
    id = int.tryParse(json['id'].toString()) ?? -1;
    name = json['name'];
    rankingRule = json['rankingRule'];
    relevanceScore = double.tryParse(json['relevanceScore'].toString()) ?? 0;

    feedItemAttributes = json['feedItemAttributes']?.map<FeedItemAttributeModel>((json) => FeedItemAttributeModel.fromJson(json)).toList() ?? [];
    feedCardImages = json['feedCardImages']?.map<FeedCardImageModel>((json) => FeedCardImageModel.fromJson(json)).toList() ?? [];
    feedCardIcons = json['feedCardIcons']?.map<FeedCardIconModel>((json) => FeedCardIconModel.fromJson(json)).toList() ?? [];
    feedCardTexts = json['feedCardTexts']?.map<FeedCardTextModel>((json) => FeedCardTextModel.fromJson(json)).toList() ?? [];
    feedCardAttributeOuts = json['feedCardAttributeOuts']?.map<FeedCardAttributeOutModel>((json) => FeedCardAttributeOutModel.fromJson(json)).toList() ?? [];
    var feedCardTypeJson = json['feedCardType'];
    if (feedCardTypeJson != null) {
      feedCardType = FeedCardTypeModel.fromJson(feedCardTypeJson);
    } else {
      feedCardType = null;
    }
    var feedCardStatusJson = json['feedCardStatus'];
    if (feedCardStatusJson != null) {
      feedCardStatus = FeedCardStatusModel.fromJson(feedCardStatusJson);
    } else {
      feedCardStatus = null;
    }
  }

  @override
  String toString() {
    return 'FeedCardOutModel(applicabilityRule: $applicabilityRule, defaultRankingScore: $defaultRankingScore,'
        'effectiveFrom: $effectiveFrom, effectiveTo: $effectiveTo, feedItemHashCode: $feedItemHashCode,'
        'hashCode: $hashCode, id: $id, name: $name, rankingRule: $rankingRule, relevanceScore: $relevanceScore)';
  }

  @override
  bool operator ==(Object other) {
    return super.hashCode == other.hashCode;
  }
}

@embedded
class FeedItemAttributeModel {
  FeedItemAttributeModel();

  late int id;
  late int typeKey;
  late String typeName;
  late String value;

  Map<String, dynamic> toJson() {
    return {'id': id, 'typeKey': typeKey, 'typeName': typeName, 'value': value};
  }

  FeedItemAttributeModel.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? -1;
    typeKey = int.tryParse(json['typeKey'].toString()) ?? -1;
    typeName = json['typeName'];
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedItemAttributeModel(id: $id, typeKey: $typeKey, typeName: $typeName, value: $value)';
  }
}

@embedded
class FeedCardIconModel {
  FeedCardIconModel();

  late int id;
  late String name;
  late double referenceKey;
  late double sortOrder;
  late String value;

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'referenceKey': referenceKey, 'sortOrder': sortOrder, 'value': value};
  }

  FeedCardIconModel.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? -1;
    name = json['name'];
    referenceKey = double.tryParse(json['referenceKey'].toString()) ?? 0;
    sortOrder = double.tryParse(json['sortOrder'].toString()) ?? 0;
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedCardIconModel(id: $id, name: $name, referenceKey: $referenceKey, sortOrder: $sortOrder, value: $value)';
  }
}

@embedded
class FeedCardImageModel {
  FeedCardImageModel();

  late int id;
  late String name;
  late double referenceKey;
  late double? sortOrder;
  late String value;

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'referenceKey': referenceKey, 'sortOrder': sortOrder, 'value': value};
  }

  FeedCardImageModel.fromJson(Map<String, dynamic> json) {
    id = int.parse(json['id'].toString());
    name = json['name'];
    referenceKey = double.tryParse(json['referenceKey'].toString()) ?? 0;
    sortOrder = double.tryParse(json['sortOrder'].toString()) ?? 0;
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedCardImageModel(id: $id, name: $name, referenceKey: $referenceKey, sortOrder: $sortOrder, value: $value)';
  }
}

@embedded
class FeedCardTextModel {
  FeedCardTextModel();

  late int id;
  late String name;
  late double referenceKey;
  late double? sortOrder;
  late String value;

  Map<String, dynamic> toJson() {
    return {'id': id, 'name': name, 'referenceKey': referenceKey, 'sortOrder': sortOrder, 'value': value};
  }

  FeedCardTextModel.fromJson(Map<String, dynamic> json) {
    id = int.tryParse(json['id'].toString()) ?? -1;
    name = json['name'];
    referenceKey = double.tryParse(json['referenceKey'].toString()) ?? 0;
    sortOrder = double.tryParse(json['sortOrder'].toString()) ?? 0;
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedCardTextModel(id: $id, name: $name, referenceKey: $referenceKey, sortOrder: $sortOrder, value: $value)';
  }
}

@embedded
class FeedCardStatusModel {
  FeedCardStatusModel();

  late String code;
  late int key;
  late String name;

  Map<String, dynamic> toJson() {
    return {'code': code, 'key': key, 'name': name};
  }

  FeedCardStatusModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    key = int.tryParse(json['key'].toString()) ?? -1;
    name = json['name'];
  }

  @override
  String toString() {
    return 'FeedCardStatusModel(code: $code, key: $key, name: $name)';
  }
}

@embedded
class FeedCardTypeModel {
  FeedCardTypeModel();

  late String code;
  late int key;
  late String name;

  Map<String, dynamic> toJson() {
    return {'code': code, 'key': key, 'name': name};
  }

  FeedCardTypeModel.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    key = int.tryParse(json['key'].toString()) ?? -1;
    name = json['name'];
  }

  @override
  String toString() {
    return 'FeedCardTypeModel(code: $code, key: $key, name: $name)';
  }
}

@embedded
class FeedCardAttributeOutModel {
  FeedCardAttributeOutModel();

  late String name;
  late String value;

  Map<String, dynamic> toJson() {
    return {'name': name, 'value': value};
  }

  FeedCardAttributeOutModel.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedCardAttributeOutModel(name: $name, value: $value)';
  }
}

@embedded
class FeedAttributeModel {
  FeedAttributeModel();

  late int typeKey;
  late String typeName;
  late String value;

  Map<String, dynamic> toJson() {
    return {'typeKey': typeKey, 'typeName': typeName, 'value': value};
  }

  FeedAttributeModel.fromJson(Map<String, dynamic> json) {
    typeKey = int.tryParse(json['typeKey'].toString()) ?? -1;
    typeName = json['typeName'];
    value = json['value'];
  }

  @override
  String toString() {
    return 'FeedAttributeModel(typeKey: $typeKey, typeName: $typeName, value: $value)';
  }
}
