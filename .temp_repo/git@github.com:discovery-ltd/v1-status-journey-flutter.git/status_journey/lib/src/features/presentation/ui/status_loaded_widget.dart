import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';
import 'package:gutenberg_shared_package/common/preference_utils.dart';
import 'package:gutenberg_shared_package/common/vg_navigator.dart';
// ignore: implementation_imports
import 'package:help_faq/src/utils/route_config_key.dart' as help_faq_route;
import 'package:intl/intl.dart';
import 'package:platform_core/platform_core.dart';
import 'package:status_journey/src/common/utils/config_key.dart';
import 'package:status_journey/src/common/utils/extensions.dart';
import 'package:status_journey/src/common/utils/extensions/status_detail_feed_card_override_extension.dart';
import 'package:status_journey/src/common/utils/localization_helpers.dart';
import 'package:status_journey/src/common/values/constants.dart';
import 'package:status_journey/src/common/values/images.dart';
import 'package:status_journey/src/features/presentation/bloc/status_landing_bloc.dart';
import 'package:status_journey/src/features/presentation/bloc/status_landing_event.dart';
import 'package:status_journey/src/features/presentation/bloc/status_landing_state.dart';
import 'package:status_journey/src/features/presentation/ui/status_details_modal_screen.dart';
import 'package:v1_journey_commons_flutter/journey_commons.dart';
import 'package:v1_modular_app_flutter/modular_app.dart';

import '../../../../status_journey_pages_routes.dart';

class StatusLoadedWidget extends StatelessWidget {
  const StatusLoadedWidget({
    super.key,
    required this.pointsState,
    required this.bloc,
  });

  final StatusLandingState pointsState;
  final StatusLandingBloc bloc;

  @override
  Widget build(BuildContext context) {
    var localization =
        VGLocator.get<PlatformCore>().resourceProvider.localization(context)!;
    final l18Helper = LocalizationHelpers(localization: localization);
    bool showPointSuffixFuelBar = PreferenceUtils()
            .getGlobalRouteConfig()[ConfigKey.showPointSuffix.name] ??
        false;

    List<Widget> carouselWidgets() {
      List<String> lengthBlocks =
          pointsState.profileResponse[kListOfLabelsTypeCode];
      List<Widget> list = [];
      for (var i = 0; i < lengthBlocks.length; i++) {
        var statusBadgePath = "status${lengthBlocks[i].toCapitalize()}";
        var vitalityStatusBadge = "";
        if (bloc.carouselColorMap.containsKey(statusBadgePath)) {
          vitalityStatusBadge = bloc.carouselColorMap[statusBadgePath];
        }
        list.add(ImageBuilder(vitalityStatusBadge,
            builder: (ImageProvider imageProvider) {
          return Container(
            color: Colors.white,
            child: VGImage(
              image: imageProvider,
              fit: BoxFit.fitHeight,
              height: 45,
              width: 50,
            ),
          );
        },
            placeHolder: Assets.images.placeholder.keyName,
            noImagePlaceholder: Assets.images.noImagePlaceholder.keyName));
        // list.add(SvgPicture.asset(vitalityStatusBadge));
      }
      return list;
    }

    return SingleChildScrollView(
      child: Container(
        color: VGApplicationTheme.colors.base0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            VGProfileHeader(
              statusMedalIcon: ImageBuilder(
                pointsState.profileResponse[kVitalityStatusBadge],
                builder: (ImageProvider imageProvider) {
                  return Container(
                    color: Colors.white,
                    child: VGImage(
                      image: imageProvider,
                      fit: BoxFit.cover,
                    ),
                  );
                },
                placeHolder: Assets.images.placeholder.keyName,
                noImagePlaceholder: Assets.images.noImagePlaceholder.keyName,
              ),
              headerStatusTitle: localization.valueOf(
                  key: VGResourceBundleKey.txtvitalityoverallstatus_001.key,
                  params: [
                    pointsState.profileResponse[kCurrentVitalityStatus]
                  ]),
              numberOfSegments: pointsState.profileResponse[kNumberOfSegments],
              labels: pointsState.profileResponse[kLabelsOfStatus],
              pointSuffix: localization.valueOf(
                  key: VGResourceBundleKey.ctrl_txt_pts_001.key),
              currentValue: pointsState.profileResponse[kTotalPointsEarned],
              totalValue: pointsState.profileResponse[kTotalValue],
              checkIndexValue: bloc.isCurrentMembershipYear == true
                  ? null
                  : bloc.fetchCurrentStatusIndex(),
              // ignore: deprecated_member_use
              checkValue: bloc.isCurrentMembershipYear == true
                  ? null
                  : bloc.fetchCurrentStatusIndex(),
              fuelBarGeneric: VGFuelBar.unEqualInterval(
                currentValue: pointsState.profileResponse[kTotalPointsEarned],
                checkIndexValue: bloc.isCurrentMembershipYear == true
                    ? null
                    : bloc.fetchCurrentStatusIndex(),
                segments: bloc.getListOfStatus(pointsState.profileResponse[
                    "getPointsToMaintainOrHigherStatusOutboundPayload"]),
                numberFormat: () {
                  final innerKey = localization.valueOf(
                      key: VGResourceBundleKey
                          .how_to_earn_points_points_number_format.key);
                  final numberFormatCode = PreferenceUtils()
                          .getGlobalRouteConfig()['numberFormatCode'] ??
                      "en";
                  final formatPattern = localization.valueOf(key: innerKey);
                  return NumberFormat(formatPattern, numberFormatCode);
                }(),
                pointSuffix: !showPointSuffixFuelBar
                    ? localization.valueOf(
                        key: VGResourceBundleKey.ctrl_txt_pts_001.key)
                    : "",
              ),
            ),
            const SizedBox(
              height: 24,
            ),
            Padding(
              padding: const EdgeInsets.only(
                  top: 16.0, left: 24.0, right: 24.0, bottom: 24.0),
              child: VGButton.custom(
                semanticsProperties: SemanticsProperties(
                  button: true,
                  label: VGResourceBundleKey
                      .ctrl_txt_yourcurrentmembershipyear_001.key,
                ),
                child: VGMembershipCard(
                  membershipText: localization.valueOf(
                      key: VGResourceBundleKey
                          .ctrl_txt_yourcurrentmembershipyear_001.key),
                  pointsEarned: NumberFormatUtils.numberFormat(
                      VGResourceBundleKey
                          .how_to_earn_points_points_number_format.key,
                      int.tryParse(pointsState
                              .profileResponse[kTotalPointsEarned]
                              .toString()) ??
                          0,
                      context),
                  daysLeft: pointsState.profileResponse[kDaysLeft],
                  pointsEarnedText: localization.valueOf(
                      key: VGResourceBundleKey
                          .ctrl_txt_totalpointsearned_001.key),
                  daysLeftText: localization.valueOf(
                      key: VGResourceBundleKey.ctrl_txt_daysleft_001.key),
                ),
                onTap: () {
                  VGBottomSheet.show(
                    context: context,
                    isScrollControlled: true,
                    useSafeArea: true,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(28),
                        topRight: Radius.circular(28),
                      ),
                    ),
                    child: VGInheritedTopLevelWidget(
                      journeyId: StatusJourneyPagesRoutes.journeyId,
                      child: StatusDetailsModalScreen(
                        statusResponse: pointsState.profileResponse,
                        pointStatus: bloc.pointStatus,
                        currentMembershipYear: bloc.isCurrentMembershipYear,
                      ),
                    ),
                  );
                },
              ),
            ),
            Visibility(
              visible: bloc.isCurrentMembershipYear == false &&
                  bloc.previousPeriodPoints == true,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const FaIcon(
                    FontAwesomeIcons.lightStarSharp,
                    size: 16,
                  ),
                  const SizedBox(width: 12),
                  VGText(localization.valueOf(
                      key: VGResourceBundleKey.txt_foot_note.key,
                      params: [
                        "${pointsState.profileResponse["pointsTotal"]}"
                      ]))
                ],
              ),
            ),
            const VGListSpacer(),
            Visibility(
              visible: bloc.rewardsStatusCarouselVisibility(),
              child: Container(
                padding: const EdgeInsets.only(
                    top: 16.0, left: 24.0, right: 24.0, bottom: 24.0),
                alignment: Alignment.centerLeft,
                child: VGRewardsStatusCarousel(
                  scalableValue: 0.6,
                  onTap: (index) {
                    bloc.updateStatusRewards(index);
                  },
                  checkWidget: ImageBuilder(Images.checkImage,
                      builder: (ImageProvider imageProvider) {
                    return Container(
                      color: Colors.transparent,
                      child: VGImage(
                        image: imageProvider,
                        fit: BoxFit.scaleDown,
                        height: 15,
                        width: 15,
                      ),
                    );
                  },
                      placeHolder: Assets.images.placeholder.keyName,
                      noImagePlaceholder:
                          Assets.images.noImagePlaceholder.keyName),
                  chevronSize: 15,
                  positions: CheckMarkPositioned(right: 3, bottom: 8),
                  carouselWidgets: carouselWidgets(),
                  currentLevel: bloc.fetchCurrentLevel(
                      pointsState.profileResponse[kCurrentVitalityStatusCode]),
                  requiredLevels: bloc.fetchRequiredLevels(),
                ),
              ),
            ),
            Visibility(
              visible: bloc.statusRewardsConfig,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  VGHeader.sectionHeader4(
                      headerText: bloc.pointStatus == true
                          ? localization.valueOf(
                              key: VGResourceBundleKey
                                  .txt_your_insurance_reward_001.key,
                            )
                          : "${pointsState.selectedStatus} ${localization.valueOf(
                                key: VGResourceBundleKey
                                    .txtstatusrewards_001.key,
                              ).toLowerCase()}"),
                  Container(
                    padding: const EdgeInsets.only(left: 16, top: 8),
                    alignment: Alignment.centerLeft,
                    child: Visibility(
                      visible: pointsState.isStatusChipVisible ?? false,
                      child: VGChip.stateIndicator(
                        key: const ValueKey("vgChip"),
                        label: bloc.pointStatus == true
                            ? "${pointsState.profileResponse[kCurrentVitalityStatus]} ${localization.valueOf(
                                  key: VGResourceBundleKey
                                      .txtvitalitystatus_001.key,
                                ).toLowerCase()}"
                            : localization.valueOf(
                                key: VGResourceBundleKey
                                    .ctrl_txt_yourrewardlevel_001.key),
                        chipSize: VGChipSize.small,
                      ),
                    ),
                  ),

                  checkExtensionForStatusDetailOverride(
                      context: context,
                      selectedStatus: pointsState.selectedStatus),

                  ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount:
                        pointsState.selectedFeedsModel?.cards.length ?? 0,
                    itemBuilder: (c, index) {
                      return pointsState.selectedFeedsModel?.cards[index];
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          child: Divider(
                            height: 1,
                            thickness: 1,
                            color: VGApplicationTheme.colors.base300,
                          ));
                    },
                  ),
                  // const SizedBox(height: 8),
                  const VGListSpacer(
                    padding: EdgeInsets.only(top: 0),
                  ),
                ],
              ),
            ),
            VGHeader.sectionHeader4(
                headerText: localization.valueOf(
                    key: VGResourceBundleKey
                        .ctrl_txt_activitiestoearnpts_001.key)),
            VGButton.custom(
              semanticsProperties: SemanticsProperties(
                button: true,
                label: VGResourceBundleKey.ctrl_txt_keepearningpts_001.key,
              ),
              onTap: () {
                bloc.add(NavigateNewScreen(
                    isLanding: true,
                    onCallBack: (value) {
                      context.showGenericErrorDialog(value ?? '', l18Helper);
                    }));
                // VGNavigator.push("/activities/landing_screen");
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    top: 8.0, bottom: 8.0, left: 16.0, right: 16.0),
                child: Container(
                  width: MediaQuery.of(context).size.width,
                  height: 132,
                  decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: const BorderRadius.all(Radius.circular(12)),
                      boxShadow: [VGApplicationTheme.shadows.shadow5]),
                  // margin: const EdgeInsets.only(left: 0),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.only(right: 16.0),
                            child: ImageBuilder(
                              Images.statusLandingImage,
                              builder: (ImageProvider imageProvider) {
                                return Container(
                                  height: 80,
                                  width: 80,
                                  color: Colors.white,
                                  child: VGImage(
                                    image: imageProvider,
                                    fit: BoxFit.cover,
                                  ),
                                );
                              },
                              placeHolder: Assets.images.placeholder.keyName,
                              noImagePlaceholder:
                                  Assets.images.noImagePlaceholder.keyName,
                            ),
                          ),
                          Expanded(
                            child: Column(
                              children: [
                                SizedBox(
                                  width: 230,
                                  child: VGText(
                                    localization.valueOf(
                                        key: VGResourceBundleKey
                                            .ctrl_txt_keepearningpts_001.key),
                                    style: VGApplicationTheme.typography.h4
                                        .copyWith(
                                            color: VGApplicationTheme
                                                .colors.base900,
                                            decoration: TextDecoration.none),
                                    textAlign: TextAlign.start,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                  ),
                                ),
                                const SizedBox(
                                  height: 4,
                                ),
                                SizedBox(
                                  width: 230,
                                  child: VGText(
                                    localization.valueOf(
                                        key: VGResourceBundleKey
                                            .ctrl_txt_thehealthieryouare_001
                                            .key),
                                    style: VGApplicationTheme.typography.pSmall
                                        .copyWith(
                                            color: VGApplicationTheme
                                                .colors.base900,
                                            decoration: TextDecoration.none),
                                    maxLines: 2,
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      )
                    ],
                  ),
                ),
              ),
            ),
            const VGListSpacer(),
            (VGLocator.get<PreferenceUtils>().getGlobalRouteConfig()[
                        shouldShowRelatedFAQsCTAButton] ??
                    true)
                ? VGList(
                    key: const ValueKey("relatedFaq"),
                    title: localization.valueOf(
                        key: VGResourceBundleKey.ctrl_txt_relatedfaqs_001.key),
                    // coverage:ignore-start
                    onTap: () {
                      bloc.add(NavigateNewScreen(
                          isLanding: false,
                          onCallBack: (value) {
                            context.showGenericErrorDialog(
                                value ?? '', l18Helper);
                          }));
                    },
                    // coverage:ignore-end
                  )
                : VGLeftIconText(
                    contentText: localization.valueOf(
                        key: VGResourceBundleKey.ctrl_helpcentre_001.key),
                    contextStyle: VGApplicationTheme.typography.pMedium,
                    rightWidget: const Icon(Icons.chevron_right),
                    onTap: () {
                      VGNavigator.pushNamed(
                          PreferenceUtils().getGlobalRouteConfig()[
                              help_faq_route
                                  .RouteConfigKey.helpCenterLanding.name]);
                    },
                  ),
          ],
        ),
      ),
    );
  }

  checkExtensionForStatusDetailOverride(
      {required BuildContext context, required String selectedStatus}) {
    try {
      final customBuildContentOutletExtension = appHost.extensionRegistry
          .findFirstExtension<StatusDetailFeedCardOverrideExtension>(
              extensionPoint:
                  "status_journey.status_details.status_widget_override");
      if (customBuildContentOutletExtension != null) {
        return customBuildContentOutletExtension.builder(
            context: context, selectedStatus: selectedStatus);
      }

      return const SizedBox.shrink();
    } catch (e) {
      uiEventDelegate?.logException(
        exceptionType: e.runtimeType.toString(),
        exceptionMessage: e.toString(),
        customUserTag:
            'Status Journey-StatusLoadedWidget: checkExtensionForStatusDetailOverride',
        severity: "High",
      );
      return const SizedBox.shrink();
    }
  }
}
