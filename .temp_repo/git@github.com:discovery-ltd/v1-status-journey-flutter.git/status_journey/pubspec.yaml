name: "status_journey"
description: "Module repository for GTB Project."
version: 0.0.78
homepage: "https://github.com/discovery-ltd/v1-status-journey-flutter"
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=2.17.0"
dependencies:
  flutter:
    sdk: flutter
  go_router: ^10.1.2
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  injectable: ^2.3.1
  url_launcher: ^6.0.6
  get_it: ^7.6.4
  collection: ^1.17.1
  permission_handler: ^10.2.0
  secure_shared_preferences: ^0.0.4
  package_info_plus: ^4.2.0
  http: 1.1.0
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.11
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.543
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.150
  home_screen_points_agreement_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-home-screen-points-agreement-micro-service-sdk-flutter.git
      path: home_screen_points_agreement_micro_service_sdk
  party_party_information_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-party-party-information-services-micro-service-sdk-flutter.git
      path: party_party_information_services_micro_service_sdk
      ref: main
  integration_platform_services_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-integration-platform-services-micro-service-sdk-flutter.git
      path: integration_platform_services_micro_service_sdk
      ref: main
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  v2_manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-party-information-micro-service-sdk-flutter.git
      path: v2_manage_party_information_micro_service_sdk
      ref: main
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-asset-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.50
  authentication:
    git:
      path: authentication
      url: **************:discovery-ltd/v1-authentication-plugin.git
      ref: 0.0.71
  common_lib:
    git:
      path: common_lib
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      ref: 0.0.136
  manage_vitality_points_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vitality-points-micro-service-sdk-flutter.git
      path: manage_vitality_points_micro_service_sdk
      ref: main
  manage_party_core_micro_service_sdk:
    git:
      path: manage_party_core_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-party-core-micro-service-sdk-flutter.git
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.217
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.132
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.128
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  intl: ^0.18.1
  cookie_jar: ^4.0.5
  app_settings: ^5.1.1
  carousel_slider: ^5.0.0
dependency_overrides:
  app_settings: 5.1.1
  injectable_generator: ^2.4.1
  intl: ^0.18.1
  flutter_secure_storage: ^8.0.0
  http: 1.1.0
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.11
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.543
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.136
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.150
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-asset-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.50
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  v2_manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v2-manage-party-information-micro-service-sdk-flutter.git
      path: v2_manage_party_information_micro_service_sdk
      ref: main
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.71
  manage_vitality_products_micro_service_sdk:
    git:
      path: manage_vitality_products_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-vitality-products-micro-service-sdk-flutter.git
      ref: main
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.457
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.217
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.15
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.323
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.56
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.132
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.16
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.145
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.17
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.128
dev_dependencies:
  flutter_secure_storage: ^8.0.0
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: 2.4.13
  analyzer: ^5.13.0
  bloc_test: ^9.1.4
  mockito: ^5.4.2
  modular_test: ^2.0.0
  injectable_generator: ^2.4.1
  shared_preferences_platform_interface: ^2.2.0
  connectivity_plus_platform_interface: ^1.2.4
  plugin_platform_interface: ^2.1.6
  mocktail: ^1.0.3
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
    - assets/images/bronze_notselected.png
    - assets/images/wheel.svg
    - assets/images/bronze_status_medal.svg
    - assets/images/silver_status_medal.svg
    - assets/images/gold_status_medal.svg
    - assets/images/asset_medal_bronze.svg
    - assets/images/asset_medal_blue.svg
    - assets/images/asset_medal_silver.svg
    - assets/images/asset_medal_gold.svg
    - assets/images/asset_medal_platinum.svg
    - assets/images/circle-check.svg
    - assets/images/asset_activity.svg
    - assets/images/tipsIllustration_header.svg
