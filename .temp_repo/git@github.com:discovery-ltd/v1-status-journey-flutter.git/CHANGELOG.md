# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.78] - 2025-08-12

* [SFSTRY0117084][<PERSON><PERSON><PERSON>][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] (#90)
* [SFSTRY0117084][<PERSON><PERSON><PERSON>][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)]

* [SFSTRY0117084][<PERSON><PERSON><PERSON>][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v0.0.77] - 2025-08-07

* [DFCT0047116]  Add number format 

## [v0.0.76] - 2025-07-31

* [SFSTRY0114788][<PERSON><PERSON><PERSON>][Status Journey - Update analytic manager version]

* [SFSTRY0114788][G<PERSON><PERSON>][Status Journey - Replaced pointsStatusTypeName with pointsStatusTypeCode to cater for the different locale] 

## [v0.0.75] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v0.0.74] - 2025-06-17

* [DFCT0044016]  Update Pod file

* [DFCT0044016] - Status - Update libraries version 

## [v0.0.73] - 2025-03-20

* [DFCT0044952][SLI]-DateFormat-Configured-In Status Details screen Added Common function and Testcases

* [DFCT0044952][SLI]-DateFormat-Configured-In Status Details screen Add new key in resource bundle 

## [v0.0.72] - 2025-03-17

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.71] - 2025-03-12

* [SFSTRY0094159] - Status - Update CDN Version 

## [v0.0.70] - 2025-03-07

* [SFSTRY0094159] - Status - Flutter Upgrade 

## [v0.0.69] - 2025-03-03

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226] - Status - Status Details build isn't working due to 'An unknown server-side error occurred while processing the command' error 

## [v0.0.68] - 2025-02-25

* [DFCT0044220] - Status - Status Details build isn't working due to 'An unknown server-side error occurred while processing the command' error 

## [v0.0.67] - 2025-02-18

* [SFSTRY0094205] - Status - Modules Override issue 

## [v0.0.66] - 2025-02-13

* [SFSTRY0094205] - Refactored code to follow clean architecture 

## [v0.0.65] - 2025-02-06

* [SFSTRY0094205] - Status - Error Handling for OFE journey with Dynatrace 

## [v0.0.64] - 2025-01-24

* [SFSTRY0094215] - Status - Error Handling 

## [v0.0.63] - 2025-01-22

* [SFSTRY0100540] - integrate event tracking on multiple elements of StatusJourneyLanding 

## [v0.0.62] - 2025-01-22

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.61] - 2025-01-21

* [DFCT0042843] - Add Semantics labal for 'You are on Blue Status and need....' text 

## [v0.0.60] - 2025-01-17

* [SFSTRY0094215] - Error Handling for Status journey using dynatrace 

## [v0.0.59] - 2025-01-17

* [SFSTRY0094092][DFCT0042580][T-31][PROD][UAT][GTB-ECU][VitalityStatus]- Tu ano de membresia actual - "insurance status rewards" is not translated  under Como funciona la categoria (#68) 

## [v0.0.58] - 2025-01-02

* [SFSTRY0099377] - Status Journey: Replace Preferences with Session Manager 

## [v0.0.57] - 2024-12-23

* [SFSTRY0098918] - Added a "selected status" to the extension. 

## [v0.0.56] - 2024-12-19

* [SFSTRY0098918] - how it works CTA card for AIA IND market handled with extension point 

## [v0.0.55] - 2024-11-26

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code 

## [v0.0.54] - 2024-11-20

* [DFCT0038212] - version fixes

* [DFCT0038212] - test case fixes

* [DFCT0038212] - status journey login issue

* [SFSTRY0094088] - thousands delimiter is set as comma

* [DFCT0038212] - Translation - Status New Changes:

## [v0.0.78] - 2025-08-12

* [SFSTRY0117084][Gutenberg][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] (#90)
* [SFSTRY0117084][Gutenberg][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)]

* [SFSTRY0117084][Gutenberg][Status Journey - safe area inset bottom in mobile is overlayed on the content of bottom sheet across the application (Android)] 

## [v0.0.77] - 2025-08-07

* [DFCT0047116]  Add number format 

## [v0.0.76] - 2025-07-31

* [SFSTRY0114788][Gutenberg][Status Journey - Update analytic manager version]

* [SFSTRY0114788][Gutenberg][Status Journey - Replaced pointsStatusTypeName with pointsStatusTypeCode to cater for the different locale] 

## [v0.0.75] - 2025-07-25

* [DFCT0047099] - Discrepancy in Logo Display Based on Current Membership Status[ICS1165021][IGI][GTB] 

## [v0.0.74] - 2025-06-17

* [DFCT0044016]  Update Pod file

* [DFCT0044016] - Status - Update libraries version 

## [v0.0.73] - 2025-03-20

* [DFCT0044952][SLI]-DateFormat-Configured-In Status Details screen Added Common function and Testcases

* [DFCT0044952][SLI]-DateFormat-Configured-In Status Details screen Add new key in resource bundle 

## [v0.0.72] - 2025-03-17

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.71] - 2025-03-12

* [SFSTRY0094159] - Status - Update CDN Version 

## [v0.0.70] - 2025-03-07

* [SFSTRY0094159] - Status - Flutter Upgrade 

## [v0.0.69] - 2025-03-03

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226]  solve login validation issue

* [DFCT0044226] - Status - Status Details build isn't working due to 'An unknown server-side error occurred while processing the command' error 

## [v0.0.68] - 2025-02-25

* [DFCT0044220] - Status - Status Details build isn't working due to 'An unknown server-side error occurred while processing the command' error 

## [v0.0.67] - 2025-02-18

* [SFSTRY0094205] - Status - Modules Override issue 

## [v0.0.66] - 2025-02-13

* [SFSTRY0094205] - Refactored code to follow clean architecture 

## [v0.0.65] - 2025-02-06

* [SFSTRY0094205] - Status - Error Handling for OFE journey with Dynatrace 

## [v0.0.64] - 2025-01-24

* [SFSTRY0094215] - Status - Error Handling 

## [v0.0.63] - 2025-01-22

* [SFSTRY0100540] - integrate event tracking on multiple elements of StatusJourneyLanding 

## [v0.0.62] - 2025-01-22

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.61] - 2025-01-21

* [DFCT0042843] - Add Semantics labal for 'You are on Blue Status and need....' text 

## [v0.0.60] - 2025-01-17

* [SFSTRY0094215] - Error Handling for Status journey using dynatrace 

## [v0.0.59] - 2025-01-17

* [SFSTRY0094092][DFCT0042580][T-31][PROD][UAT][GTB-ECU][VitalityStatus]- Tu ano de membresia actual - "insurance status rewards" is not translated  under Como funciona la categoria (#68) 

## [v0.0.58] - 2025-01-02

* [SFSTRY0099377] - Status Journey: Replace Preferences with Session Manager 

## [v0.0.57] - 2024-12-23

* [SFSTRY0098918] - Added a "selected status" to the extension. 

## [v0.0.56] - 2024-12-19

* [SFSTRY0098918] - how it works CTA card for AIA IND market handled with extension point 

## [v0.0.55] - 2024-11-26

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code

* [DFCT0038212] - refactor code  Profile - <status> + txtvitalityoverallstatus_001 

## [v0.0.53] - 2024-11-08

* [SFSTRY0096758] - wrap routes with VGInheritedTopLevelWidget 

## [v0.0.52] - 2024-11-07

* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value … (#59)
* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value is not consistent across the app]

* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value is not consistent across the app]

* [SFSTRY0094088][DFCT0039173][Thousand separator on the Numeric value is not consistent across the app] 

## [v0.0.51] - 2024-11-01

* [SFSTRY0087988] - added image height and width for cdn image 

## [v0.0.50] - 2024-10-29

* [SFSTRY0087988] - fixed the cdn image issue

* [SFSTRY0087988] - fixed the cdn image issue

* [SFSTRY0087988] - fixed the cdn image issue 

## [v0.0.49] - 2024-10-29

* [SFSTRY0087988] - fixed the cdn image issue 
