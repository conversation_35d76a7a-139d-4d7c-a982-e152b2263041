name: test_app
description: "Test app for status journey Widget"
publish_to: 'none'
version: 0.0.78
environment:
  sdk: ">=3.3.4 <4.0.0"
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  flutter_modular: ^5.0.3
  package_info_plus: ^4.2.0
  device_info_plus: ^9.1.0
  collection: ^1.17.1
  http: 1.1.0
  status_journey:
    path: ../status_journey
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: master
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.323
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.150
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.217
  manage_vdp_integration_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-vdp-integration-micro-service-sdk-flutter.git
      path: manage_vdp_integration_micro_service_sdk
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.128
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
dependency_overrides:
  app_settings: 5.1.1
  injectable_generator: ^2.4.1
  intl: ^0.18.1
  flutter_secure_storage: ^8.0.0
  http: 1.1.0
  dynatrace_flutter_plugin: 3.307.1
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.11
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.11
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.71
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  platform_core:
    git:
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      path: platform_core
      ref: 0.0.150
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.543
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.136
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-asset-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.50
  manage_vitality_products_micro_service_sdk:
    git:
      path: manage_vitality_products_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-vitality-products-micro-service-sdk-flutter.git
      ref: main
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.457
  help_faq:
    git:
      url: **************:discovery-ltd/v1-help-faq-flutter.git
      path: help_faq
      ref: 0.0.217
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.323
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.129
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.56
  v1_feeds_rendering_engine:
    git:
      url: **************:discovery-ltd/v1-feeds-rendering-engine-flutter.git
      path: v1_feeds_rendering_engine
      ref: 0.0.132
  v1_feeds_card_builders:
    git:
      url: **************:discovery-ltd/v1-feeds-card-builders-flutter.git
      path: v1_feeds_card_builders
      ref: 0.0.128
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.42
  notifications_flutter_plugin:
    git:
      url: **************:discovery-ltd/v1-notifications-flutter-plugin.git
      path: notifications_flutter_plugin
      ref: 0.0.16
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.83
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      path: vg_framework_contracts
      ref: 0.0.17
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0
  build_runner: 2.4.13
  analyzer: ^5.13.0
  injectable_generator: ^2.4.1
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
