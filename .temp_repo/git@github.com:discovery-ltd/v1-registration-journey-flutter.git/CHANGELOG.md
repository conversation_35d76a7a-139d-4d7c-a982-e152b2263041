# Change Log

> All notable changes to this project will be documented in this file. <br/>
> This project adheres to [Semantic Versioning](http://semver.org/).

New Changes:

## [v0.0.353] - 2025-08-21

* No notable changes on this release version. 

## [v0.0.352] - 2025-08-20

* No notable changes on this release version. 

## [v0.0.351] - 2025-08-19

* No notable changes on this release version. 

## [v0.0.350] - 2025-08-18

* No notable changes on this release version. 

## [v0.0.349] - 2025-08-15

* No notable changes on this release version. 

## [v0.0.348] - 2025-08-14

* No notable changes on this release version. 

## [v0.0.347] - 2025-08-13

* No notable changes on this release version. 

## [v0.0.346] - 2025-08-12

* No notable changes on this release version. 

## [v0.0.345] - 2025-08-11

* No notable changes on this release version. 

## [v0.0.344] - 2025-08-08

* No notable changes on this release version. 

## [v0.0.343] - 2025-08-07

* No notable changes on this release version. 

## [v0.0.342] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update test setup

* [DFCT0049251][MKT][SLI]: add extension to toggle subtext display in allset_screen.dart 

## [v0.0.341] - 2025-08-06

* No notable changes on this release version. 

## [v0.0.340] - 2025-08-05

* No notable changes on this release version. 

## [v0.0.339] - 2025-08-04

* No notable changes on this release version. 

## [v0.0.338] - 2025-08-01

* [SFSTRY0115230]-fixed the accessibility issue for ios

* [SFSTRY0115230]-fixed the accessibility issue 

## [v0.0.337] - 2025-07-29

* No notable changes on this release version. 

## [v0.0.336] - 2025-07-28

* [SFSTRY0115230] - maxline changes in textfield 

## [v0.0.335] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v0.0.334] - 2025-07-28

* No notable changes on this release version. 

## [v0.0.333] - 2025-07-25

* No notable changes on this release version. 

## [v0.0.332] - 2025-07-24

* [SFSTRY0105115][Gutenberg][Registraion - termsAndConditions path update] 

## [v0.0.331] - 2025-07-24

* No notable changes on this release version. 

## [v0.0.330] - 2025-07-23

* [SFSTRY0115230]-fixed accessibility issues 

## [v0.0.329] - 2025-07-23

* No notable changes on this release version. 

## [v0.0.328] - 2025-07-18

* No notable changes on this release version. 

## [v0.0.327] - 2025-07-16

* No notable changes on this release version. 

## [v0.0.326] - 2025-07-15

* No notable changes on this release version. 

## [v0.0.325] - 2025-07-14

* [SFSTRY0113404] - Added stack trace logging for the Privacy Policy page in Dynatrace. 

## [v0.0.324] - 2025-07-14

* No notable changes on this release version. 

## [v0.0.323] - 2025-07-11

* No notable changes on this release version. 

## [v0.0.322] - 2025-07-10

* No notable changes on this release version. 

## [v0.0.321] - 2025-07-09

* No notable changes on this release version. 

## [v0.0.320] - 2025-07-08

* [DFCT0048477]-sub text updated for future dated user in all set screen 

## [v0.0.319] - 2025-07-08

* No notable changes on this release version. 

## [v0.0.318] - 2025-07-07

* No notable changes on this release version. 

## [v0.0.317] - 2025-07-04

* [DFCT0048472]-version update and test cases updated 

## [v0.0.316] - 2025-07-04

* No notable changes on this release version. 

## [v0.0.315] - 2025-07-03

* No notable changes on this release version. 

## [v0.0.314] - 2025-07-02

* No notable changes on this release version. 

## [v0.0.313] - 2025-06-30

* No notable changes on this release version. 

## [v0.0.312] - 2025-06-27

* [DFCT0047315] - Commenting the password space dialog. 

## [v0.0.311] - 2025-06-27

* No notable changes on this release version. 

## [v0.0.310] - 2025-06-26

* No notable changes on this release version. 

## [v0.0.309] - 2025-06-25

* No notable changes on this release version. 

## [v0.0.308] - 2025-06-24

* No notable changes on this release version. 

## [v0.0.307] - 2025-06-23

* No notable changes on this release version. 

## [v0.0.306] - 2025-06-20

* [DFCT0047315] - added new phrase strings.

* [DFCT0047315] - show error dialog for lead or trail spaces in password. 

## [v0.0.305] - 2025-06-20

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4 

## [v0.0.304] - 2025-06-20

* No notable changes on this release version. 

## [v0.0.303] - 2025-06-19

* No notable changes on this release version. 

## [v0.0.302] - 2025-06-18

* No notable changes on this release version. 

## [v0.0.301] - 2025-06-17

* No notable changes on this release version. 

## [v0.0.300] - 2025-06-16

* No notable changes on this release version. 

## [v0.0.299] - 2025-06-13

* No notable changes on this release version. 

## [v0.0.298] - 2025-06-12

* No notable changes on this release version. 

## [v0.0.297] - 2025-06-11

* No notable changes on this release version. 

## [v0.0.296] - 2025-06-10

* No notable changes on this release version. 

## [v0.0.295] - 2025-06-09

* No notable changes on this release version. 

## [v0.0.294] - 2025-06-06

* No notable changes on this release version. 

## [v0.0.293] - 2025-06-05

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* Revert "[DFCT0043403] - biometric unknown name"
This reverts commit bc22f991ad0803bf20d236ba324db9390facfee0.

* [DFCT0043403] - biometric unknown name 

## [v0.0.292] - 2025-06-03

* No notable changes on this release version. 

## [v0.0.291] - 2025-06-02

* No notable changes on this release version. 

## [v0.0.290] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.289] - 2025-05-29

* No notable changes on this release version. 

## [v0.0.288] - 2025-05-28

* No notable changes on this release version. 

## [v0.0.287] - 2025-05-27

* No notable changes on this release version. 

## [v0.0.286] - 2025-05-26

* No notable changes on this release version. 

## [v0.0.285] - 2025-05-23

* No notable changes on this release version. 

## [v0.0.284] - 2025-05-22

* No notable changes on this release version. 

## [v0.0.283] - 2025-05-21

* No notable changes on this release version. 

## [v0.0.282] - 2025-05-20

* No notable changes on this release version. 

## [v0.0.281] - 2025-05-19

* [SFSTRY0110201] - resolved multiple calls on enableprivacy function in step1. 

## [v0.0.280] - 2025-05-19

* No notable changes on this release version. 

## [v0.0.279] - 2025-05-16

* [SFSTRY0110201] - resolved step 3 reg screen blur view not getting removed on app foreground 

## [v0.0.278] - 2025-05-16

* No notable changes on this release version. 

## [v0.0.277] - 2025-05-15

* No notable changes on this release version. 

## [v0.0.276] - 2025-05-14

* No notable changes on this release version. 

## [v0.0.275] - 2025-05-13

* No notable changes on this release version. 

## [v0.0.274] - 2025-05-12

* No notable changes on this release version. 

## [v0.0.273] - 2025-05-09

* No notable changes on this release version. 

## [v0.0.272] - 2025-05-08

* No notable changes on this release version. 

## [v0.0.271] - 2025-05-07

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen 

## [v0.0.270] - 2025-05-07

* No notable changes on this release version. 

## [v0.0.269] - 2025-05-06

* [SFSTRY0104915] - v1-platform-core dependency update

* [SFSTRY0104915] - Privacy Policy Update for first time App Installed. 

## [v0.0.268] - 2025-05-06

* No notable changes on this release version. 

## [v0.0.267] - 2025-05-05

* No notable changes on this release version. 

## [v0.0.266] - 2025-05-02

* No notable changes on this release version. 

## [v0.0.265] - 2025-05-01

* No notable changes on this release version. 

## [v0.0.264] - 2025-04-30

* [SFSTRY0106645]-allow space in password field 

## [v0.0.263] - 2025-04-30

* [SFSTRY0105115] - TNew Changes:

## [v0.0.353] - 2025-08-21

* No notable changes on this release version. 

## [v0.0.352] - 2025-08-20

* No notable changes on this release version. 

## [v0.0.351] - 2025-08-19

* No notable changes on this release version. 

## [v0.0.350] - 2025-08-18

* No notable changes on this release version. 

## [v0.0.349] - 2025-08-15

* No notable changes on this release version. 

## [v0.0.348] - 2025-08-14

* No notable changes on this release version. 

## [v0.0.347] - 2025-08-13

* No notable changes on this release version. 

## [v0.0.346] - 2025-08-12

* No notable changes on this release version. 

## [v0.0.345] - 2025-08-11

* No notable changes on this release version. 

## [v0.0.344] - 2025-08-08

* No notable changes on this release version. 

## [v0.0.343] - 2025-08-07

* No notable changes on this release version. 

## [v0.0.342] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update test setup

* [DFCT0049251][MKT][SLI]: add extension to toggle subtext display in allset_screen.dart 

## [v0.0.341] - 2025-08-06

* No notable changes on this release version. 

## [v0.0.340] - 2025-08-05

* No notable changes on this release version. 

## [v0.0.339] - 2025-08-04

* No notable changes on this release version. 

## [v0.0.338] - 2025-08-01

* [SFSTRY0115230]-fixed the accessibility issue for ios

* [SFSTRY0115230]-fixed the accessibility issue 

## [v0.0.337] - 2025-07-29

* No notable changes on this release version. 

## [v0.0.336] - 2025-07-28

* [SFSTRY0115230] - maxline changes in textfield 

## [v0.0.335] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v0.0.334] - 2025-07-28

* No notable changes on this release version. 

## [v0.0.333] - 2025-07-25

* No notable changes on this release version. 

## [v0.0.332] - 2025-07-24

* [SFSTRY0105115][Gutenberg][Registraion - termsAndConditions path update] 

## [v0.0.331] - 2025-07-24

* No notable changes on this release version. 

## [v0.0.330] - 2025-07-23

* [SFSTRY0115230]-fixed accessibility issues 

## [v0.0.329] - 2025-07-23

* No notable changes on this release version. 

## [v0.0.328] - 2025-07-18

* No notable changes on this release version. 

## [v0.0.327] - 2025-07-16

* No notable changes on this release version. 

## [v0.0.326] - 2025-07-15

* No notable changes on this release version. 

## [v0.0.325] - 2025-07-14

* [SFSTRY0113404] - Added stack trace logging for the Privacy Policy page in Dynatrace. 

## [v0.0.324] - 2025-07-14

* No notable changes on this release version. 

## [v0.0.323] - 2025-07-11

* No notable changes on this release version. 

## [v0.0.322] - 2025-07-10

* No notable changes on this release version. 

## [v0.0.321] - 2025-07-09

* No notable changes on this release version. 

## [v0.0.320] - 2025-07-08

* [DFCT0048477]-sub text updated for future dated user in all set screen 

## [v0.0.319] - 2025-07-08

* No notable changes on this release version. 

## [v0.0.318] - 2025-07-07

* No notable changes on this release version. 

## [v0.0.317] - 2025-07-04

* [DFCT0048472]-version update and test cases updated 

## [v0.0.316] - 2025-07-04

* No notable changes on this release version. 

## [v0.0.315] - 2025-07-03

* No notable changes on this release version. 

## [v0.0.314] - 2025-07-02

* No notable changes on this release version. 

## [v0.0.313] - 2025-06-30

* No notable changes on this release version. 

## [v0.0.312] - 2025-06-27

* [DFCT0047315] - Commenting the password space dialog. 

## [v0.0.311] - 2025-06-27

* No notable changes on this release version. 

## [v0.0.310] - 2025-06-26

* No notable changes on this release version. 

## [v0.0.309] - 2025-06-25

* No notable changes on this release version. 

## [v0.0.308] - 2025-06-24

* No notable changes on this release version. 

## [v0.0.307] - 2025-06-23

* No notable changes on this release version. 

## [v0.0.306] - 2025-06-20

* [DFCT0047315] - added new phrase strings.

* [DFCT0047315] - show error dialog for lead or trail spaces in password. 

## [v0.0.305] - 2025-06-20

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4 

## [v0.0.304] - 2025-06-20

* No notable changes on this release version. 

## [v0.0.303] - 2025-06-19

* No notable changes on this release version. 

## [v0.0.302] - 2025-06-18

* No notable changes on this release version. 

## [v0.0.301] - 2025-06-17

* No notable changes on this release version. 

## [v0.0.300] - 2025-06-16

* No notable changes on this release version. 

## [v0.0.299] - 2025-06-13

* No notable changes on this release version. 

## [v0.0.298] - 2025-06-12

* No notable changes on this release version. 

## [v0.0.297] - 2025-06-11

* No notable changes on this release version. 

## [v0.0.296] - 2025-06-10

* No notable changes on this release version. 

## [v0.0.295] - 2025-06-09

* No notable changes on this release version. 

## [v0.0.294] - 2025-06-06

* No notable changes on this release version. 

## [v0.0.293] - 2025-06-05

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* Revert "[DFCT0043403] - biometric unknown name"
This reverts commit bc22f991ad0803bf20d236ba324db9390facfee0.

* [DFCT0043403] - biometric unknown name 

## [v0.0.292] - 2025-06-03

* No notable changes on this release version. 

## [v0.0.291] - 2025-06-02

* No notable changes on this release version. 

## [v0.0.290] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.289] - 2025-05-29

* No notable changes on this release version. 

## [v0.0.288] - 2025-05-28

* No notable changes on this release version. 

## [v0.0.287] - 2025-05-27

* No notable changes on this release version. 

## [v0.0.286] - 2025-05-26

* No notable changes on this release version. 

## [v0.0.285] - 2025-05-23

* No notable changes on this release version. 

## [v0.0.284] - 2025-05-22

* No notable changes on this release version. 

## [v0.0.283] - 2025-05-21

* No notable changes on this release version. 

## [v0.0.282] - 2025-05-20

* No notable changes on this release version. 

## [v0.0.281] - 2025-05-19

* [SFSTRY0110201] - resolved multiple calls on enableprivacy function in step1. 

## [v0.0.280] - 2025-05-19

* No notable changes on this release version. 

## [v0.0.279] - 2025-05-16

* [SFSTRY0110201] - resolved step 3 reg screen blur view not getting removed on app foreground 

## [v0.0.278] - 2025-05-16

* No notable changes on this release version. 

## [v0.0.277] - 2025-05-15

* No notable changes on this release version. 

## [v0.0.276] - 2025-05-14

* No notable changes on this release version. 

## [v0.0.275] - 2025-05-13

* No notable changes on this release version. 

## [v0.0.274] - 2025-05-12

* No notable changes on this release version. 

## [v0.0.273] - 2025-05-09

* No notable changes on this release version. 

## [v0.0.272] - 2025-05-08

* No notable changes on this release version. 

## [v0.0.271] - 2025-05-07

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen 

## [v0.0.270] - 2025-05-07

* No notable changes on this release version. 

## [v0.0.269] - 2025-05-06

* [SFSTRY0104915] - v1-platform-core dependency update

* [SFSTRY0104915] - Privacy Policy Update for first time App Installed. 

## [v0.0.268] - 2025-05-06

* No notable changes on this release version. 

## [v0.0.267] - 2025-05-05

* No notable changes on this release version. 

## [v0.0.266] - 2025-05-02

* No notable changes on this release version. 

## [v0.0.265] - 2025-05-01

* No notable changes on this release version. 

## [v0.0.264] - 2025-04-30

* [SFSTRY0106645]-allow space in password field C screen for JH

* [SFSTRY0105115] - TNew Changes:

## [v0.0.353] - 2025-08-21

* No notable changes on this release version. 

## [v0.0.352] - 2025-08-20

* No notable changes on this release version. 

## [v0.0.351] - 2025-08-19

* No notable changes on this release version. 

## [v0.0.350] - 2025-08-18

* No notable changes on this release version. 

## [v0.0.349] - 2025-08-15

* No notable changes on this release version. 

## [v0.0.348] - 2025-08-14

* No notable changes on this release version. 

## [v0.0.347] - 2025-08-13

* No notable changes on this release version. 

## [v0.0.346] - 2025-08-12

* No notable changes on this release version. 

## [v0.0.345] - 2025-08-11

* No notable changes on this release version. 

## [v0.0.344] - 2025-08-08

* No notable changes on this release version. 

## [v0.0.343] - 2025-08-07

* No notable changes on this release version. 

## [v0.0.342] - 2025-08-06

* [DFCT0049251][MKT][SLI]: update test setup

* [DFCT0049251][MKT][SLI]: add extension to toggle subtext display in allset_screen.dart 

## [v0.0.341] - 2025-08-06

* No notable changes on this release version. 

## [v0.0.340] - 2025-08-05

* No notable changes on this release version. 

## [v0.0.339] - 2025-08-04

* No notable changes on this release version. 

## [v0.0.338] - 2025-08-01

* [SFSTRY0115230]-fixed the accessibility issue for ios

* [SFSTRY0115230]-fixed the accessibility issue 

## [v0.0.337] - 2025-07-29

* No notable changes on this release version. 

## [v0.0.336] - 2025-07-28

* [SFSTRY0115230] - maxline changes in textfield 

## [v0.0.335] - 2025-07-28

* [SFSTRY0116510] - 200% Font Size changes 

## [v0.0.334] - 2025-07-28

* No notable changes on this release version. 

## [v0.0.333] - 2025-07-25

* No notable changes on this release version. 

## [v0.0.332] - 2025-07-24

* [SFSTRY0105115][Gutenberg][Registraion - termsAndConditions path update] 

## [v0.0.331] - 2025-07-24

* No notable changes on this release version. 

## [v0.0.330] - 2025-07-23

* [SFSTRY0115230]-fixed accessibility issues 

## [v0.0.329] - 2025-07-23

* No notable changes on this release version. 

## [v0.0.328] - 2025-07-18

* No notable changes on this release version. 

## [v0.0.327] - 2025-07-16

* No notable changes on this release version. 

## [v0.0.326] - 2025-07-15

* No notable changes on this release version. 

## [v0.0.325] - 2025-07-14

* [SFSTRY0113404] - Added stack trace logging for the Privacy Policy page in Dynatrace. 

## [v0.0.324] - 2025-07-14

* No notable changes on this release version. 

## [v0.0.323] - 2025-07-11

* No notable changes on this release version. 

## [v0.0.322] - 2025-07-10

* No notable changes on this release version. 

## [v0.0.321] - 2025-07-09

* No notable changes on this release version. 

## [v0.0.320] - 2025-07-08

* [DFCT0048477]-sub text updated for future dated user in all set screen 

## [v0.0.319] - 2025-07-08

* No notable changes on this release version. 

## [v0.0.318] - 2025-07-07

* No notable changes on this release version. 

## [v0.0.317] - 2025-07-04

* [DFCT0048472]-version update and test cases updated 

## [v0.0.316] - 2025-07-04

* No notable changes on this release version. 

## [v0.0.315] - 2025-07-03

* No notable changes on this release version. 

## [v0.0.314] - 2025-07-02

* No notable changes on this release version. 

## [v0.0.313] - 2025-06-30

* No notable changes on this release version. 

## [v0.0.312] - 2025-06-27

* [DFCT0047315] - Commenting the password space dialog. 

## [v0.0.311] - 2025-06-27

* No notable changes on this release version. 

## [v0.0.310] - 2025-06-26

* No notable changes on this release version. 

## [v0.0.309] - 2025-06-25

* No notable changes on this release version. 

## [v0.0.308] - 2025-06-24

* No notable changes on this release version. 

## [v0.0.307] - 2025-06-23

* No notable changes on this release version. 

## [v0.0.306] - 2025-06-20

* [DFCT0047315] - added new phrase strings.

* [DFCT0047315] - show error dialog for lead or trail spaces in password. 

## [v0.0.305] - 2025-06-20

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4

* [DFCT0045817] - receive email toggle state in step 4 

## [v0.0.304] - 2025-06-20

* No notable changes on this release version. 

## [v0.0.303] - 2025-06-19

* No notable changes on this release version. 

## [v0.0.302] - 2025-06-18

* No notable changes on this release version. 

## [v0.0.301] - 2025-06-17

* No notable changes on this release version. 

## [v0.0.300] - 2025-06-16

* No notable changes on this release version. 

## [v0.0.299] - 2025-06-13

* No notable changes on this release version. 

## [v0.0.298] - 2025-06-12

* No notable changes on this release version. 

## [v0.0.297] - 2025-06-11

* No notable changes on this release version. 

## [v0.0.296] - 2025-06-10

* No notable changes on this release version. 

## [v0.0.295] - 2025-06-09

* No notable changes on this release version. 

## [v0.0.294] - 2025-06-06

* No notable changes on this release version. 

## [v0.0.293] - 2025-06-05

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* [DFCT0043403] - biometric unknown name

* Revert "[DFCT0043403] - biometric unknown name"
This reverts commit bc22f991ad0803bf20d236ba324db9390facfee0.

* [DFCT0043403] - biometric unknown name 

## [v0.0.292] - 2025-06-03

* No notable changes on this release version. 

## [v0.0.291] - 2025-06-02

* No notable changes on this release version. 

## [v0.0.290] - 2025-05-30

* No notable changes on this release version. 

## [v0.0.289] - 2025-05-29

* No notable changes on this release version. 

## [v0.0.288] - 2025-05-28

* No notable changes on this release version. 

## [v0.0.287] - 2025-05-27

* No notable changes on this release version. 

## [v0.0.286] - 2025-05-26

* No notable changes on this release version. 

## [v0.0.285] - 2025-05-23

* No notable changes on this release version. 

## [v0.0.284] - 2025-05-22

* No notable changes on this release version. 

## [v0.0.283] - 2025-05-21

* No notable changes on this release version. 

## [v0.0.282] - 2025-05-20

* No notable changes on this release version. 

## [v0.0.281] - 2025-05-19

* [SFSTRY0110201] - resolved multiple calls on enableprivacy function in step1. 

## [v0.0.280] - 2025-05-19

* No notable changes on this release version. 

## [v0.0.279] - 2025-05-16

* [SFSTRY0110201] - resolved step 3 reg screen blur view not getting removed on app foreground 

## [v0.0.278] - 2025-05-16

* No notable changes on this release version. 

## [v0.0.277] - 2025-05-15

* No notable changes on this release version. 

## [v0.0.276] - 2025-05-14

* No notable changes on this release version. 

## [v0.0.275] - 2025-05-13

* No notable changes on this release version. 

## [v0.0.274] - 2025-05-12

* No notable changes on this release version. 

## [v0.0.273] - 2025-05-09

* No notable changes on this release version. 

## [v0.0.272] - 2025-05-08

* No notable changes on this release version. 

## [v0.0.271] - 2025-05-07

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen

* [SFSTRY0108364] - Added Privacy Service for RegistrationScreen 

## [v0.0.270] - 2025-05-07

* No notable changes on this release version. 

## [v0.0.269] - 2025-05-06

* [SFSTRY0104915] - v1-platform-core dependency update

* [SFSTRY0104915] - Privacy Policy Update for first time App Installed. 

## [v0.0.268] - 2025-05-06

* No notable changes on this release version. 

## [v0.0.267] - 2025-05-05

* No notable changes on this release version. 

## [v0.0.266] - 2025-05-02

* No notable changes on this release version. 

## [v0.0.265] - 2025-05-01

* No notable changes on this release version. 

## [v0.0.264] - 2025-04-30

* [SFSTRY0106645]-allow space in password field C screen for JH 

## [v0.0.262] - 2025-04-30

* No notable changes on this release version. 

## [v0.0.261] - 2025-04-29

* No notable changes on this release version. 

## [v0.0.260] - 2025-04-28

* No notable changes on this release version. 

## [v0.0.259] - 2025-04-25

* No notable changes on this release version. 

## [v0.0.258] - 2025-04-24

* No notable changes on this release version. 

## [v0.0.257] - 2025-04-23

* No notable changes on this release version. 

## [v0.0.256] - 2025-04-22

* No notable changes on this release version. 

## [v0.0.255] - 2025-04-21

* No notable changes on this release version. 

## [v0.0.254] - 2025-04-18

* No notable changes on this release version. 

## [v0.0.253] - 2025-04-17

* No notable changes on this release version. 

## [v0.0.252] - 2025-04-16

* No notable changes on this release version. 

## [v0.0.251] - 2025-04-15

* No notable changes on this release version. 

## [v0.0.250] - 2025-04-14

* No notable changes on this release version. 

## [v0.0.249] - 2025-04-11

* No notable changes on this release version. 

## [v0.0.248] - 2025-04-10

* No notable changes on this release version. 

## [v0.0.247] - 2025-04-09

* No notable changes on this release version. 

## [v0.0.246] - 2025-04-08

* No notable changes on this release version. 

## [v0.0.245] - 2025-04-07

* No notable changes on this release version. 

## [v0.0.244] - 2025-04-04

* No notable changes on this release version. 

## [v0.0.243] - 2025-04-03

* No notable changes on this release version. 

## [v0.0.242] - 2025-04-02

* No notable changes on this release version. 

## [v0.0.241] - 2025-04-01

* No notable changes on this release version. 

## [v0.0.240] - 2025-03-31

* No notable changes on this release version. 

## [v0.0.239] - 2025-03-28

* No notable changes on this release version. 

## [v0.0.238] - 2025-03-27

* No notable changes on this release version. 

## [v0.0.237] - 2025-03-26

* No notable changes on this release version. 

## [v0.0.236] - 2025-03-25

* No notable changes on this release version. 

## [v0.0.235] - 2025-03-24

* No notable changes on this release version. 

## [v0.0.234] - 2025-03-21

* No notable changes on this release version. 

## [v0.0.233] - 2025-03-20

* [SFSTRY0105316] - Publishing the journey flutter packages through github actions 

## [v0.0.232] - 2025-03-20

* No notable changes on this release version. 

## [v0.0.231] - 2025-03-19

* No notable changes on this release version. 

## [v0.0.230] - 2025-03-18

* No notable changes on this release version. 

## [v0.0.229] - 2025-03-17

* No notable changes on this release version. 

## [v0.0.228] - 2025-03-14

* No notable changes on this release version. 

## [v0.0.227] - 2025-03-13

* [DFCT0044836] - updating dynatrace version.

* [DFCT0044836] - fixed login issue in test app and fixing the test cases after updating the inappwebview version. 

## [v0.0.226] - 2025-03-11

* [DFCT0044760]-tnc issue fixed 

## [v0.0.225] - 2025-03-11

* No notable changes on this release version. 

## [v0.0.224] - 2025-03-10

* No notable changes on this release version. 

## [v0.0.223] - 2025-03-07

* No notable changes on this release version. 

## [v0.0.222] - 2025-03-06

* [DFCT0044677]-duplicate code removed

* [DFCT0044677]-cdn version update 

## [v0.0.221] - 2025-03-06

* No notable changes on this release version. 

## [v0.0.220] - 2025-03-05

* No notable changes on this release version. 

## [v0.0.219] - 2025-03-04

* [DFCT0044122]-fix the test cases

* [DFCT0044122] - Implement url white list for privacy policy 

## [v0.0.218] - 2025-03-04

* [DFCT0039883] - add inverted 1/0 appConfig implementation 

## [v0.0.217] - 2025-03-04

* No notable changes on this release version. 

## [v0.0.216] - 2025-03-03

* No notable changes on this release version. 

## [v0.0.215] - 2025-02-28

* No notable changes on this release version. 

## [v0.0.214] - 2025-02-27

* [DFCT0044318] - flutter sdk environment version update  3.3.4 

## [v0.0.213] - 2025-02-25

* No notable changes on this release version. 

## [v0.0.212] - 2025-02-24

* No notable changes on this release version. 

## [v0.0.211] - 2025-02-21

* [DFCT0039883] - save email preference for both on and off switch values 

## [v0.0.210] - 2025-02-21

* No notable changes on this release version. 

## [v0.0.209] - 2025-02-20

* [DFCT0043562]-change env prefix for email 

## [v0.0.208] - 2025-02-20

* [DFCT0043448] Refactor Session Manager Tokens and LoginResponse Setter

* [DFCT0043448] Refactor Session Manager Tokens and LoginResponse Setter

* [DFCT0043448] Refactor Session Manager Tokens and LoginResponse Setter

* [DFCT0043448] Fix missing implementations in test_app
Change-Id: I535d1bb9c7c2d798aa8b32eb554cb3088254f749

* [DFCT0043448] Refactor Session Manager Tokens and LoginResponse Setter 

## [v0.0.207] - 2025-02-20

* [DFCT0039883] - modify email preferenceUpdate to use app.config 'zeroOneEmailPref' boolean as basis for using "0"/"1" or "true"/"false" params 

## [v0.0.206] - 2025-02-20

* No notable changes on this release version. 

## [v0.0.205] - 2025-02-19

* No notable changes on this release version. 

## [v0.0.204] - 2025-02-18

* No notable changes on this release version. 

## [v0.0.203] - 2025-02-17

* [SFSTRY0103216]-update user pref value to modular box 

## [v0.0.202] - 2025-02-17

* [DFCT0038024] - removing hardcoded mdev and added env config to the module level test app.

* [DFCT0038024] - updating module versions to fix the module level build. 

## [v0.0.201] - 2025-02-17

* No notable changes on this release version. 

## [v0.0.200] - 2025-02-14

* [DFCT0043562]-env prefix validation added 

## [v0.0.199] - 2025-02-14

* No notable changes on this release version. 

## [v0.0.198] - 2025-02-13

* No notable changes on this release version. 

## [v0.0.197] - 2025-02-12

* No notable changes on this release version. 

## [v0.0.196] - 2025-02-11

* No notable changes on this release version. 

## [v0.0.195] - 2025-02-10

* No notable changes on this release version. 

## [v0.0.194] - 2025-02-07

* No notable changes on this release version. 

## [v0.0.193] - 2025-02-07

* No notable changes on this release version. 

## [v0.0.192] - 2025-02-06

* [SFSTRY0101375] - env prefix update to modular box 

## [v0.0.191] - 2025-02-06

* No notable changes on this release version. 

## [v0.0.190] - 2025-02-05

* No notable changes on this release version. 

## [v0.0.189] - 2025-02-04

* No notable changes on this release version. 

## [v0.0.188] - 2025-02-03

* No notable changes on this release version. 

## [v0.0.187] - 2025-01-31

* No notable changes on this release version. 

## [v0.0.186] - 2025-01-30

* No notable changes on this release version. 

## [v0.0.185] - 2025-01-29

* No notable changes on this release version. 

## [v0.0.184] - 2025-01-28

* [DFCT0042876] Some hyperlinks in GTB redirect
fix key for webURLExternal

* [DFCT0042876] Some hyperlinks in GTB redirect
support config for external browser 

## [v0.0.183] - 2025-01-28

* [SFSTRY0094553] - dynatrace log exception update 

## [v0.0.182] - 2025-01-28

* No notable changes on this release version. 

## [v0.0.181] - 2025-01-27

* No notable changes on this release version. 

## [v0.0.180] - 2025-01-24

* No notable changes on this release version. 

## [v0.0.179] - 2025-01-23

* No notable changes on this release version. 

## [v0.0.178] - 2025-01-22

* No notable changes on this release version. 

## [v0.0.177] - 2025-01-21

* [SFSTRY0094553] - fix for user preference provider 

## [v0.0.176] - 2025-01-21

* [SFSTRY0091585]-Added avoid_print in analysis_options.yaml 

## [v0.0.175] - 2025-01-21

* No notable changes on this release version. 

## [v0.0.174] - 2025-01-20

* [SFSTRY0094553] - removed modular box for env prefix 

## [v0.0.173] - 2025-01-20

* [SFSTRY0094553] - added log exception

* [SFSTRY0094553] - added log exception 

## [v0.0.172] - 2025-01-20

* [SFSTRY0099596] - pod file update

* [SFSTRY0099596] - yaml file update

* [SFSTRY0099596] - mock file update

* [SFSTRY0099596] - yaml file update

* [SFSTRY0099596] - yaml file update

* [SFSTRY0099596] - fixed failed test case

* [SFSTRY0099596] - added modular box preferences

* [SFSTRY0099596] - added modular box preferences

* [SFSTRY0099596] - modularbox usage navigate_registration_step4

* [SFSTRY0099596] - modularbox test cases changes

* [SFSTRY0099596] - added modular box preferences 

## [v0.0.171] - 2025-01-20

* No notable changes on this release version. 

## [v0.0.170] - 2025-01-17

* No notable changes on this release version. 

## [v0.0.169] - 2025-01-16

* No notable changes on this release version. 

## [v0.0.168] - 2025-01-15

* [SFSTRY0099596] - change to prefernce to user prefernce provider

* [SFSTRY0099596] - change to prefernce to user prefernce provider

* [SFSTRY0099596] - change to prefernce to user prefernce provider 

## [v0.0.167] - 2025-01-15

* No notable changes on this release version. 

## [v0.0.166] - 2025-01-13

* No notable changes on this release version. 

## [v0.0.165] - 2025-01-10

* No notable changes on this release version. 

## [v0.0.164] - 2025-01-09

* No notable changes on this release version. 

## [v0.0.163] - 2025-01-08

* [SFSTRY0094556] - change to prefernce to session manager

* [SFSTRY0094556] - change to prefernce to session manager

* [SFSTRY0094556] - change to prefernce to session manager

* [SFSTRY0094556] - change to prefernce to session manager

* [SFSTRY0094556] - update test cases

* [SFSTRY0094556] - change to prefernce to session manager 

## [v0.0.162] - 2025-01-08

* No notable changes on this release version. 

## [v0.0.161] - 2025-01-07

* [SFSTRY0095714] - Added dynatrace Event 

## [v0.0.160] - 2025-01-06

* [SFSTRY0096340] - update mock classes

* [SFSTRY0096340] - Update mock classes

* [SFSTRY0096340] - Update extension name and use it for custom header 1st section 

## [v0.0.159] - 2025-01-06

* No notable changes on this release version. 

## [v0.0.158] - 2025-01-03

* No notable changes on this release version. 

## [v0.0.157] - 2025-01-02

* No notable changes on this release version. 

## [v0.0.156] - 2025-01-01

* No notable changes on this release version. 

## [v0.0.155] - 2024-12-31

* [DFCT0042441] - reference common-lib email validaion regex 

## [v0.0.154] - 2024-12-31

* No notable changes on this release version. 

## [v0.0.153] - 2024-12-30

* [SFSTRY0094554] - removed pref usage for tenantID, accessToken, partyId and loginResponse and fetching it from session manager 

## [v0.0.152] - 2024-12-30

* No notable changes on this release version. 

## [v0.0.151] - 2024-12-27

* No notable changes on this release version. 

## [v0.0.150] - 2024-12-26

* No notable changes on this release version. 

## [v0.0.149] - 2024-12-25

* No notable changes on this release version. 

## [v0.0.148] - 2024-12-24

* No notable changes on this release version. 

## [v0.0.147] - 2024-12-23

* No notable changes on this release version. 

## [v0.0.146] - 2024-12-20

* [DFCT0041834] - biometrics translations

* [DFCT0041834] - biometrics translations 

## [v0.0.145] - 2024-12-20

* [SFSTRY0095972] - change GoRoute name to RegistrationRoutes.privacyPolicyScreen 

## [v0.0.144] - 2024-12-20

* No notable changes on this release version. 

## [v0.0.143] - 2024-12-19

* [SFSTRY0099278] - update email validation regex according to RFC 5322 

## [v0.0.142] - 2024-12-19

* No notable changes on this release version. 

## [v0.0.141] - 2024-12-18

* No notable changes on this release version. 

## [v0.0.140] - 2024-12-17

* No notable changes on this release version. 

## [v0.0.139] - 2024-12-16

* No notable changes on this release version. 

## [v0.0.138] - 2024-12-13

* [SFSTRY0096340] - Add unit testing for extension

* [SFSTRY0096340] - Add extension for email switch updates 

## [v0.0.137] - 2024-11-29

* [SFSTRY0094549]-test case issue fixed

* [SFSTRY0094549]-delay added tnc service 

## [v0.0.136] - 2024-11-29

* No notable changes on this release version. 

## [v0.0.135] - 2024-11-28

* [DFCT0041318] - Fix for accepting email address with single character in registration 

## [v0.0.134] - 2024-11-28

* No notable changes on this release version. 

## [v0.0.133] - 2024-11-27

* No notable changes on this release version. 

## [v0.0.132] - 2024-11-26

* No notable changes on this release version. 

## [v0.0.131] - 2024-11-25

* No notable changes on this release version. 

## [v0.0.130] - 2024-11-22

* No notable changes on this release version. 

## [v0.0.129] - 2024-11-21

* No notable changes on this release version. 

## [v0.0.128] - 2024-11-19

* No notable changes on this release version. 

## [v0.0.127] - 2024-11-18

* No notable changes on this release version. 

## [v0.0.126] - 2024-11-14

* No notable changes on this release version. 

## [v0.0.125] - 2024-11-13

* [SFSTRY0096534] -  validate post handling

* [SFSTRY0096534] -  handle env-not-found at saveEnvironment; and link to email field validation 

## [v0.0.124] - 2024-11-13

* No notable changes on this release version. 

## [v0.0.123] - 2024-11-12

* No notable changes on this release version. 

## [v0.0.122] - 2024-11-11

* [DFCT0040291] - clear session data after log out.

* [DFCT0040291] - clear session data after log out.

* [DFCT0040291] - clear session data after log out. 

## [v0.0.121] - 2024-11-11

* [SFSTRY0096536] - analytics_manager updated

* [SFSTRY0096536] - ios building fixes

* [SFSTRY0096536] - wrap routes with VGInheritedTopLevelWidget 

## [v0.0.120] - 2024-11-07

* [SFFEAT0015068][DevOps][Modules][Alerting][Monitoring] Setup alerting on scheduled builds and send notifications to DT in case of unsuccessful builds.

* [SFFEAT0015068][DevOps][Modules][Alerting][Monitoring] Setup alerting on scheduled builds and send notifications to DT in case of unsuccessful builds.

* [SFFEAT0015068][DevOps][Modules][Alerting][Monitoring] Setup alerting on scheduled builds and send notifications to DT in case of unsuccessful builds. 

## [v0.0.119] - 2024-10-28

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines.

* [SFSTSK0021462]Migrate all the Modules from M1 to M2 machines. 

## [v0.0.64] - 2024-03-13

* [SFSTRY0073337] [Engineering - GTB]Add a readme/changelog file and update it with latest commits during every new release tag. 1st test. 
