name: test_app
description: "Test app for Registration Journey implementation"
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
version: 0.0.353
dependencies:
  flutter:
    sdk: flutter
  registration_journey:
    path: ../registration_journey
  device_info_plus: ^9.1.0
  gutenberg_startup_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-startup-package-flutter.git
      path: gutenberg_startup_package
      ref: master
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.301
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.34
dependency_overrides:
  dynatrace_flutter_plugin: 3.307.1
  # 3rd party dependencies-----------------
  flutter_inappwebview: 6.1.0
  flutter_secure_storage: ^9.0.0
  http: 1.1.0
  intl: ^0.18.1
  # project dependencies-------------------
  preferences_migration:
    git:
      url: **************:discovery-ltd/v1-preferences-migration-flutter.git
      path: preferences_migration
      ref: 0.0.44
  http_manager:
    git:
      url: **************:discovery-ltd/v1-http-manager-flutter.git
      path: http_manager
      ref: 0.0.18
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.72
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.145
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.152
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.127
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.466
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.565
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.56
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  http_util:
    git:
      url: **************:discovery-ltd/v1-http-util-flutter.git
      path: http_util
      ref: 0.0.16
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.83
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.17
      path: vg_framework_contracts
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.8
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.52
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  login_journey:
    git:
      url: **************:discovery-ltd/v1-login-journey-flutter.git
      path: login_journey
      ref: 0.0.301
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.34
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.11
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.34
dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.6
  injectable_generator: ^2.4.1
  hive_flutter: ^1.1.0
  path_provider: ^2.0.14
  path_provider_platform_interface: ^2.0.0
  plugin_platform_interface: ^2.0.0
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
