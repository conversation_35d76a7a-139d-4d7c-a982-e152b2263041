name: "registration_journey"
description: "This project is used for Registration Journey in flutter"
version: 0.0.353
homepage: https://github.com/discovery-ltd/v1-registration-journey-flutter
publish_to: none
environment:
  sdk: ">=3.3.4 <4.0.0"
  flutter: ">=2.17.0"
dependencies:
  # sdk dependencies-----------------------
  flutter:
    sdk: flutter
  # 3rd party dependencies-----------------
  cookie_jar: ^4.0.8
  equatable: ^2.0.5
  flutter_bloc: ^8.1.3
  http: 1.1.0
  package_info_plus: ^4.0.2
  url_launcher: ^6.1.11
  injectable: ^2.3.1
  intl: ^0.18.1
  hive_flutter: ^1.1.0
  path_provider: ^2.0.14
  path_provider_platform_interface: ^2.0.0
  plugin_platform_interface: ^2.0.0
  # project dependencies-------------------
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.72
  gutenberg_shared_package:
    git:
      path: gutenberg_shared_package
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      ref: 0.0.85
  manage_content_micro_service_sdk:
    git:
      path: manage_content_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-content-micro-service-sdk-flutter.git
      ref: main
  manage_events_micro_service_sdk:
    git:
      path: manage_events_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-events-micro-service-sdk-flutter.git
      ref: main
  manage_party_information_micro_service_sdk:
    git:
      url: **************:discovery-ltd/v1-manage-party-information-micro-service-sdk-flutter.git
      path: manage_party_information_micro_service_sdk
      ref: main
  manage_user_micro_service_sdk:
    git:
      path: manage_user_micro_service_sdk
      url: **************:discovery-ltd/v1-manage-user-micro-service-sdk-flutter.git
      ref: main
  party_party_information_services_micro_service_sdk:
    git:
      path: party_party_information_services_micro_service_sdk
      url: **************:discovery-ltd/v1-party-party-information-services-micro-service-sdk-flutter.git
      ref: main
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.152
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
dependency_overrides:
  # 3rd party dependencies-----------------
  flutter_secure_storage: ^9.0.0
  http: 1.1.0
  intl: ^0.18.1
  # project dependencies-------------------
  http_manager:
    git:
      url: **************:discovery-ltd/v1-http-manager-flutter.git
      path: http_manager
      ref: 0.0.18
  authentication:
    git:
      url: **************:discovery-ltd/v1-authentication-plugin.git
      path: authentication
      ref: 0.0.72
  common_lib:
    git:
      url: **************:discovery-ltd/v1-commons-lib-flutter.git
      path: common_lib
      ref: 0.0.145
  framework_contracts_flutter:
    git:
      url: **************:discovery-ltd/v1-framework-contracts-flutter.git
      path: framework_contracts_flutter
      ref: 0.0.251
  platform_core:
    git:
      path: platform_core
      url: **************:discovery-ltd/v1-platform-core-flutter.git
      ref: 0.0.152
  resource_manager:
    git:
      url: **************:discovery-ltd/v1-resource-manager-flutter.git
      path: resource_manager
      ref: 0.0.127
  ui_lib:
    git:
      url: **************:discovery-ltd/v1-ui-lib-flutter.git
      path: ui_lib
      ref: 0.0.565
  session_manager:
    git:
      url: **************:discovery-ltd/v1-session-manager-flutter.git
      path: session_manager
      ref: 0.0.56
  gutenberg_shared_package:
    git:
      url: **************:discovery-ltd/v1-gutenberg-shared-package-flutter.git
      path: gutenberg_shared_package
      ref: 0.0.85
  gateway_health_sdk:
    git:
      url: **************:discovery-ltd/v1-gateway-health-sdk-plugin.git
      path: gateway_health_sdk
      ref: 0.0.83
  vg_framework_contracts:
    git:
      url: **************:discovery-ltd/vg-gateway-health-sdk-framework-contracts-flutter.git
      ref: 0.0.17
      path: vg_framework_contracts
  v1_modular_app_flutter:
    git:
      url: **************:discovery-ltd/v1-modular-app-flutter.git
      path: modular_app
      ref: 0.0.16
  user_preferences:
    git:
      url: **************:discovery-ltd/v1-user-preferences-flutter.git
      path: user_preferences
      ref: 0.0.12
  storage_management:
    git:
      url: **************:discovery-ltd/v1-storage-cache-management-flutter.git
      path: storage_management
      version: 0.0.9
  key_management:
    git:
      url: **************:discovery-ltd/v1-key-management-flutter.git
      path: key_management
      ref: 0.0.8
  image_asset_provider:
    git:
      url: **************:discovery-ltd/v1-image-provider-flutter.git
      path: image_asset_provider
      ref: 0.0.52
  v1_ui_event_tracker:
    git:
      url: **************:discovery-ltd/v1-ui-event-tracker-flutter.git
      path: v1_ui_event_tracker
      ref: 0.0.29
  v1_journey_commons_flutter:
    git:
      url: **************:discovery-ltd/v1-journey-commons-flutter.git
      path: v1_journey_commons_flutter
      ref: 0.0.34
  resource_bundle_manager:
    git:
      url: **************:discovery-ltd/v1-resource-bundle-manager-flutter.git
      path: resource_bundle_manager
      ref: 0.0.466
  logger_management:
    git:
      url: **************:discovery-ltd/v1-logger-flutter.git
      path: logger_management
      version: 0.0.11
  analytics_manager:
    git:
      url: **************:discovery-ltd/v1-analytics-manager-flutter.git
      path: analytics_manager
      ref: 1.0.29
  application_configuration:
    git:
      url: **************:discovery-ltd/v1-application-configuration-flutter.git
      path: application_configuration
      ref: 0.0.16
  content_manager:
    git:
      url: **************:discovery-ltd/v1-content-manager-flutter.git
      path: content_manager
      ref: 0.0.34
dev_dependencies:
  flutter_lints: ^2.0.2
  flutter_test:
    sdk: flutter
  bloc_test: ^9.1.4
  build_runner: ^2.4.6
  connectivity_plus_platform_interface: ^1.2.4
  injectable_generator: ^2.4.1
  mockito: ^5.4.2
  shared_preferences_platform_interface: ^2.2.0
flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/config/
