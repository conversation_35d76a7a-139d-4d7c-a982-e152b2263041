# Flutter Commit Log Aggregator

This script scans the pubspec in a Flutter project to get all changes for its dependencies based on a given release tag range and compiles the list of commits in a commit log. It also extracts story/defect numbers from commit messages and fetches their descriptions from ServiceNow.

## Features

- Analyzes dependency changes between Flutter app versions
- Aggregates commit messages from all changed dependencies
- Extracts and lists unique story/defect numbers from commit messages
- Fetches story descriptions from ServiceNow API (optional)
- Generates comprehensive release notes

## Flow Diagram

![image](https://github.com/p01pienaara/commit-message-history/assets/80837554/40704f19-4d3f-4ba7-bae6-06a3f3022aa8)

## Usage

For specific version diff run:
```bash
python main.py --old <old_version> --new <new_version>
```

For latest market release diff run:
```bash
python main.py --repo <repo_url>
```

For tenant-specific releases run:
```bash
python main.py --tenant <tenant_number>
```

## Requirements

- Python 3.11
- Required Python packages: `GitPython`, `requests`, `python-dotenv`

## Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# GitHub Configuration (Required)
GITHUB_USERNAME=your_github_username
GITHUB_PAT=your_github_personal_access_token

# ServiceNow Configuration (Optional - for story descriptions)
SERVICENOW_USERNAME=your_servicenow_username
SERVICENOW_PASSWORD=your_servicenow_password
```

### GitHub Setup

1. Go to GitHub Settings > Developer settings > Personal access tokens > Tokens (classic)
2. Click "Generate new token (classic)"
3. Give it a descriptive name like "V1 Commit Log Flutter Tool"
4. Select the following scopes:
   - `repo` (Full control of private repositories)
   - `read:org` (Read org and team membership, read org projects)
5. Click "Generate token"
6. Copy the token and add it to your `.env` file

### ServiceNow Setup (Optional)

If you want to fetch story descriptions from ServiceNow:

1. Obtain your ServiceNow credentials
2. Add `SERVICENOW_USERNAME` and `SERVICENOW_PASSWORD` to your `.env` file
3. The tool will automatically fetch story descriptions when available

**Note:** If ServiceNow credentials are not provided or the API call fails, the tool will still generate the commit log but will only show story numbers without descriptions. An error note will be added at the bottom of the log explaining why descriptions couldn't be fetched.

## Output

The tool generates commit logs with the following sections:

1. **Date** - When the log was generated
2. **Affected Modules** - List of modules that changed with version ranges
3. **Stories/Defects Addressed** - Unique story/defect numbers with descriptions (if available)
4. **Detailed Commits** - Full commit messages grouped by module

Example output:
```
08-08-2025

Affected Modules:
-------------
* gutenberg_central_app (3.0.1855 - 3.0.1858)
* profile_and_settings (0.0.214 - 0.0.215)

Stories/Defects Addressed:
---------------
SFSTRY0116897: Profile&Settings - Accessibility Fixes for All Journey Components
SFSTRY0117247: Updated local variable for resource bundle
SFSTRY0117758: Apply filter functionality is failing

--- Commits for gutenberg_central_app (3.0.1855 - 3.0.1858) ---
[SFSTRY0117247] - Updated local variable[SFSTRY0113984] update resource bundle
...
```

## GUI Mode

You can also run the tool with a graphical interface:

```bash
python gui.py
```
