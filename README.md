# Flutter Commit Log Aggregator

This script scans the pubspec in a Flutter project to get all changes for its dependencies based on a given release tag range and compiles the list of commits in a commit log. It also extracts story/defect numbers from commit messages and fetches their descriptions from ServiceNow.

## Features

- Analyzes dependency changes between Flutter app versions
- Aggregates commit messages from all changed dependencies
- Extracts and lists unique story/defect numbers from commit messages
- Differentiates between stories (SFSTRY...) and defects (DFCT...)
- Fetches descriptions from ServiceNow API for both stories and defects (optional)
- Generates comprehensive release notes

## Flow Diagram

![image](https://github.com/p01pienaara/commit-message-history/assets/80837554/40704f19-4d3f-4ba7-bae6-06a3f3022aa8)

## Usage

For specific version diff run:
```bash
python main.py --old <old_version> --new <new_version>
```

For latest market release diff run:
```bash
python main.py --repo <repo_url>
```

For tenant-specific releases run:
```bash
python main.py --tenant <tenant_number>
```

## Requirements

- Python 3.11
- Required Python packages: `GitPython`, `requests`, `python-dotenv`

## Configuration

### Environment Variables

Create a `.env` file in the project root with the following variables:

```env
# ServiceNow Configuration (Optional - for story descriptions)
SERVICENOW_USERNAME=your_servicenow_username
SERVICENOW_PASSWORD=your_servicenow_password
```

### GitHub Setup

This tool uses SSH authentication to access GitHub repositories. You need to have SSH keys configured for GitHub access:

1. **Generate SSH Key** (if you don't have one):
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   ```

2. **Add SSH Key to SSH Agent**:
   ```bash
   eval "$(ssh-agent -s)"
   ssh-add ~/.ssh/id_ed25519
   ```

3. **Add SSH Key to GitHub**:
   - Copy your public key: `cat ~/.ssh/id_ed25519.pub`
   - Go to GitHub Settings > SSH and GPG keys
   - Click "New SSH key"
   - Paste your public key and save

4. **Test SSH Connection**:
   ```bash
   ssh -T **************
   ```

### ServiceNow Setup (Optional)

If you want to fetch story and defect descriptions from ServiceNow:

1. Obtain your ServiceNow credentials
2. Add `SERVICENOW_USERNAME` and `SERVICENOW_PASSWORD` to your `.env` file
3. The tool will automatically fetch descriptions from the appropriate tables:
   - **Stories** (SFSTRY...): Fetched from `/sn_safe_story` table
   - **Defects** (DFCT...): Fetched from `/rm_defect` table

**Note:** If ServiceNow credentials are not provided or API calls fail, the tool will still generate the commit log but will only show story/defect numbers without descriptions. Error notes will be added at the bottom of the log explaining why descriptions couldn't be fetched.

## Output

The tool generates commit logs with the following sections:

1. **Date** - When the log was generated
2. **Affected Modules** - List of modules that changed with version ranges
3. **Stories/Defects Addressed** - Unique story (SFSTRY...) and defect (DFCT...) numbers with descriptions (if available)
4. **Detailed Commits** - Full commit messages grouped by module

Example output:
```
08-08-2025

Affected Modules:
-------------
* gutenberg_central_app (3.0.1855 - 3.0.1858)
* profile_and_settings (0.0.214 - 0.0.215)

Stories/Defects Addressed:
---------------
DFCT0038596: Fix for login authentication issue
SFSTRY0116897: Profile&Settings - Accessibility Fixes for All Journey Components
SFSTRY0117247: Updated local variable for resource bundle
SFSTRY0117758: Apply filter functionality is failing

--- Commits for gutenberg_central_app (3.0.1855 - 3.0.1858) ---
[SFSTRY0117247] - Updated local variable[SFSTRY0113984] update resource bundle
...
```

## GUI Mode

You can also run the tool with a graphical interface:

```bash
python gui.py
```
