import os
from git import Repo, GitCommandError

def update_repo(repo_url):
    """
    Clones the repository if not already cloned, ensures the working tree is clean by discarding any changes,
    and pulls the latest changes if already cloned.
    
    :param repo_url: URL of the repository to clone/pull
    :param repo_dir: Directory where the repository should be cloned
    """
    temp_dir = f".temp_repo/{repo_url}"
    try:
        # Check if the repo directory exists
        if not os.path.exists(temp_dir):
            print(f"Cloning repository from {repo_url} to {temp_dir}")
            Repo.clone_from(repo_url, temp_dir)
        else:
            print(f"Repository already exists at {temp_dir}. Checking status...")

            # Open the existing repository
            repo = Repo(temp_dir)

            # Check if the working tree is clean
            if repo.is_dirty(untracked_files=True):
                print("Working tree is not clean. Resetting changes...")
                repo.git.reset('--hard')
                repo.git.clean('-fd')

            # Pull the latest changes from the remote repository
            print("Pulling the latest changes...")
            origin = repo.remotes.origin
            origin.pull()

    except GitCommandError as e:
        print(f"An error occurred while updating the repository: {e}")

