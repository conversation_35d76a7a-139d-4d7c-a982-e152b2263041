
import os
import subprocess
from git import Repo
import json
import datetime
import re
import requests
import base64
from dotenv import load_dotenv
import manageRepo

# Load environment variables
load_dotenv()

def get_commit_messages_between_releases(repo_url, newer_version, older_version=None):
    """Gets all commit messages between two releases in a Git repository.

    Args:
        repo_path: The path to the local Git repository.
        older_version: The tag of the older release.
        newer_version: The tag of the newer release.

    Returns:
        A list of commit messages.
    """

    manageRepo.update_repo(repo_url)
    repo = Repo(f".temp_repo/{repo_url}")
    commit_messages = []
    if older_version is not None:

        # Ensure that release_tag1 is older than release_tag2 (if necessary):
        base = repo.merge_base(older_version, newer_version)  # Find common ancestor
        if repo.is_ancestor(newer_version, base):  # release_tag2 is newer
            older_version, newer_version = newer_version, older_version

        commit_messages = repo.iter_commits(f"{older_version}..{newer_version}")
    else:
        commit_messages = repo.iter_commits(f"{newer_version}")
    repo.close()
    return [commit.message for commit in commit_messages]

def write_commits_to_file(filename, all_commits):
    """Writes commit messages to a text file.

    Args:
        filename: The name of the output text file.
        all_commits: A list of commit messages, grouped by dependency and release tag.
                     (This would be an output from combining both of your previous scripts.) 
    """

    with open(filename, "w") as f:
        for commit in all_commits:
            f.write(f"{commit}")

def read_json_file(filepath):
    """Reads data from a JSON file.

    Args:
        filepath: The path to the JSON file.

    Returns:
        The parsed data (often a list or dictionary).
    """

    with open(filepath, "r") as file:
        data = json.load(file)
    return data

def remove_duplicates(input_list):
    """
    Removes duplicates from a list while preserving the order of elements.

    :param input_list: The list from which duplicates need to be removed
    :return: A new list with duplicates removed
    """
    seen = set()
    unique_list = []
    for item in input_list:
        if item not in seen:
            unique_list.append(item)
            seen.add(item)
    return unique_list

def extract_story_defect_numbers(commit_messages):
    """
    Extracts unique story/defect numbers from commit messages.

    Args:
        commit_messages: A list of commit messages

    Returns:
        A list of unique story/defect numbers found in the commit messages
    """
    story_defect_numbers = set()

    for commit in commit_messages:
        # Find all patterns like [SFSTRY0117247] at the beginning of commit messages
        # This regex looks for square brackets containing alphanumeric characters
        matches = re.findall(r'\[([A-Z0-9]+)\]', commit)
        for match in matches:
            # Only include if it looks like a story/defect number (contains both letters and numbers)
            if re.match(r'^[A-Z]+[0-9]+$', match):
                story_defect_numbers.add(match)

    # Return sorted list for consistent ordering
    return sorted(list(story_defect_numbers))

def fetch_story_descriptions(story_numbers):
    """
    Fetches story descriptions from ServiceNow API.

    Args:
        story_numbers: A list of story/defect numbers

    Returns:
        A tuple containing:
        - A dictionary mapping story numbers to their descriptions
        - An error message string if there was an issue, None if successful
    """
    if not story_numbers:
        return {}, None

    # Get credentials from environment variables
    username = os.getenv('SERVICENOW_USERNAME')
    password = os.getenv('SERVICENOW_PASSWORD')

    if not username or not password:
        error_msg = "ServiceNow credentials not found in .env file"
        print(f"Warning: {error_msg}. Skipping story description fetch.")
        return {}, error_msg

    # Prepare the API request
    base_url = "https://vitalitysupport.service-now.com/api/now/table/sn_safe_story"
    story_list = ",".join(story_numbers)

    params = {
        'sysparm_limit': len(story_numbers),
        'sysparm_query': f'numberIN{story_list}',
        'sysparm_fields': 'number,short_description'  # Only fetch the fields we need
    }

    # Create basic auth header
    credentials = base64.b64encode(f"{username}:{password}".encode()).decode()
    headers = {
        'Authorization': f'Basic {credentials}',
        'Accept': 'application/json'
    }

    try:
        print(f"Fetching descriptions for {len(story_numbers)} stories from ServiceNow...")
        response = requests.get(base_url, params=params, headers=headers, timeout=30)
        response.raise_for_status()

        data = response.json()
        story_descriptions = {}

        for story in data.get('result', []):
            number = story.get('number', '')
            description = story.get('short_description', 'No description available')
            story_descriptions[number] = description

        print(f"Successfully fetched descriptions for {len(story_descriptions)} stories.")
        return story_descriptions, None

    except requests.exceptions.Timeout:
        error_msg = "ServiceNow API request timed out"
        print(f"Error: {error_msg}")
        return {}, error_msg
    except requests.exceptions.HTTPError as e:
        error_msg = f"ServiceNow API returned HTTP error: {e.response.status_code}"
        print(f"Error: {error_msg}")
        return {}, error_msg
    except requests.exceptions.RequestException as e:
        error_msg = f"ServiceNow API request failed: {str(e)}"
        print(f"Error: {error_msg}")
        return {}, error_msg
    except Exception as e:
        error_msg = f"Unexpected error while fetching story descriptions: {str(e)}"
        print(f"Error: {error_msg}")
        return {}, error_msg

def fetch():
    filepath = "dependencies.json"
    dependencies = read_json_file(filepath)

    today = datetime.date.today()
    formatted_date = today.strftime("%d-%m-%Y")
    affected_modules = ["\nAffected Modules:\n-------------\n"]
    all_commits = [f"{formatted_date}"]
    all_commit_messages = []  # Collect all commit messages for story/defect extraction

    central_old_version = dependencies[0]["old_version"]
    central_new_version = dependencies[0]["new_version"]
    for repo in dependencies:
        print(repo)
        if "url" not in repo or "new_version" not in repo:
            print("XXX - ERROR: \n" + repo)
            continue

        path = repo["url"]
        new_version = repo["new_version"]
        old_version = None
        if "old_version" in repo:
            old_version = repo["old_version"]

        commits_by_release = get_commit_messages_between_releases(path, new_version, old_version)
        print(commits_by_release)
        repo_name = "unnamed"
        if "path" in repo:
            repo_name = repo["path"]
        elif "url" in repo:
            repo_name = repo["url"]
        print(f"--- Commits for {repo_name} ---")
        all_commits.append(f"\n--- Commits for {repo_name} ({old_version} - {new_version}) ---\n")
        affected_modules.append(f"* {repo_name} ({old_version} - {new_version})\n")
        for commit in commits_by_release:
            if "🚀" in commit or "Merge " in commit:
                continue
            print(f"{commit}")
            all_commits.append(f"{commit}")
            all_commit_messages.append(commit)  # Collect for story/defect extraction

    # Extract story/defect numbers from all commit messages
    story_defect_numbers = extract_story_defect_numbers(all_commit_messages)

    # Fetch story descriptions from ServiceNow
    story_descriptions, fetch_error = fetch_story_descriptions(story_defect_numbers)

    # Create Stories/Defects section
    stories_defects_section = ["\nStories/Defects Addressed:\n---------------\n"]
    if story_defect_numbers:
        for number in story_defect_numbers:
            if story_descriptions:
                description = story_descriptions.get(number, "Description not available")
                stories_defects_section.append(f"{number}: {description}\n")
            else:
                # If no descriptions were fetched, just show the number
                stories_defects_section.append(f"{number}\n")
    else:
        stories_defects_section.append("No story/defect numbers found\n")

    # Insert sections in the correct order: date, affected modules, stories/defects, then commits
    all_commits[1:1] = affected_modules
    all_commits[len(affected_modules) + 1:len(affected_modules) + 1] = stories_defects_section

    # Add error note at the bottom if story descriptions couldn't be fetched
    if fetch_error and story_defect_numbers:
        error_note = [f"\nNote: Story descriptions could not be fetched from ServiceNow.\nReason: {fetch_error}\n"]
        all_commits.extend(error_note)

    unique_list = remove_duplicates(all_commits)
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')

    # Write to file
    write_commits_to_file(f"logs/commit_log_{central_old_version}-{central_new_version}.txt", unique_list)
    write_commits_to_file(f"logs/commit_log.txt", unique_list)

    return unique_list

